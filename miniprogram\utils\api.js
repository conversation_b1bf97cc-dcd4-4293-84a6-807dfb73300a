// utils/api.js
const app = getApp()

/**
 * 封装微信请求
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token')

    wx.request({
      url: app.globalData.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data)
          } else {
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // 未授权，清除登录信息
          app.logout()
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/login/login'
            })
          }, 1500)
          reject(res)
        } else {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

/**
 * API接口定义
 */
const api = {
  // 获取首页背景图片
  getBackgroundImage() {
    return request({
      url: '/background/image',
      method: 'GET'
    })
  },

  // 员工登录
  login(username, password) {
    return request({
      url: '/auth/login',
      method: 'POST',
      data: {
        username,
        password
      }
    })
  },

  // 获取员工列表
  getEmployeeList() {
    return request({
      url: '/employees',
      method: 'GET'
    })
  },

  // 获取员工详情
  getEmployeeDetail(id) {
    return request({
      url: `/employees/${id}`,
      method: 'GET'
    })
  },

  // 获取部门列表
  getDepartmentList() {
    return request({
      url: '/departments',
      method: 'GET'
    })
  },

  // 根据部门ID获取员工列表
  getEmployeesByDepartment(departmentId) {
    return request({
      url: `/employees/department-id/${departmentId}`,
      method: 'GET'
    })
  },

  // 验证token有效性
  validateToken() {
    return request({
      url: '/auth/validate',
      method: 'GET'
    })
  },

  // 获取所有长图
  getAllLongImages() {
    return request({
      url: '/long-images',
      method: 'GET'
    })
  },

  // 根据分类获取长图
  getLongImagesByCategory(category) {
    return request({
      url: `/long-images/category/${category}`,
      method: 'GET'
    })
  },

  // 获取所有分类
  getAllCategories() {
    return request({
      url: '/long-images/categories',
      method: 'GET'
    })
  },

  // 根据ID获取部门详情
  getDepartmentById(departmentId) {
    return request({
      url: `/departments/${departmentId}`,
      method: 'GET'
    })
  },

  // 根据ID获取长图详情
  getLongImageById(id) {
    return request({
      url: `/long-images/${id}`,
      method: 'GET'
    })
  }
}

module.exports = {
  request,
  api
}