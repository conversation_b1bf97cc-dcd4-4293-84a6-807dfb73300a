package com.minmetals.service.impl;

import com.minmetals.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 文件上传服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Value("${file.upload.max-size:10485760}") // 10MB
    private long maxFileSize;

    @Value("${server.port}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Value("${file.upload.domain:gtdw.chinasteel.com.cn}")
    private String domain;

    @Override
    public String uploadBackgroundImage(MultipartFile file) {
        log.info("开始上传背景图片，文件名: {}", file.getOriginalFilename());
        String subDir = "background";
        if (!isValidImageFile(file)) {
            throw new RuntimeException("只允许上传图片文件 (png, jpg, jpeg, gif)");
        }
        return uploadFile(file, subDir);
    }

    @Override
    public String uploadAvatarImage(MultipartFile file) {
        log.info("开始上传头像图片，文件名: {}", file.getOriginalFilename());
        String subDir = "avatar";
        if (!isValidImageFile(file)) {
            throw new RuntimeException("只允许上传图片文件 (png, jpg, jpeg, gif)");
        }
        return uploadFile(file, subDir);
    }

    @Override
    public String uploadLongImage(MultipartFile file) {
        log.info("开始上传长图，文件名: {}", file.getOriginalFilename());
        String subDir = "longimage";
        if (!isValidImageFile(file)) {
            throw new RuntimeException("只允许上传图片文件 (png, jpg, jpeg, gif)");
        }
        return uploadFile(file, subDir);
    }

    private String uploadFile(MultipartFile file, String subDir) {
        if (file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }
        if (file.getSize() > maxFileSize) {
            throw new RuntimeException("文件大小超过限制: " + (maxFileSize / 1024 / 1024) + "MB");
        }

        try {
            String dateDir = new SimpleDateFormat("yyyyMMdd").format(new Date());
            Path uploadDir = Paths.get(uploadPath);
            Files.createDirectories(uploadDir);

            String uniqueFilename = generateUniqueFilename(file.getOriginalFilename());
            Path filePath = uploadDir.resolve(uniqueFilename);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            String fileUrl = String.format("https://%s%s/files/%s",
                    domain, contextPath, uniqueFilename);

            log.info("文件上传成功，URL: {}", fileUrl);
            return fileUrl;
        } catch (Exception e) {
            log.error("文件上传失败: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        log.info("尝试删除文件: {}", fileUrl);
        if (!StringUtils.hasText(fileUrl)) {
            log.warn("文件URL为空，无法删除。");
            return false;
        }

        try {
            String filePath = extractFilePathFromUrl(fileUrl);
            if (filePath == null) {
                log.warn("无法从URL中提取文件路径: {}", fileUrl);
                return false;
            }

            Path path = Paths.get(uploadPath, filePath);
            boolean deleted = Files.deleteIfExists(path);
            if (deleted) {
                log.info("文件删除成功: {}", fileUrl);
                return true;
            } else {
                log.warn("文件不存在或删除失败: {}", fileUrl);
                return false;
            }
        } catch (Exception e) {
            log.error("删除文件失败: {}", fileUrl, e);
            return false;
        }
    }

    @Override
    public boolean isValidImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }
        return contentType.startsWith("image/") && (contentType.endsWith("png") || contentType.endsWith("jpeg") || contentType.endsWith("jpg") || contentType.endsWith("gif"));
    }

    public String generateUniqueFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        return UUID.randomUUID().toString() + (extension.isEmpty() ? "" : "." + extension);
    }

    public String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf('.');
        return (dotIndex == -1) ? "" : filename.substring(dotIndex + 1);
    }

    private String extractFilePathFromUrl(String fileUrl) {
        int filesIndex = fileUrl.indexOf("/files/");
        if (filesIndex == -1) {
            return null;
        }
        return fileUrl.substring(filesIndex + "/files/".length());
    }
}
