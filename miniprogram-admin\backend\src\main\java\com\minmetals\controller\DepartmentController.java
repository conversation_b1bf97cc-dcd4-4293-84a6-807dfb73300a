package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import com.minmetals.dto.DepartmentCreateRequest;
import com.minmetals.dto.DepartmentDTO;
import com.minmetals.dto.DepartmentUpdateRequest;
import com.minmetals.service.DepartmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 部门管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/departments")
@RequiredArgsConstructor
@Tag(name = "部门管理", description = "部门管理相关接口")
public class DepartmentController {

    private final DepartmentService departmentService;

    @PostMapping
    @Operation(summary = "创建部门", description = "创建新的部门")
    public ResponseEntity<ApiResponse<DepartmentDTO>> createDepartment(
            @Valid @RequestBody DepartmentCreateRequest request) {
        try {
            DepartmentDTO department = departmentService.createDepartment(request);
            return ResponseEntity.ok(ApiResponse.success(department));
        } catch (Exception e) {
            log.error("创建部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新部门", description = "更新部门信息")
    public ResponseEntity<ApiResponse<DepartmentDTO>> updateDepartment(
            @Parameter(description = "部门ID") @PathVariable Long id,
            @Valid @RequestBody DepartmentUpdateRequest request) {
        try {
            DepartmentDTO department = departmentService.updateDepartment(id, request);
            return ResponseEntity.ok(ApiResponse.success(department));
        } catch (Exception e) {
            log.error("更新部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除部门", description = "删除部门")
    public ResponseEntity<ApiResponse<Void>> deleteDepartment(
            @Parameter(description = "部门ID") @PathVariable Long id) {
        try {
            departmentService.deleteDepartment(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("删除部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取部门", description = "根据部门ID获取部门详细信息")
    public ResponseEntity<ApiResponse<DepartmentDTO>> getDepartmentById(
            @Parameter(description = "部门ID") @PathVariable Long id) {
        try {
            DepartmentDTO department = departmentService.getDepartmentById(id);
            return ResponseEntity.ok(ApiResponse.success(department));
        } catch (Exception e) {
            log.error("获取部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping
    @Operation(summary = "获取所有部门列表", description = "获取所有部门列表")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getAllDepartments() {
        try {
            List<DepartmentDTO> departments = departmentService.getAllDepartments();
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("获取部门列表失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/tree")
    @Operation(summary = "获取部门树形结构", description = "获取部门树形结构")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getDepartmentTree() {
        try {
            List<DepartmentDTO> tree = departmentService.getDepartmentTree();
            return ResponseEntity.ok(ApiResponse.success(tree));
        } catch (Exception e) {
            log.error("获取部门树失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/level/{level}")
    @Operation(summary = "根据级别获取部门列表", description = "根据部门级别获取部门列表")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getDepartmentsByLevel(
            @Parameter(description = "部门级别") @PathVariable Integer level) {
        try {
            List<DepartmentDTO> departments = departmentService.getDepartmentsByLevel(level);
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("获取指定级别部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/parent/{parentId}")
    @Operation(summary = "根据父部门ID获取子部门列表", description = "根据父部门ID获取子部门列表")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getChildDepartments(
            @Parameter(description = "父部门ID") @PathVariable Long parentId) {
        try {
            List<DepartmentDTO> departments = departmentService.getChildDepartments(parentId);
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("获取子部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/enabled")
    @Operation(summary = "获取启用状态的部门列表", description = "获取启用状态的部门列表")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getEnabledDepartments() {
        try {
            List<DepartmentDTO> departments = departmentService.getEnabledDepartments();
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("获取启用部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/enabled/tree")
    @Operation(summary = "获取启用状态的部门树形结构", description = "获取启用状态的部门树形结构")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getEnabledDepartmentTree() {
        try {
            List<DepartmentDTO> tree = departmentService.getEnabledDepartmentTree();
            return ResponseEntity.ok(ApiResponse.success(tree));
        } catch (Exception e) {
            log.error("获取启用部门树失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/search")
    @Operation(summary = "根据名称搜索部门", description = "根据部门名称搜索部门")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> searchDepartments(
            @Parameter(description = "搜索关键词") @RequestParam String name) {
        try {
            List<DepartmentDTO> departments = departmentService.searchDepartmentsByName(name);
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("搜索部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping("/{id}/toggle-status")
    @Operation(summary = "切换部门启用状态", description = "切换部门启用状态")
    public ResponseEntity<ApiResponse<Void>> toggleDepartmentStatus(
            @Parameter(description = "部门ID") @PathVariable Long id) {
        try {
            departmentService.toggleDepartmentStatus(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("切换部门状态失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/root")
    @Operation(summary = "获取一级部门列表", description = "获取一级部门列表")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getRootDepartments() {
        try {
            List<DepartmentDTO> departments = departmentService.getRootDepartments();
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("获取一级部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/root/enabled")
    @Operation(summary = "获取启用状态的一级部门列表", description = "获取启用状态的一级部门列表")
    public ResponseEntity<ApiResponse<List<DepartmentDTO>>> getEnabledRootDepartments() {
        try {
            List<DepartmentDTO> departments = departmentService.getEnabledRootDepartments();
            return ResponseEntity.ok(ApiResponse.success(departments));
        } catch (Exception e) {
            log.error("获取启用一级部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping("/{id}/can-delete")
    @Operation(summary = "检查部门是否可以删除", description = "检查部门是否可以删除")
    public ResponseEntity<ApiResponse<Boolean>> canDeleteDepartment(
            @Parameter(description = "部门ID") @PathVariable Long id) {
        try {
            boolean canDelete = departmentService.canDeleteDepartment(id);
            return ResponseEntity.ok(ApiResponse.success(canDelete));
        } catch (Exception e) {
            log.error("检查部门删除权限失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping("/{id}/move-up")
    @Operation(summary = "上移部门", description = "将部门向上移动一位")
    public ResponseEntity<ApiResponse<Void>> moveDepartmentUp(
            @Parameter(description = "部门ID") @PathVariable Long id) {
        try {
            departmentService.moveDepartmentUp(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("上移部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping("/{id}/move-down")
    @Operation(summary = "下移部门", description = "将部门向下移动一位")
    public ResponseEntity<ApiResponse<Void>> moveDepartmentDown(
            @Parameter(description = "部门ID") @PathVariable Long id) {
        try {
            departmentService.moveDepartmentDown(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("下移部门失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
