package com.minmetals.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 员工实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "employees")
@EntityListeners(AuditingEntityListener.class)
public class Employee {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 员工姓名
     */
    @Column(name = "name", nullable = false, length = 50)
    private String name;

    /**
     * 传真号码
     */
    @Column(name = "employee_id", length = 20)
    private String employeeId;

    /**
     * 登录用户名
     */
    @Column(name = "username", nullable = false, unique = true, length = 50)
    private String username;

    /**
     * 登录密码（加密存储）
     */
    @Column(name = "password", nullable = false, length = 255)
    private String password;

    /**
     * 部门ID（关联departments表）
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 部门名称（冗余字段，便于查询显示）
     */
    @Column(name = "department", length = 100)
    private String department;

    /**
     * 职位
     */
    @Column(name = "position", length = 100)
    private String position;

    /**
     * 级别
     */
    @Column(name = "level", length = 50)
    private String level;

    /**
     * 电话号码
     */
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;

    /**
     * 地址
     */
    @Column(name = "address", length = 200)
    private String address;

    /**
     * 手机号码
     */
    @Column(name = "avatar", length = 20)
    private String avatar;

    /**
     * 邮政编码
     */
    @Column(name = "manager", length = 10)
    private String manager;

    /**
     * 工作地点
     */
    @Column(name = "work_location", length = 100)
    private String workLocation;

    /**
     * 工作描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 入职时间
     */
    @Column(name = "hire_date")
    @Temporal(TemporalType.DATE)
    private Date hireDate;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
