<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑员工' : '新增员工'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="传真" prop="employeeId">
            <el-input
              v-model="form.employeeId"
              placeholder="请输入传真号码（可选，默认为'无'）"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="移动电话" prop="avatar">
            <el-input
              v-model="form.avatar"
              placeholder="请输入移动电话号码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门" prop="departmentId">
            <DepartmentCascader
              v-model="form.departmentId"
              @change="handleDepartmentChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="position">
            <el-input
              v-model="form.position"
              placeholder="请输入职位"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="form.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input
              v-model="form.phone"
              placeholder="请输入电话号码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮政编码" prop="manager">
            <el-input
              v-model="form.manager"
              placeholder="请输入邮政编码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作地址" prop="workLocation">
            <el-input
              v-model="form.workLocation"
              placeholder="请输入工作地址"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="详细地址" prop="address">
        <el-input
          v-model="form.address"
          placeholder="请输入详细地址"
        />
      </el-form-item>

      <el-form-item v-if="!isEdit" label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="留空则使用默认密码：123456"
          show-password
        />
      </el-form-item>



      <el-form-item label="个人简介">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入个人简介"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DepartmentCascader from '@/components/DepartmentCascader.vue'
import request from '@/utils/request'

const props = defineProps({
  visible: Boolean,
  employee: Object,
  isEdit: Boolean
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const loading = ref(false)

const form = reactive({
  username: '',
  name: '',
  employeeId: '无',
  departmentId: null,
  department: '',
  position: '',
  email: '',
  phone: '',
  avatar: '',
  manager: '',
  workLocation: '',
  address: '',
  password: '',
  description: '',
  enabled: true
})



const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  employeeId: [
    { max: 20, message: '传真号码长度不能超过20个字符', trigger: 'blur' }
  ],
  departmentId: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择职位', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
    { pattern: /^[\d-]+$/, message: '请输入正确的电话号码格式', trigger: 'blur' }
  ],
  avatar: [
    { required: true, message: '请输入移动电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的移动电话号码格式', trigger: 'blur' }
  ],
  manager: [
    { pattern: /^\d{6}$/, message: '请输入正确的邮政编码格式（6位数字）', trigger: 'blur' }
  ],
  password: [
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 处理部门变化
const handleDepartmentChange = async (departmentId) => {
  if (departmentId) {
    try {
      const response = await request.get(`/departments/${departmentId}`)
      form.department = response.data.name
    } catch (error) {
      console.error('获取部门信息失败:', error)
    }
  } else {
    form.department = ''
  }
}

// 监听员工数据变化
watch(() => props.employee, (newEmployee) => {
  if (newEmployee) {
    Object.assign(form, {
      username: newEmployee.username || '',
      name: newEmployee.name || '',
      employeeId: newEmployee.employeeId || '',
      departmentId: newEmployee.departmentId || null,
      department: newEmployee.department || '',
      position: newEmployee.position || '',
      email: newEmployee.email || '',
      phone: newEmployee.phone || '',
      avatar: newEmployee.avatar || '',
      manager: newEmployee.manager || '',
      workLocation: newEmployee.workLocation || '',
      address: newEmployee.address || '',
      password: '',
      description: newEmployee.description || '',
      enabled: newEmployee.enabled !== false
    })
  } else {
    // 重置表单
    Object.assign(form, {
      username: '',
      name: '',
      employeeId: '无',
      departmentId: null,
      department: '',
      position: '',
      email: '',
      phone: '',
      avatar: '',
      manager: '',
      workLocation: '',
      address: '',
      password: '',
      description: '',
      enabled: true
    })
  }
}, { immediate: true })

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = { ...form }

    // 如果是编辑模式且没有输入新密码，则不提交密码字段
    if (props.isEdit && !submitData.password) {
      delete submitData.password
    }

    // 如果是新增模式且没有输入密码，则设置默认密码为123456
    if (!props.isEdit && !submitData.password) {
      submitData.password = '123456'
    }

    if (props.isEdit) {
      await request.put(`/employees/${props.employee.id}`, submitData)
      ElMessage.success('更新成功')
    } else {
      await request.post('/employees', submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存员工失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
