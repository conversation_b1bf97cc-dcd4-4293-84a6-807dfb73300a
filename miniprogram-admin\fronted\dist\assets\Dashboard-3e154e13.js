import{_ as k}from"./_plugin-vue_export-helper-063e7853.js";/* empty css               */import{a as w,r as h,q as I,o as $,s as B,w as a,v as p,d as e,e as s,g as d,t as c,i as m,x as R,y as D,z as E,m as x,n as g,A as v,B as f,C as b}from"./index-fbf0bc73.js";import{L as N}from"./Layout-97877336.js";const S={class:"dashboard"},V={class:"stat-card"},q={class:"stat-icon employee"},A={class:"stat-content"},L={class:"stat-number"},z={class:"stat-card"},M={class:"stat-icon background"},T={class:"stat-content"},j={class:"stat-number"},F={class:"stat-card"},G={class:"stat-icon longimage"},H={class:"stat-content"},J={class:"stat-number"},K={class:"stat-card"},O={class:"stat-icon department"},P={class:"stat-content"},Q={class:"stat-number"},U={class:"content-card"},W={__name:"Dashboard",setup(X){w();const l=h({employeeCount:0,backgroundImageCount:0,longImageCount:0,departmentCount:0}),y=async()=>{var u,t,o,n;try{const i=await p.get("/employees");l.value.employeeCount=((u=i.data)==null?void 0:u.length)||0;const r=await p.get("/departments");l.value.departmentCount=((t=r.data)==null?void 0:t.length)||0;const _=await p.get("/background-images");l.value.backgroundImageCount=((o=_.data)==null?void 0:o.length)||0;const C=await p.get("/long-images");l.value.longImageCount=((n=C.data)==null?void 0:n.length)||0}catch(i){console.error("加载统计数据失败:",i),l.value={employeeCount:25,backgroundImageCount:8,longImageCount:15,departmentCount:12}}};return I(()=>{y()}),(u,t)=>{const o=R,n=D,i=E,r=x;return $(),B(N,null,{default:a(()=>[e("div",S,[t[13]||(t[13]=e("div",{class:"content-card"},[e("h2",null,"欢迎使用五矿钢铁名片管理平台"),e("p",null,"当前登录用户：管理员")],-1)),s(i,{gutter:20,class:"stats-row"},{default:a(()=>[s(n,{span:6},{default:a(()=>[e("div",V,[e("div",q,[s(o,null,{default:a(()=>[s(d(g))]),_:1})]),e("div",A,[e("div",L,c(l.value.employeeCount),1),t[4]||(t[4]=e("div",{class:"stat-label"},"员工总数",-1))])])]),_:1}),s(n,{span:6},{default:a(()=>[e("div",z,[e("div",M,[s(o,null,{default:a(()=>[s(d(v))]),_:1})]),e("div",T,[e("div",j,c(l.value.backgroundImageCount),1),t[5]||(t[5]=e("div",{class:"stat-label"},"背景图片",-1))])])]),_:1}),s(n,{span:6},{default:a(()=>[e("div",F,[e("div",G,[s(o,null,{default:a(()=>[s(d(f))]),_:1})]),e("div",H,[e("div",J,c(l.value.longImageCount),1),t[6]||(t[6]=e("div",{class:"stat-label"},"长图数量",-1))])])]),_:1}),s(n,{span:6},{default:a(()=>[e("div",K,[e("div",O,[s(o,null,{default:a(()=>[s(d(b))]),_:1})]),e("div",P,[e("div",Q,c(l.value.departmentCount),1),t[7]||(t[7]=e("div",{class:"stat-label"},"部门数量",-1))])])]),_:1})]),_:1}),e("div",U,[t[12]||(t[12]=e("h3",null,"快捷操作",-1)),s(i,{gutter:20},{default:a(()=>[s(n,{span:6},{default:a(()=>[s(r,{type:"primary",onClick:t[0]||(t[0]=_=>u.$router.push("/departments")),style:{width:"100%"}},{default:a(()=>[s(o,null,{default:a(()=>[s(d(b))]),_:1}),t[8]||(t[8]=m(" 部门管理 "))]),_:1,__:[8]})]),_:1}),s(n,{span:6},{default:a(()=>[s(r,{type:"info",onClick:t[1]||(t[1]=_=>u.$router.push("/employees")),style:{width:"100%"}},{default:a(()=>[s(o,null,{default:a(()=>[s(d(g))]),_:1}),t[9]||(t[9]=m(" 员工管理 "))]),_:1,__:[9]})]),_:1}),s(n,{span:6},{default:a(()=>[s(r,{type:"success",onClick:t[2]||(t[2]=_=>u.$router.push("/background-images")),style:{width:"100%"}},{default:a(()=>[s(o,null,{default:a(()=>[s(d(v))]),_:1}),t[10]||(t[10]=m(" 背景图片 "))]),_:1,__:[10]})]),_:1}),s(n,{span:6},{default:a(()=>[s(r,{type:"warning",onClick:t[3]||(t[3]=_=>u.$router.push("/long-images")),style:{width:"100%"}},{default:a(()=>[s(o,null,{default:a(()=>[s(d(f))]),_:1}),t[11]||(t[11]=m(" 长图管理 "))]),_:1,__:[11]})]),_:1})]),_:1})])])]),_:1})}}},et=k(W,[["__scopeId","data-v-8ae8b6b9"]]);export{et as default};
