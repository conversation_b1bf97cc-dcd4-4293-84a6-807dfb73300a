package com.minmetals.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 部门创建请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DepartmentCreateRequest {

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    private String name;

    /**
     * 父部门ID（一级部门为null）
     */
    private Long parentId;

    /**
     * 部门级别（1-一级部门，2-二级部门，3-三级部门）
     */
    @NotNull(message = "部门级别不能为空")
    @Min(value = 1, message = "部门级别最小为1")
    @Max(value = 3, message = "部门级别最大为3")
    private Integer level;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门负责人
     */
    private String manager;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 办公地址
     */
    private String address;

    /**
     * 排序号
     */
    private Integer sortOrder = 0;

    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
