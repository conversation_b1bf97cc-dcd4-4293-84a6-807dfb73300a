import{_ as mn}from"./_plugin-vue_export-helper-063e7853.js";/* empty css                   */import{L as eo}from"./Layout-97877336.js";/* empty css               *//* empty css                 */import{r as hr,D as ro,F as gs,q as _s,o as vr,s as dt,v as Dt,G as to,b as ws,w as de,d as Ge,e as q,i as yr,t as Wt,H as it,j as Ir,k as ks,l as ao,y as Es,z as Ts,E as no,m as vn,I as Ss,c as Ut,g as la,J as io,K as so,L as fo,M as co,N as oo,O as lo,P as uo,x as Fs,Q as ho,R as xo,S as po,T as ys,U as As,V as mo,W as vo,X as go,Y as _o,Z as wo,_ as ko,$ as Eo,a0 as To}from"./index-fbf0bc73.js";/* empty css                     *//* empty css                    */const So={__name:"DepartmentCascader",props:{modelValue:[Number,String],placeholder:{type:String,default:"请选择部门"}},emits:["update:modelValue","change"],setup(e,{emit:t}){const r=e,a=t,n=hr([]),i=hr([]),s={value:"id",label:"name",children:"children",emitPath:!1,checkStrictly:!1},f=ro(()=>c(n.value)),c=h=>h.map(d=>({id:d.id,name:d.name,level:d.level,children:d.children&&d.children.length>0?c(d.children):void 0})),o=(h,d=n.value,p=[])=>{for(const x of d){const m=[...p,x.id];if(x.id===h)return m;if(x.children&&x.children.length>0){const _=o(h,x.children,m);if(_)return _}}return null},l=async()=>{try{const h=await Dt.get("/departments/enabled/tree");if(n.value=h.data||[],r.modelValue){const d=o(r.modelValue);d&&(i.value=d)}}catch(h){console.error("加载部门树失败:",h)}},u=h=>{const d=Array.isArray(h)?h[h.length-1]:h;a("update:modelValue",d),a("change",d,h)};return gs(()=>r.modelValue,h=>{if(h&&n.value.length>0){const d=o(h);d&&(i.value=d)}else i.value=[]}),_s(()=>{l()}),(h,d)=>{const p=to;return vr(),dt(p,{modelValue:i.value,"onUpdate:modelValue":d[0]||(d[0]=x=>i.value=x),options:f.value,props:s,placeholder:"请选择部门",style:{width:"100%"},clearable:"",filterable:"",onChange:u},null,8,["modelValue","options"])}}};const Fo={class:"dialog-footer"},yo={__name:"EmployeeForm",props:{visible:Boolean,employee:Object,isEdit:Boolean},emits:["update:visible","success"],setup(e,{emit:t}){const r=e,a=t,n=hr(),i=hr(!1),s=ws({username:"",name:"",employeeId:"",departmentId:null,department:"",position:"",email:"",phone:"",avatar:"",manager:"",workLocation:"",address:"",password:"",description:"",enabled:!0}),f={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],employeeId:[{required:!0,message:"请输入传真号码",trigger:"blur"},{pattern:/^[\d-]+$/,message:"请输入正确的传真号码格式",trigger:"blur"}],departmentId:[{required:!0,message:"请选择部门",trigger:"change"}],position:[{required:!0,message:"请选择职位",trigger:"change"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{required:!0,message:"请输入电话号码",trigger:"blur"},{pattern:/^[\d-]+$/,message:"请输入正确的电话号码格式",trigger:"blur"}],avatar:[{required:!0,message:"请输入移动电话号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的移动电话号码格式",trigger:"blur"}],manager:[{pattern:/^\d{6}$/,message:"请输入正确的邮政编码格式（6位数字）",trigger:"blur"}],password:[{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},c=async l=>{if(l)try{const u=await Dt.get(`/departments/${l}`);s.department=u.data.name}catch(u){console.error("获取部门信息失败:",u)}else s.department=""};gs(()=>r.employee,l=>{l?Object.assign(s,{username:l.username||"",name:l.name||"",employeeId:l.employeeId||"",departmentId:l.departmentId||null,department:l.department||"",position:l.position||"",email:l.email||"",phone:l.phone||"",avatar:l.avatar||"",manager:l.manager||"",workLocation:l.workLocation||"",address:l.address||"",password:"",description:l.description||"",enabled:l.enabled!==!1}):Object.assign(s,{username:"",name:"",employeeId:"",departmentId:null,department:"",position:"",email:"",phone:"",avatar:"",manager:"",workLocation:"",address:"",password:"",description:"",enabled:!0})},{immediate:!0});const o=async()=>{if(n.value)try{await n.value.validate(),i.value=!0;const l={...s};r.isEdit&&!l.password&&delete l.password,!r.isEdit&&!l.password&&(l.password="123456"),r.isEdit?(await Dt.put(`/employees/${r.employee.id}`,l),Ir.success("更新成功")):(await Dt.post("/employees",l),Ir.success("创建成功")),a("success")}catch(l){console.error("保存员工失败:",l)}finally{i.value=!1}};return(l,u)=>{const h=ks,d=ao,p=Es,x=Ts,m=no,_=vn,T=Ss;return vr(),dt(T,{"model-value":e.visible,"onUpdate:modelValue":u[14]||(u[14]=g=>l.$emit("update:visible",g)),title:e.isEdit?"编辑员工":"新增员工",width:"600px","close-on-click-modal":!1},{footer:de(()=>[Ge("div",Fo,[q(_,{onClick:u[13]||(u[13]=g=>l.$emit("update:visible",!1))},{default:de(()=>u[15]||(u[15]=[yr(" 取消 ")])),_:1,__:[15]}),q(_,{type:"primary",loading:i.value,onClick:o},{default:de(()=>[yr(Wt(i.value?"保存中...":"保存"),1)]),_:1},8,["loading"])])]),default:de(()=>[q(m,{ref_key:"formRef",ref:n,model:s,rules:f,"label-width":"100px"},{default:de(()=>[q(x,{gutter:20},{default:de(()=>[q(p,{span:12},{default:de(()=>[q(d,{label:"用户名",prop:"username"},{default:de(()=>[q(h,{modelValue:s.username,"onUpdate:modelValue":u[0]||(u[0]=g=>s.username=g),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1}),q(p,{span:12},{default:de(()=>[q(d,{label:"姓名",prop:"name"},{default:de(()=>[q(h,{modelValue:s.name,"onUpdate:modelValue":u[1]||(u[1]=g=>s.name=g),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),q(x,{gutter:20},{default:de(()=>[q(p,{span:12},{default:de(()=>[q(d,{label:"传真",prop:"employeeId"},{default:de(()=>[q(h,{modelValue:s.employeeId,"onUpdate:modelValue":u[2]||(u[2]=g=>s.employeeId=g),placeholder:"请输入传真号码"},null,8,["modelValue"])]),_:1})]),_:1}),q(p,{span:12},{default:de(()=>[q(d,{label:"移动电话",prop:"avatar"},{default:de(()=>[q(h,{modelValue:s.avatar,"onUpdate:modelValue":u[3]||(u[3]=g=>s.avatar=g),placeholder:"请输入移动电话号码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),q(x,{gutter:20},{default:de(()=>[q(p,{span:12},{default:de(()=>[q(d,{label:"部门",prop:"departmentId"},{default:de(()=>[q(So,{modelValue:s.departmentId,"onUpdate:modelValue":u[4]||(u[4]=g=>s.departmentId=g),onChange:c},null,8,["modelValue"])]),_:1})]),_:1}),q(p,{span:12},{default:de(()=>[q(d,{label:"职位",prop:"position"},{default:de(()=>[q(h,{modelValue:s.position,"onUpdate:modelValue":u[5]||(u[5]=g=>s.position=g),placeholder:"请输入职位"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),q(x,{gutter:20},{default:de(()=>[q(p,{span:12},{default:de(()=>[q(d,{label:"邮箱",prop:"email"},{default:de(()=>[q(h,{modelValue:s.email,"onUpdate:modelValue":u[6]||(u[6]=g=>s.email=g),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1}),q(p,{span:12},{default:de(()=>[q(d,{label:"电话",prop:"phone"},{default:de(()=>[q(h,{modelValue:s.phone,"onUpdate:modelValue":u[7]||(u[7]=g=>s.phone=g),placeholder:"请输入电话号码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),q(x,{gutter:20},{default:de(()=>[q(p,{span:12},{default:de(()=>[q(d,{label:"邮政编码",prop:"manager"},{default:de(()=>[q(h,{modelValue:s.manager,"onUpdate:modelValue":u[8]||(u[8]=g=>s.manager=g),placeholder:"请输入邮政编码"},null,8,["modelValue"])]),_:1})]),_:1}),q(p,{span:12},{default:de(()=>[q(d,{label:"工作地址",prop:"workLocation"},{default:de(()=>[q(h,{modelValue:s.workLocation,"onUpdate:modelValue":u[9]||(u[9]=g=>s.workLocation=g),placeholder:"请输入工作地址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),q(d,{label:"详细地址",prop:"address"},{default:de(()=>[q(h,{modelValue:s.address,"onUpdate:modelValue":u[10]||(u[10]=g=>s.address=g),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),e.isEdit?it("",!0):(vr(),dt(d,{key:0,label:"密码",prop:"password"},{default:de(()=>[q(h,{modelValue:s.password,"onUpdate:modelValue":u[11]||(u[11]=g=>s.password=g),type:"password",placeholder:"留空则使用默认密码：123456","show-password":""},null,8,["modelValue"])]),_:1})),q(d,{label:"个人简介"},{default:de(()=>[q(h,{modelValue:s.description,"onUpdate:modelValue":u[12]||(u[12]=g=>s.description=g),type:"textarea",rows:3,placeholder:"请输入个人简介"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}},Ao=mn(yo,[["__scopeId","data-v-dc8dfd5e"]]);/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var _0={};_0.version="0.18.5";var Cs=1252,Co=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],gn={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},_n=function(e){Co.indexOf(e)!=-1&&(Cs=gn[0]=e)};function Do(){_n(1252)}var Zr=function(e){_n(e)};function wn(){Zr(1200),Do()}function oi(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function Io(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function Ds(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var Ca=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return t==255&&r==254?Io(e.slice(2)):t==254&&r==255?Ds(e.slice(2)):t==65279?e.slice(1):e},f0=function(t){return String.fromCharCode(t)},li=function(t){return String.fromCharCode(t)},yt,At="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Ua(e){for(var t="",r=0,a=0,n=0,i=0,s=0,f=0,c=0,o=0;o<e.length;)r=e.charCodeAt(o++),i=r>>2,a=e.charCodeAt(o++),s=(r&3)<<4|a>>4,n=e.charCodeAt(o++),f=(a&15)<<2|n>>6,c=n&63,isNaN(a)?f=c=64:isNaN(n)&&(c=64),t+=At.charAt(i)+At.charAt(s)+At.charAt(f)+At.charAt(c);return t}function Ur(e){var t="",r=0,a=0,n=0,i=0,s=0,f=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)i=At.indexOf(e.charAt(o++)),s=At.indexOf(e.charAt(o++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=At.indexOf(e.charAt(o++)),a=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(a)),c=At.indexOf(e.charAt(o++)),n=(f&3)<<6|c,c!==64&&(t+=String.fromCharCode(n));return t}var Fe=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),wt=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function It(e){return Fe?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function ui(e){return Fe?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var Br=function(t){return Fe?wt(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function b0(e){if(typeof ArrayBuffer>"u")return Br(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=e.charCodeAt(a)&255;return t}function Nt(e){if(Array.isArray(e))return e.map(function(a){return String.fromCharCode(a)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Oo(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}function kn(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return kn(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var or=Fe?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:wt(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function bo(e){for(var t=[],r=0,a=e.length+250,n=It(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)n[r++]=s;else if(s<2048)n[r++]=192|s>>6&31,n[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;n[r++]=240|s>>8&7,n[r++]=128|s>>2&63,n[r++]=128|f>>6&15|(s&3)<<4,n[r++]=128|f&63}else n[r++]=224|s>>12&15,n[r++]=128|s>>6&63,n[r++]=128|s&63;r>a&&(t.push(n.slice(0,r)),r=0,n=It(65535),a=65530)}return t.push(n.slice(0,r)),or(t)}var Dr=/\u0000/g,Da=/[\u0001-\u0006]/g;function ua(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function Qr(e,t){var r=""+e;return r.length>=t?r:je("0",t-r.length)+r}function En(e,t){var r=""+e;return r.length>=t?r:je(" ",t-r.length)+r}function w0(e,t){var r=""+e;return r.length>=t?r:r+je(" ",t-r.length)}function Ro(e,t){var r=""+Math.round(e);return r.length>=t?r:je("0",t-r.length)+r}function No(e,t){var r=""+e;return r.length>=t?r:je("0",t-r.length)+r}var hi=Math.pow(2,32);function ia(e,t){if(e>hi||e<-hi)return Ro(e,t);var r=Math.round(e);return No(r,t)}function k0(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var xi=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],G0=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Po(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var ge={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},di={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Lo={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function E0(e,t,r){for(var a=e<0?-1:1,n=e*a,i=0,s=1,f=0,c=1,o=0,l=0,u=Math.floor(n);o<t&&(u=Math.floor(n),f=u*s+i,l=u*o+c,!(n-u<5e-8));)n=1/(n-u),i=s,s=f,c=o,o=l;if(l>t&&(o>t?(l=c,f=i):(l=o,f=s)),!r)return[0,a*f,l];var h=Math.floor(a*f/l);return[h,a*f-h*l,l]}function Ht(e,t,r){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),i=0,s=[],f={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(a+=1462),f.u>.9999&&(f.u=0,++n==86400&&(f.T=n=0,++a,++f.D)),a===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(a===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),s=[c.getFullYear(),c.getMonth()+1,c.getDate()],i=c.getDay(),a<60&&(i=(i+6)%7),r&&(i=Ho(c,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=n%60,n=Math.floor(n/60),f.M=n%60,n=Math.floor(n/60),f.H=n,f.q=i,f}var Is=new Date(1899,11,31,0,0,0),Bo=Is.getTime(),Mo=new Date(1900,2,1,0,0,0);function Os(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Mo&&(r+=24*60*60*1e3),(r-(Bo+(e.getTimezoneOffset()-Is.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function Tn(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Uo(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Wo(e){var t=e<0?12:11,r=Tn(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Vo(e){var t=Tn(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Wa(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Wo(e):t===10?r=e.toFixed(10).substr(0,12):r=Vo(e),Tn(Uo(r.toUpperCase()))}function Kt(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Wa(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Yr(14,Os(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Ho(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Go(e,t,r,a){var n="",i=0,s=0,f=r.y,c,o=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:c=f%100,o=2;break;default:c=f%1e4,o=4;break}break;case 109:switch(t.length){case 1:case 2:c=r.m,o=t.length;break;case 3:return G0[r.m-1][1];case 5:return G0[r.m-1][0];default:return G0[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:c=r.d,o=t.length;break;case 3:return xi[r.q][0];default:return xi[r.q][1]}break;case 104:switch(t.length){case 1:case 2:c=1+(r.H+11)%12,o=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:c=r.H,o=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:c=r.M,o=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?Qr(r.S,t.length):(a>=2?s=a===3?1e3:100:s=a===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(n=Qr(i,2+a),t==="ss"?n.substr(0,2):"."+n.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":c=r.D*24+r.H;break;case"[m]":case"[mm]":c=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":c=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}o=t.length===3?1:2;break;case 101:c=f,o=1;break}var l=o>0?Qr(c,o):"";return l}function Ct(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,a=e.substr(0,r);r!=e.length;r+=t)a+=(a.length>0?",":"")+e.substr(r,t);return a}var bs=/%/g;function Xo(e,t,r){var a=t.replace(bs,""),n=t.length-a.length;return mt(e,a,r*Math.pow(10,2*n))+je("%",n)}function zo(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return mt(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function Rs(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Rs(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%n;if(i<0&&(i+=n),r=(t/Math.pow(10,i)).toPrecision(a+1+(n+i)%n),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,c,o,l){return c+o+l.substr(0,(n+i)%n)+"."+l.substr(i)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Ns=/# (\?+)( ?)\/( ?)(\d+)/;function $o(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),i=Math.floor(n/a),s=n-i*a,f=a;return r+(i===0?"":""+i)+" "+(s===0?je(" ",e[1].length+1+e[4].length):En(s,e[1].length)+e[2]+"/"+e[3]+Qr(f,e[4].length))}function Ko(e,t,r){return r+(t===0?"":""+t)+je(" ",e[1].length+2+e[4].length)}var Ps=/^#*0*\.([0#]+)/,Ls=/\).*[0#]/,Bs=/\(###\) ###\\?-####/;function Ar(e){for(var t="",r,a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function pi(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function mi(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function jo(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Yo(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Gr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ls)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Gr("n",a,r):"("+Gr("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return zo(e,t,r);if(t.indexOf("%")!==-1)return Xo(e,t,r);if(t.indexOf("E")!==-1)return Rs(t,r);if(t.charCodeAt(0)===36)return"$"+Gr(e,t.substr(t.charAt(1)==" "?2:1),r);var n,i,s,f,c=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+ia(c,t.length);if(t.match(/^[#?]+$/))return n=ia(r,0),n==="0"&&(n=""),n.length>t.length?n:Ar(t.substr(0,t.length-n.length))+n;if(i=t.match(Ns))return $o(i,c,o);if(t.match(/^#+0+$/))return o+ia(c,t.length-t.indexOf("0"));if(i=t.match(Ps))return n=pi(r,i[1].length).replace(/^([^\.]+)$/,"$1."+Ar(i[1])).replace(/\.$/,"."+Ar(i[1])).replace(/\.(\d*)$/,function(p,x){return"."+x+je("0",Ar(i[1]).length-x.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+pi(c,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Ct(ia(c,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Gr(e,t,-r):Ct(""+(Math.floor(r)+jo(r,i[1].length)))+"."+Qr(mi(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return Gr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=ua(Gr(e,t.replace(/[\\-]/g,""),r)),s=0,ua(ua(t.replace(/\\/g,"")).replace(/[0#]/g,function(p){return s<n.length?n.charAt(s++):p==="0"?"0":""}));if(t.match(Bs))return n=Gr(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var l="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=E0(c,Math.pow(10,s)-1,!1),n=""+o,l=mt("n",i[1],f[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),n+=l+i[2]+"/"+i[3],l=w0(f[2],s),l.length<i[4].length&&(l=Ar(i[4].substr(i[4].length-l.length))+l),n+=l,n;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=E0(c,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?En(f[1],s)+i[2]+"/"+i[3]+w0(f[2],s):je(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return n=ia(r,0),t.length<=n.length?n:Ar(t.substr(0,t.length-n.length))+n;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=n.indexOf(".");var u=t.indexOf(".")-s,h=t.length-n.length-u;return Ar(t.substr(0,u)+n+t.substr(t.length-h))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=mi(r,i[1].length),r<0?"-"+Gr(e,t,-r):Ct(Yo(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(p){return"00,"+(p.length<3?Qr(0,3-p.length):"")+p})+"."+Qr(s,i[1].length);switch(t){case"###,##0.00":return Gr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=Ct(ia(c,0));return d!=="0"?o+d:"";case"###,###.00":return Gr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Gr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function Jo(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return mt(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function qo(e,t,r){var a=t.replace(bs,""),n=t.length-a.length;return mt(e,a,r*Math.pow(10,2*n))+je("%",n)}function Ms(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ms(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%n;if(i<0&&(i+=n),r=(t/Math.pow(10,i)).toPrecision(a+1+(n+i)%n),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,c,o,l){return c+o+l.substr(0,(n+i)%n)+"."+l.substr(i)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function st(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ls)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?st("n",a,r):"("+st("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Jo(e,t,r);if(t.indexOf("%")!==-1)return qo(e,t,r);if(t.indexOf("E")!==-1)return Ms(t,r);if(t.charCodeAt(0)===36)return"$"+st(e,t.substr(t.charAt(1)==" "?2:1),r);var n,i,s,f,c=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+Qr(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,r===0&&(n=""),n.length>t.length?n:Ar(t.substr(0,t.length-n.length))+n;if(i=t.match(Ns))return Ko(i,c,o);if(t.match(/^#+0+$/))return o+Qr(c,t.length-t.indexOf("0"));if(i=t.match(Ps))return n=(""+r).replace(/^([^\.]+)$/,"$1."+Ar(i[1])).replace(/\.$/,"."+Ar(i[1])),n=n.replace(/\.(\d*)$/,function(p,x){return"."+x+je("0",Ar(i[1]).length-x.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Ct(""+c);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+st(e,t,-r):Ct(""+r)+"."+je("0",i[1].length);if(i=t.match(/^#,#*,#0/))return st(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=ua(st(e,t.replace(/[\\-]/g,""),r)),s=0,ua(ua(t.replace(/\\/g,"")).replace(/[0#]/g,function(p){return s<n.length?n.charAt(s++):p==="0"?"0":""}));if(t.match(Bs))return n=st(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var l="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=E0(c,Math.pow(10,s)-1,!1),n=""+o,l=mt("n",i[1],f[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),n+=l+i[2]+"/"+i[3],l=w0(f[2],s),l.length<i[4].length&&(l=Ar(i[4].substr(i[4].length-l.length))+l),n+=l,n;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=E0(c,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?En(f[1],s)+i[2]+"/"+i[3]+w0(f[2],s):je(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:Ar(t.substr(0,t.length-n.length))+n;if(i=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=n.indexOf(".");var u=t.indexOf(".")-s,h=t.length-n.length-u;return Ar(t.substr(0,u)+n+t.substr(t.length-h))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+st(e,t,-r):Ct(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(p){return"00,"+(p.length<3?Qr(0,3-p.length):"")+p})+"."+Qr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=Ct(""+c);return d!=="0"?o+d:"";default:if(t.match(/\.[0#?]*$/))return st(e,t.slice(0,t.lastIndexOf(".")),r)+Ar(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function mt(e,t,r){return(r|0)===r?st(e,t,r):Gr(e,t,r)}function Zo(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Us=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ga(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":k0(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(a=r;e.charAt(t++)!=="]"&&t<e.length;)a+=e.charAt(t);if(a.match(Us))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function Qo(e,t,r,a){for(var n=[],i="",s=0,f="",c="t",o,l,u,h="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!k0(e,s))throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(u=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(u);n[n.length]={t:"t",v:i},++s;break;case"\\":var d=e.charAt(++s),p=d==="("||d===")"?d:"t";n[n.length]={t:p,v:d},++s;break;case"_":n[n.length]={t:"t",v:" "},s+=2;break;case"@":n[n.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(o==null&&(o=Ht(t,r,e.charAt(s+1)==="2"),o==null))return"";n[n.length]={t:"X",v:e.substr(s,2)},c=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||o==null&&(o=Ht(t,r),o==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&c.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=h),n[n.length]={t:f,v:i},c=f;break;case"A":case"a":case"上":var x={t:f,v:f};if(o==null&&(o=Ht(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(o!=null&&(x.v=o.H>=12?"P":"A"),x.t="T",h="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(o!=null&&(x.v=o.H>=12?"PM":"AM"),x.t="T",s+=5,h="h"):e.substr(s,5).toUpperCase()==="上午/下午"?(o!=null&&(x.v=o.H>=12?"下午":"上午"),x.t="T",s+=5,h="h"):(x.t="t",++s),o==null&&x.t==="T")return"";n[n.length]=x,c=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Us)){if(o==null&&(o=Ht(t,r),o==null))return"";n[n.length]={t:"Z",v:i.toLowerCase()},c=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",ga(e)||(n[n.length]={t:"t",v:i}));break;case".":if(o!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;n[n.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;n[n.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;n[n.length]={t:f,v:i},c=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":n[n.length]={t:a===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);n[n.length]={t:"D",v:i};break;case" ":n[n.length]={t:f,v:f},++s;break;case"$":n[n.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);n[n.length]={t:"t",v:f},++s;break}var m=0,_=0,T;for(s=n.length-1,c="t";s>=0;--s)switch(n[s].t){case"h":case"H":n[s].t=h,c="h",m<1&&(m=1);break;case"s":(T=n[s].v.match(/\.0+$/))&&(_=Math.max(_,T[0].length-1)),m<3&&(m=3);case"d":case"y":case"M":case"e":c=n[s].t;break;case"m":c==="s"&&(n[s].t="M",m<2&&(m=2));break;case"X":break;case"Z":m<1&&n[s].v.match(/[Hh]/)&&(m=1),m<2&&n[s].v.match(/[Mm]/)&&(m=2),m<3&&n[s].v.match(/[Ss]/)&&(m=3)}switch(m){case 0:break;case 1:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M),o.M>=60&&(o.M=0,++o.H);break;case 2:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M);break}var g="",I;for(s=0;s<n.length;++s)switch(n[s].t){case"t":case"T":case" ":case"D":break;case"X":n[s].v="",n[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":n[s].v=Go(n[s].t.charCodeAt(0),n[s].v,o,_),n[s].t="t";break;case"n":case"?":for(I=s+1;n[I]!=null&&((f=n[I].t)==="?"||f==="D"||(f===" "||f==="t")&&n[I+1]!=null&&(n[I+1].t==="?"||n[I+1].t==="t"&&n[I+1].v==="/")||n[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(n[I].v==="/"||n[I].v===" "&&n[I+1]!=null&&n[I+1].t=="?"));)n[s].v+=n[I].v,n[I]={v:"",t:";"},++I;g+=n[s].v,s=I-1;break;case"G":n[s].t="t",n[s].v=Kt(t,r);break}var L="",D,k;if(g.length>0){g.charCodeAt(0)==40?(D=t<0&&g.charCodeAt(0)===45?-t:t,k=mt("n",g,D)):(D=t<0&&a>1?-t:t,k=mt("n",g,D),D<0&&n[0]&&n[0].t=="t"&&(k=k.substr(1),n[0].v="-"+n[0].v)),I=k.length-1;var R=n.length;for(s=0;s<n.length;++s)if(n[s]!=null&&n[s].t!="t"&&n[s].v.indexOf(".")>-1){R=s;break}var O=n.length;if(R===n.length&&k.indexOf("E")===-1){for(s=n.length-1;s>=0;--s)n[s]==null||"n?".indexOf(n[s].t)===-1||(I>=n[s].v.length-1?(I-=n[s].v.length,n[s].v=k.substr(I+1,n[s].v.length)):I<0?n[s].v="":(n[s].v=k.substr(0,I+1),I=-1),n[s].t="t",O=s);I>=0&&O<n.length&&(n[O].v=k.substr(0,I+1)+n[O].v)}else if(R!==n.length&&k.indexOf("E")===-1){for(I=k.indexOf(".")-1,s=R;s>=0;--s)if(!(n[s]==null||"n?".indexOf(n[s].t)===-1)){for(l=n[s].v.indexOf(".")>-1&&s===R?n[s].v.indexOf(".")-1:n[s].v.length-1,L=n[s].v.substr(l+1);l>=0;--l)I>=0&&(n[s].v.charAt(l)==="0"||n[s].v.charAt(l)==="#")&&(L=k.charAt(I--)+L);n[s].v=L,n[s].t="t",O=s}for(I>=0&&O<n.length&&(n[O].v=k.substr(0,I+1)+n[O].v),I=k.indexOf(".")+1,s=R;s<n.length;++s)if(!(n[s]==null||"n?(".indexOf(n[s].t)===-1&&s!==R)){for(l=n[s].v.indexOf(".")>-1&&s===R?n[s].v.indexOf(".")+1:0,L=n[s].v.substr(0,l);l<n[s].v.length;++l)I<k.length&&(L+=k.charAt(I++));n[s].v=L,n[s].t="t",O=s}}}for(s=0;s<n.length;++s)n[s]!=null&&"n?".indexOf(n[s].t)>-1&&(D=a>1&&t<0&&s>0&&n[s-1].v==="-"?-t:t,n[s].v=mt(n[s].t,n[s].v,D),n[s].t="t");var X="";for(s=0;s!==n.length;++s)n[s]!=null&&(X+=n[s].v);return X}var vi=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function gi(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function el(e,t){var r=Zo(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[a,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(vi),f=r[1].match(vi);return gi(t,s)?[a,r[0]]:gi(t,f)?[a,r[1]]:[a,r[s!=null&&f!=null?2:1]]}return[a,i]}function Yr(e,t,r){r==null&&(r={});var a="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?a=r.dateNF:a=e;break;case"number":e==14&&r.dateNF?a=r.dateNF:a=(r.table!=null?r.table:ge)[e],a==null&&(a=r.table&&r.table[di[e]]||ge[di[e]]),a==null&&(a=Lo[e]||"General");break}if(k0(a,0))return Kt(t,r);t instanceof Date&&(t=Os(t,r.date1904));var n=el(a,t);if(k0(n[1]))return Kt(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return Qo(n[1],t,r,n[0])}function vt(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(ge[r]==null){t<0&&(t=r);continue}if(ge[r]==e){t=r;break}}t<0&&(t=391)}return ge[t]=e,t}function R0(e){for(var t=0;t!=392;++t)e[t]!==void 0&&vt(e[t],t)}function _a(){ge=Po()}var rl={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},Ws=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function tl(e){var t=typeof e=="number"?ge[e]:e;return t=t.replace(Ws,"(\\d+)"),new RegExp("^"+t+"$")}function al(e,t,r){var a=-1,n=-1,i=-1,s=-1,f=-1,c=-1;(t.match(Ws)||[]).forEach(function(u,h){var d=parseInt(r[h+1],10);switch(u.toLowerCase().charAt(0)){case"y":a=d;break;case"d":i=d;break;case"h":s=d;break;case"s":c=d;break;case"m":s>=0?f=d:n=d;break}}),c>=0&&f==-1&&n>=0&&(f=n,n=-1);var o=(""+(a>=0?a:new Date().getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);o.length==7&&(o="0"+o),o.length==8&&(o="20"+o);var l=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return s==-1&&f==-1&&c==-1?o:a==-1&&n==-1&&i==-1?l:o+"T"+l}var nl=function(){var e={};e.version="1.2.0";function t(){for(var k=0,R=new Array(256),O=0;O!=256;++O)k=O,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,k=k&1?-306674912^k>>>1:k>>>1,R[O]=k;return typeof Int32Array<"u"?new Int32Array(R):R}var r=t();function a(k){var R=0,O=0,X=0,M=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(X=0;X!=256;++X)M[X]=k[X];for(X=0;X!=256;++X)for(O=k[X],R=256+X;R<4096;R+=256)O=M[R]=O>>>8^k[O&255];var P=[];for(X=1;X!=16;++X)P[X-1]=typeof Int32Array<"u"?M.subarray(X*256,X*256+256):M.slice(X*256,X*256+256);return P}var n=a(r),i=n[0],s=n[1],f=n[2],c=n[3],o=n[4],l=n[5],u=n[6],h=n[7],d=n[8],p=n[9],x=n[10],m=n[11],_=n[12],T=n[13],g=n[14];function I(k,R){for(var O=R^-1,X=0,M=k.length;X<M;)O=O>>>8^r[(O^k.charCodeAt(X++))&255];return~O}function L(k,R){for(var O=R^-1,X=k.length-15,M=0;M<X;)O=g[k[M++]^O&255]^T[k[M++]^O>>8&255]^_[k[M++]^O>>16&255]^m[k[M++]^O>>>24]^x[k[M++]]^p[k[M++]]^d[k[M++]]^h[k[M++]]^u[k[M++]]^l[k[M++]]^o[k[M++]]^c[k[M++]]^f[k[M++]]^s[k[M++]]^i[k[M++]]^r[k[M++]];for(X+=15;M<X;)O=O>>>8^r[(O^k[M++])&255];return~O}function D(k,R){for(var O=R^-1,X=0,M=k.length,P=0,J=0;X<M;)P=k.charCodeAt(X++),P<128?O=O>>>8^r[(O^P)&255]:P<2048?(O=O>>>8^r[(O^(192|P>>6&31))&255],O=O>>>8^r[(O^(128|P&63))&255]):P>=55296&&P<57344?(P=(P&1023)+64,J=k.charCodeAt(X++)&1023,O=O>>>8^r[(O^(240|P>>8&7))&255],O=O>>>8^r[(O^(128|P>>2&63))&255],O=O>>>8^r[(O^(128|J>>6&15|(P&3)<<4))&255],O=O>>>8^r[(O^(128|J&63))&255]):(O=O>>>8^r[(O^(224|P>>12&15))&255],O=O>>>8^r[(O^(128|P>>6&63))&255],O=O>>>8^r[(O^(128|P&63))&255]);return~O}return e.table=r,e.bstr=I,e.buf=L,e.str=D,e}(),me=function(){var t={};t.version="1.2.1";function r(v,S){for(var w=v.split("/"),E=S.split("/"),F=0,y=0,W=Math.min(w.length,E.length);F<W;++F){if(y=w[F].length-E[F].length)return y;if(w[F]!=E[F])return w[F]<E[F]?-1:1}return w.length-E.length}function a(v){if(v.charAt(v.length-1)=="/")return v.slice(0,-1).indexOf("/")===-1?v:a(v.slice(0,-1));var S=v.lastIndexOf("/");return S===-1?v:v.slice(0,S+1)}function n(v){if(v.charAt(v.length-1)=="/")return n(v.slice(0,-1));var S=v.lastIndexOf("/");return S===-1?v:v.slice(S+1)}function i(v,S){typeof S=="string"&&(S=new Date(S));var w=S.getHours();w=w<<6|S.getMinutes(),w=w<<5|S.getSeconds()>>>1,v.write_shift(2,w);var E=S.getFullYear()-1980;E=E<<4|S.getMonth()+1,E=E<<5|S.getDate(),v.write_shift(2,E)}function s(v){var S=v.read_shift(2)&65535,w=v.read_shift(2)&65535,E=new Date,F=w&31;w>>>=5;var y=w&15;w>>>=4,E.setMilliseconds(0),E.setFullYear(w+1980),E.setMonth(y-1),E.setDate(F);var W=S&31;S>>>=5;var K=S&63;return S>>>=6,E.setHours(S),E.setMinutes(K),E.setSeconds(W<<1),E}function f(v){xr(v,0);for(var S={},w=0;v.l<=v.length-4;){var E=v.read_shift(2),F=v.read_shift(2),y=v.l+F,W={};switch(E){case 21589:w=v.read_shift(1),w&1&&(W.mtime=v.read_shift(4)),F>5&&(w&2&&(W.atime=v.read_shift(4)),w&4&&(W.ctime=v.read_shift(4))),W.mtime&&(W.mt=new Date(W.mtime*1e3));break}v.l=y,S[E]=W}return S}var c;function o(){return c||(c={})}function l(v,S){if(v[0]==80&&v[1]==75)return ci(v,S);if((v[0]|32)==109&&(v[1]|32)==105)return jc(v,S);if(v.length<512)throw new Error("CFB file size "+v.length+" < 512");var w=3,E=512,F=0,y=0,W=0,K=0,U=0,H=[],G=v.slice(0,512);xr(G,0);var ee=u(G);switch(w=ee[0],w){case 3:E=512;break;case 4:E=4096;break;case 0:if(ee[1]==0)return ci(v,S);default:throw new Error("Major Version: Expected 3 or 4 saw "+w)}E!==512&&(G=v.slice(0,E),xr(G,28));var fe=v.slice(0,E);h(G,w);var xe=G.read_shift(4,"i");if(w===3&&xe!==0)throw new Error("# Directory Sectors: Expected 0 saw "+xe);G.l+=4,W=G.read_shift(4,"i"),G.l+=4,G.chk("00100000","Mini Stream Cutoff Size: "),K=G.read_shift(4,"i"),F=G.read_shift(4,"i"),U=G.read_shift(4,"i"),y=G.read_shift(4,"i");for(var te=-1,he=0;he<109&&(te=G.read_shift(4,"i"),!(te<0));++he)H[he]=te;var ke=d(v,E);m(U,y,ke,E,H);var Xe=T(ke,W,H,E);Xe[W].name="!Directory",F>0&&K!==J&&(Xe[K].name="!MiniFAT"),Xe[H[0]].name="!FAT",Xe.fat_addrs=H,Xe.ssz=E;var ze={},pr=[],Sa=[],Fa=[];g(W,Xe,ke,pr,F,ze,Sa,K),p(Sa,Fa,pr),pr.shift();var ya={FileIndex:Sa,FullPaths:Fa};return S&&S.raw&&(ya.raw={header:fe,sectors:ke}),ya}function u(v){if(v[v.l]==80&&v[v.l+1]==75)return[0,0];v.chk(le,"Header Signature: "),v.l+=16;var S=v.read_shift(2,"u");return[v.read_shift(2,"u"),S]}function h(v,S){var w=9;switch(v.l+=2,w=v.read_shift(2)){case 9:if(S!=3)throw new Error("Sector Shift: Expected 9 saw "+w);break;case 12:if(S!=4)throw new Error("Sector Shift: Expected 12 saw "+w);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+w)}v.chk("0600","Mini Sector Shift: "),v.chk("000000000000","Reserved: ")}function d(v,S){for(var w=Math.ceil(v.length/S)-1,E=[],F=1;F<w;++F)E[F-1]=v.slice(F*S,(F+1)*S);return E[w-1]=v.slice(w*S),E}function p(v,S,w){for(var E=0,F=0,y=0,W=0,K=0,U=w.length,H=[],G=[];E<U;++E)H[E]=G[E]=E,S[E]=w[E];for(;K<G.length;++K)E=G[K],F=v[E].L,y=v[E].R,W=v[E].C,H[E]===E&&(F!==-1&&H[F]!==F&&(H[E]=H[F]),y!==-1&&H[y]!==y&&(H[E]=H[y])),W!==-1&&(H[W]=E),F!==-1&&E!=H[E]&&(H[F]=H[E],G.lastIndexOf(F)<K&&G.push(F)),y!==-1&&E!=H[E]&&(H[y]=H[E],G.lastIndexOf(y)<K&&G.push(y));for(E=1;E<U;++E)H[E]===E&&(y!==-1&&H[y]!==y?H[E]=H[y]:F!==-1&&H[F]!==F&&(H[E]=H[F]));for(E=1;E<U;++E)if(v[E].type!==0){if(K=E,K!=H[K])do K=H[K],S[E]=S[K]+"/"+S[E];while(K!==0&&H[K]!==-1&&K!=H[K]);H[E]=-1}for(S[0]+="/",E=1;E<U;++E)v[E].type!==2&&(S[E]+="/")}function x(v,S,w){for(var E=v.start,F=v.size,y=[],W=E;w&&F>0&&W>=0;)y.push(S.slice(W*P,W*P+P)),F-=P,W=Vt(w,W*4);return y.length===0?z(0):or(y).slice(0,v.size)}function m(v,S,w,E,F){var y=J;if(v===J){if(S!==0)throw new Error("DIFAT chain shorter than expected")}else if(v!==-1){var W=w[v],K=(E>>>2)-1;if(!W)return;for(var U=0;U<K&&(y=Vt(W,U*4))!==J;++U)F.push(y);m(Vt(W,E-4),S-1,w,E,F)}}function _(v,S,w,E,F){var y=[],W=[];F||(F=[]);var K=E-1,U=0,H=0;for(U=S;U>=0;){F[U]=!0,y[y.length]=U,W.push(v[U]);var G=w[Math.floor(U*4/E)];if(H=U*4&K,E<4+H)throw new Error("FAT boundary crossed: "+U+" 4 "+E);if(!v[G])break;U=Vt(v[G],H)}return{nodes:y,data:Ii([W])}}function T(v,S,w,E){var F=v.length,y=[],W=[],K=[],U=[],H=E-1,G=0,ee=0,fe=0,xe=0;for(G=0;G<F;++G)if(K=[],fe=G+S,fe>=F&&(fe-=F),!W[fe]){U=[];var te=[];for(ee=fe;ee>=0;){te[ee]=!0,W[ee]=!0,K[K.length]=ee,U.push(v[ee]);var he=w[Math.floor(ee*4/E)];if(xe=ee*4&H,E<4+xe)throw new Error("FAT boundary crossed: "+ee+" 4 "+E);if(!v[he]||(ee=Vt(v[he],xe),te[ee]))break}y[fe]={nodes:K,data:Ii([U])}}return y}function g(v,S,w,E,F,y,W,K){for(var U=0,H=E.length?2:0,G=S[v].data,ee=0,fe=0,xe;ee<G.length;ee+=128){var te=G.slice(ee,ee+128);xr(te,64),fe=te.read_shift(2),xe=In(te,0,fe-H),E.push(xe);var he={name:xe,type:te.read_shift(1),color:te.read_shift(1),L:te.read_shift(4,"i"),R:te.read_shift(4,"i"),C:te.read_shift(4,"i"),clsid:te.read_shift(16),state:te.read_shift(4,"i"),start:0,size:0},ke=te.read_shift(2)+te.read_shift(2)+te.read_shift(2)+te.read_shift(2);ke!==0&&(he.ct=I(te,te.l-8));var Xe=te.read_shift(2)+te.read_shift(2)+te.read_shift(2)+te.read_shift(2);Xe!==0&&(he.mt=I(te,te.l-8)),he.start=te.read_shift(4,"i"),he.size=te.read_shift(4,"i"),he.size<0&&he.start<0&&(he.size=he.type=0,he.start=J,he.name=""),he.type===5?(U=he.start,F>0&&U!==J&&(S[U].name="!StreamData")):he.size>=4096?(he.storage="fat",S[he.start]===void 0&&(S[he.start]=_(w,he.start,S.fat_addrs,S.ssz)),S[he.start].name=he.name,he.content=S[he.start].data.slice(0,he.size)):(he.storage="minifat",he.size<0?he.size=0:U!==J&&he.start!==J&&S[U]&&(he.content=x(he,S[U].data,(S[K]||{}).data))),he.content&&xr(he.content,0),y[xe]=he,W.push(he)}}function I(v,S){return new Date((Lr(v,S+4)/1e7*Math.pow(2,32)+Lr(v,S)/1e7-11644473600)*1e3)}function L(v,S){return o(),l(c.readFileSync(v),S)}function D(v,S){var w=S&&S.type;switch(w||Fe&&Buffer.isBuffer(v)&&(w="buffer"),w||"base64"){case"file":return L(v,S);case"base64":return l(Br(Ur(v)),S);case"binary":return l(Br(v),S)}return l(v,S)}function k(v,S){var w=S||{},E=w.root||"Root Entry";if(v.FullPaths||(v.FullPaths=[]),v.FileIndex||(v.FileIndex=[]),v.FullPaths.length!==v.FileIndex.length)throw new Error("inconsistent CFB structure");v.FullPaths.length===0&&(v.FullPaths[0]=E+"/",v.FileIndex[0]={name:E,type:5}),w.CLSID&&(v.FileIndex[0].clsid=w.CLSID),R(v)}function R(v){var S="Sh33tJ5";if(!me.find(v,"/"+S)){var w=z(4);w[0]=55,w[1]=w[3]=50,w[2]=54,v.FileIndex.push({name:S,type:2,content:w,size:4,L:69,R:69,C:69}),v.FullPaths.push(v.FullPaths[0]+S),O(v)}}function O(v,S){k(v);for(var w=!1,E=!1,F=v.FullPaths.length-1;F>=0;--F){var y=v.FileIndex[F];switch(y.type){case 0:E?w=!0:(v.FileIndex.pop(),v.FullPaths.pop());break;case 1:case 2:case 5:E=!0,isNaN(y.R*y.L*y.C)&&(w=!0),y.R>-1&&y.L>-1&&y.R==y.L&&(w=!0);break;default:w=!0;break}}if(!(!w&&!S)){var W=new Date(1987,1,19),K=0,U=Object.create?Object.create(null):{},H=[];for(F=0;F<v.FullPaths.length;++F)U[v.FullPaths[F]]=!0,v.FileIndex[F].type!==0&&H.push([v.FullPaths[F],v.FileIndex[F]]);for(F=0;F<H.length;++F){var G=a(H[F][0]);E=U[G],E||(H.push([G,{name:n(G).replace("/",""),type:1,clsid:ue,ct:W,mt:W,content:null}]),U[G]=!0)}for(H.sort(function(xe,te){return r(xe[0],te[0])}),v.FullPaths=[],v.FileIndex=[],F=0;F<H.length;++F)v.FullPaths[F]=H[F][0],v.FileIndex[F]=H[F][1];for(F=0;F<H.length;++F){var ee=v.FileIndex[F],fe=v.FullPaths[F];if(ee.name=n(fe).replace("/",""),ee.L=ee.R=ee.C=-(ee.color=1),ee.size=ee.content?ee.content.length:0,ee.start=0,ee.clsid=ee.clsid||ue,F===0)ee.C=H.length>1?1:-1,ee.size=0,ee.type=5;else if(fe.slice(-1)=="/"){for(K=F+1;K<H.length&&a(v.FullPaths[K])!=fe;++K);for(ee.C=K>=H.length?-1:K,K=F+1;K<H.length&&a(v.FullPaths[K])!=a(fe);++K);ee.R=K>=H.length?-1:K,ee.type=1}else a(v.FullPaths[F+1]||"")==a(fe)&&(ee.R=F+1),ee.type=2}}}function X(v,S){var w=S||{};if(w.fileType=="mad")return Yc(v,w);switch(O(v),w.fileType){case"zip":return Hc(v,w)}var E=function(xe){for(var te=0,he=0,ke=0;ke<xe.FileIndex.length;++ke){var Xe=xe.FileIndex[ke];if(Xe.content){var ze=Xe.content.length;ze>0&&(ze<4096?te+=ze+63>>6:he+=ze+511>>9)}}for(var pr=xe.FullPaths.length+3>>2,Sa=te+7>>3,Fa=te+127>>7,ya=Sa+he+pr+Fa,Mt=ya+127>>7,H0=Mt<=109?0:Math.ceil((Mt-109)/127);ya+Mt+H0+127>>7>Mt;)H0=++Mt<=109?0:Math.ceil((Mt-109)/127);var xt=[1,H0,Mt,Fa,pr,he,te,0];return xe.FileIndex[0].size=te<<6,xt[7]=(xe.FileIndex[0].start=xt[0]+xt[1]+xt[2]+xt[3]+xt[4]+xt[5])+(xt[6]+7>>3),xt}(v),F=z(E[7]<<9),y=0,W=0;{for(y=0;y<8;++y)F.write_shift(1,Z[y]);for(y=0;y<8;++y)F.write_shift(2,0);for(F.write_shift(2,62),F.write_shift(2,3),F.write_shift(2,65534),F.write_shift(2,9),F.write_shift(2,6),y=0;y<3;++y)F.write_shift(2,0);for(F.write_shift(4,0),F.write_shift(4,E[2]),F.write_shift(4,E[0]+E[1]+E[2]+E[3]-1),F.write_shift(4,0),F.write_shift(4,4096),F.write_shift(4,E[3]?E[0]+E[1]+E[2]-1:J),F.write_shift(4,E[3]),F.write_shift(-4,E[1]?E[0]-1:J),F.write_shift(4,E[1]),y=0;y<109;++y)F.write_shift(-4,y<E[2]?E[1]+y:-1)}if(E[1])for(W=0;W<E[1];++W){for(;y<236+W*127;++y)F.write_shift(-4,y<E[2]?E[1]+y:-1);F.write_shift(-4,W===E[1]-1?J:W+1)}var K=function(xe){for(W+=xe;y<W-1;++y)F.write_shift(-4,y+1);xe&&(++y,F.write_shift(-4,J))};for(W=y=0,W+=E[1];y<W;++y)F.write_shift(-4,ce.DIFSECT);for(W+=E[2];y<W;++y)F.write_shift(-4,ce.FATSECT);K(E[3]),K(E[4]);for(var U=0,H=0,G=v.FileIndex[0];U<v.FileIndex.length;++U)G=v.FileIndex[U],G.content&&(H=G.content.length,!(H<4096)&&(G.start=W,K(H+511>>9)));for(K(E[6]+7>>3);F.l&511;)F.write_shift(-4,ce.ENDOFCHAIN);for(W=y=0,U=0;U<v.FileIndex.length;++U)G=v.FileIndex[U],G.content&&(H=G.content.length,!(!H||H>=4096)&&(G.start=W,K(H+63>>6)));for(;F.l&511;)F.write_shift(-4,ce.ENDOFCHAIN);for(y=0;y<E[4]<<2;++y){var ee=v.FullPaths[y];if(!ee||ee.length===0){for(U=0;U<17;++U)F.write_shift(4,0);for(U=0;U<3;++U)F.write_shift(4,-1);for(U=0;U<12;++U)F.write_shift(4,0);continue}G=v.FileIndex[y],y===0&&(G.start=G.size?G.start-1:J);var fe=y===0&&w.root||G.name;if(H=2*(fe.length+1),F.write_shift(64,fe,"utf16le"),F.write_shift(2,H),F.write_shift(1,G.type),F.write_shift(1,G.color),F.write_shift(-4,G.L),F.write_shift(-4,G.R),F.write_shift(-4,G.C),G.clsid)F.write_shift(16,G.clsid,"hex");else for(U=0;U<4;++U)F.write_shift(4,0);F.write_shift(4,G.state||0),F.write_shift(4,0),F.write_shift(4,0),F.write_shift(4,0),F.write_shift(4,0),F.write_shift(4,G.start),F.write_shift(4,G.size),F.write_shift(4,0)}for(y=1;y<v.FileIndex.length;++y)if(G=v.FileIndex[y],G.size>=4096)if(F.l=G.start+1<<9,Fe&&Buffer.isBuffer(G.content))G.content.copy(F,F.l,0,G.size),F.l+=G.size+511&-512;else{for(U=0;U<G.size;++U)F.write_shift(1,G.content[U]);for(;U&511;++U)F.write_shift(1,0)}for(y=1;y<v.FileIndex.length;++y)if(G=v.FileIndex[y],G.size>0&&G.size<4096)if(Fe&&Buffer.isBuffer(G.content))G.content.copy(F,F.l,0,G.size),F.l+=G.size+63&-64;else{for(U=0;U<G.size;++U)F.write_shift(1,G.content[U]);for(;U&63;++U)F.write_shift(1,0)}if(Fe)F.l=F.length;else for(;F.l<F.length;)F.write_shift(1,0);return F}function M(v,S){var w=v.FullPaths.map(function(U){return U.toUpperCase()}),E=w.map(function(U){var H=U.split("/");return H[H.length-(U.slice(-1)=="/"?2:1)]}),F=!1;S.charCodeAt(0)===47?(F=!0,S=w[0].slice(0,-1)+S):F=S.indexOf("/")!==-1;var y=S.toUpperCase(),W=F===!0?w.indexOf(y):E.indexOf(y);if(W!==-1)return v.FileIndex[W];var K=!y.match(Da);for(y=y.replace(Dr,""),K&&(y=y.replace(Da,"!")),W=0;W<w.length;++W)if((K?w[W].replace(Da,"!"):w[W]).replace(Dr,"")==y||(K?E[W].replace(Da,"!"):E[W]).replace(Dr,"")==y)return v.FileIndex[W];return null}var P=64,J=-2,le="d0cf11e0a1b11ae1",Z=[208,207,17,224,161,177,26,225],ue="00000000000000000000000000000000",ce={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:J,FREESECT:-1,HEADER_SIGNATURE:le,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:ue,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Ie(v,S,w){o();var E=X(v,w);c.writeFileSync(S,E)}function V(v){for(var S=new Array(v.length),w=0;w<v.length;++w)S[w]=String.fromCharCode(v[w]);return S.join("")}function pe(v,S){var w=X(v,S);switch(S&&S.type||"buffer"){case"file":return o(),c.writeFileSync(S.filename,w),w;case"binary":return typeof w=="string"?w:V(w);case"base64":return Ua(typeof w=="string"?w:V(w));case"buffer":if(Fe)return Buffer.isBuffer(w)?w:wt(w);case"array":return typeof w=="string"?Br(w):w}return w}var ve;function C(v){try{var S=v.InflateRaw,w=new S;if(w._processChunk(new Uint8Array([3,0]),w._finishFlushFlag),w.bytesRead)ve=v;else throw new Error("zlib does not expose bytesRead")}catch(E){console.error("cannot use native zlib: "+(E.message||E))}}function B(v,S){if(!ve)return si(v,S);var w=ve.InflateRaw,E=new w,F=E._processChunk(v.slice(v.l),E._finishFlushFlag);return v.l+=E.bytesRead,F}function b(v){return ve?ve.deflateRawSync(v):ye(v)}var N=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],j=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],ae=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ne(v){var S=(v<<1|v<<11)&139536|(v<<5|v<<15)&558144;return(S>>16|S>>8|S)&255}for(var re=typeof Uint8Array<"u",Q=re?new Uint8Array(256):[],Se=0;Se<256;++Se)Q[Se]=ne(Se);function A(v,S){var w=Q[v&255];return S<=8?w>>>8-S:(w=w<<8|Q[v>>8&255],S<=16?w>>>16-S:(w=w<<8|Q[v>>16&255],w>>>24-S))}function Ue(v,S){var w=S&7,E=S>>>3;return(v[E]|(w<=6?0:v[E+1]<<8))>>>w&3}function De(v,S){var w=S&7,E=S>>>3;return(v[E]|(w<=5?0:v[E+1]<<8))>>>w&7}function Me(v,S){var w=S&7,E=S>>>3;return(v[E]|(w<=4?0:v[E+1]<<8))>>>w&15}function Ce(v,S){var w=S&7,E=S>>>3;return(v[E]|(w<=3?0:v[E+1]<<8))>>>w&31}function oe(v,S){var w=S&7,E=S>>>3;return(v[E]|(w<=1?0:v[E+1]<<8))>>>w&127}function qe(v,S,w){var E=S&7,F=S>>>3,y=(1<<w)-1,W=v[F]>>>E;return w<8-E||(W|=v[F+1]<<8-E,w<16-E)||(W|=v[F+2]<<16-E,w<24-E)||(W|=v[F+3]<<24-E),W&y}function Wr(v,S,w){var E=S&7,F=S>>>3;return E<=5?v[F]|=(w&7)<<E:(v[F]|=w<<E&255,v[F+1]=(w&7)>>8-E),S+3}function tt(v,S,w){var E=S&7,F=S>>>3;return w=(w&1)<<E,v[F]|=w,S+1}function ut(v,S,w){var E=S&7,F=S>>>3;return w<<=E,v[F]|=w&255,w>>>=8,v[F+1]=w,S+8}function Ea(v,S,w){var E=S&7,F=S>>>3;return w<<=E,v[F]|=w&255,w>>>=8,v[F+1]=w&255,v[F+2]=w>>>8,S+16}function Tt(v,S){var w=v.length,E=2*w>S?2*w:S+5,F=0;if(w>=S)return v;if(Fe){var y=ui(E);if(v.copy)v.copy(y);else for(;F<v.length;++F)y[F]=v[F];return y}else if(re){var W=new Uint8Array(E);if(W.set)W.set(v);else for(;F<w;++F)W[F]=v[F];return W}return v.length=E,v}function Nr(v){for(var S=new Array(v),w=0;w<v;++w)S[w]=0;return S}function ht(v,S,w){var E=1,F=0,y=0,W=0,K=0,U=v.length,H=re?new Uint16Array(32):Nr(32);for(y=0;y<32;++y)H[y]=0;for(y=U;y<w;++y)v[y]=0;U=v.length;var G=re?new Uint16Array(U):Nr(U);for(y=0;y<U;++y)H[F=v[y]]++,E<F&&(E=F),G[y]=0;for(H[0]=0,y=1;y<=E;++y)H[y+16]=K=K+H[y-1]<<1;for(y=0;y<U;++y)K=v[y],K!=0&&(G[y]=H[K+16]++);var ee=0;for(y=0;y<U;++y)if(ee=v[y],ee!=0)for(K=A(G[y],E)>>E-ee,W=(1<<E+4-ee)-1;W>=0;--W)S[K|W<<ee]=ee&15|y<<4;return E}var St=re?new Uint16Array(512):Nr(512),Ta=re?new Uint16Array(32):Nr(32);if(!re){for(var Fr=0;Fr<512;++Fr)St[Fr]=0;for(Fr=0;Fr<32;++Fr)Ta[Fr]=0}(function(){for(var v=[],S=0;S<32;S++)v.push(5);ht(v,Ta,32);var w=[];for(S=0;S<=143;S++)w.push(8);for(;S<=255;S++)w.push(9);for(;S<=279;S++)w.push(7);for(;S<=287;S++)w.push(8);ht(w,St,288)})();var at=function(){for(var S=re?new Uint8Array(32768):[],w=0,E=0;w<ae.length-1;++w)for(;E<ae[w+1];++E)S[E]=w;for(;E<32768;++E)S[E]=29;var F=re?new Uint8Array(259):[];for(w=0,E=0;w<j.length-1;++w)for(;E<j[w+1];++E)F[E]=w;function y(K,U){for(var H=0;H<K.length;){var G=Math.min(65535,K.length-H),ee=H+G==K.length;for(U.write_shift(1,+ee),U.write_shift(2,G),U.write_shift(2,~G&65535);G-- >0;)U[U.l++]=K[H++]}return U.l}function W(K,U){for(var H=0,G=0,ee=re?new Uint16Array(32768):[];G<K.length;){var fe=Math.min(65535,K.length-G);if(fe<10){for(H=Wr(U,H,+(G+fe==K.length)),H&7&&(H+=8-(H&7)),U.l=H/8|0,U.write_shift(2,fe),U.write_shift(2,~fe&65535);fe-- >0;)U[U.l++]=K[G++];H=U.l*8;continue}H=Wr(U,H,+(G+fe==K.length)+2);for(var xe=0;fe-- >0;){var te=K[G];xe=(xe<<5^te)&32767;var he=-1,ke=0;if((he=ee[xe])&&(he|=G&-32768,he>G&&(he-=32768),he<G))for(;K[he+ke]==K[G+ke]&&ke<250;)++ke;if(ke>2){te=F[ke],te<=22?H=ut(U,H,Q[te+1]>>1)-1:(ut(U,H,3),H+=5,ut(U,H,Q[te-23]>>5),H+=3);var Xe=te<8?0:te-4>>2;Xe>0&&(Ea(U,H,ke-j[te]),H+=Xe),te=S[G-he],H=ut(U,H,Q[te]>>3),H-=3;var ze=te<4?0:te-2>>1;ze>0&&(Ea(U,H,G-he-ae[te]),H+=ze);for(var pr=0;pr<ke;++pr)ee[xe]=G&32767,xe=(xe<<5^K[G])&32767,++G;fe-=ke-1}else te<=143?te=te+48:H=tt(U,H,1),H=ut(U,H,Q[te]),ee[xe]=G&32767,++G}H=ut(U,H,0)-1}return U.l=(H+7)/8|0,U.l}return function(U,H){return U.length<8?y(U,H):W(U,H)}}();function ye(v){var S=z(50+Math.floor(v.length*1.1)),w=at(v,S);return S.slice(0,w)}var Ze=re?new Uint16Array(32768):Nr(32768),Vr=re?new Uint16Array(32768):Nr(32768),ir=re?new Uint16Array(128):Nr(128),Bt=1,ii=1;function Uc(v,S){var w=Ce(v,S)+257;S+=5;var E=Ce(v,S)+1;S+=5;var F=Me(v,S)+4;S+=4;for(var y=0,W=re?new Uint8Array(19):Nr(19),K=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],U=1,H=re?new Uint8Array(8):Nr(8),G=re?new Uint8Array(8):Nr(8),ee=W.length,fe=0;fe<F;++fe)W[N[fe]]=y=De(v,S),U<y&&(U=y),H[y]++,S+=3;var xe=0;for(H[0]=0,fe=1;fe<=U;++fe)G[fe]=xe=xe+H[fe-1]<<1;for(fe=0;fe<ee;++fe)(xe=W[fe])!=0&&(K[fe]=G[xe]++);var te=0;for(fe=0;fe<ee;++fe)if(te=W[fe],te!=0){xe=Q[K[fe]]>>8-te;for(var he=(1<<7-te)-1;he>=0;--he)ir[xe|he<<te]=te&7|fe<<3}var ke=[];for(U=1;ke.length<w+E;)switch(xe=ir[oe(v,S)],S+=xe&7,xe>>>=3){case 16:for(y=3+Ue(v,S),S+=2,xe=ke[ke.length-1];y-- >0;)ke.push(xe);break;case 17:for(y=3+De(v,S),S+=3;y-- >0;)ke.push(0);break;case 18:for(y=11+oe(v,S),S+=7;y-- >0;)ke.push(0);break;default:ke.push(xe),U<xe&&(U=xe);break}var Xe=ke.slice(0,w),ze=ke.slice(w);for(fe=w;fe<286;++fe)Xe[fe]=0;for(fe=E;fe<30;++fe)ze[fe]=0;return Bt=ht(Xe,Ze,286),ii=ht(ze,Vr,30),S}function Wc(v,S){if(v[0]==3&&!(v[1]&3))return[It(S),2];for(var w=0,E=0,F=ui(S||1<<18),y=0,W=F.length>>>0,K=0,U=0;!(E&1);){if(E=De(v,w),w+=3,E>>>1)E>>1==1?(K=9,U=5):(w=Uc(v,w),K=Bt,U=ii);else{w&7&&(w+=8-(w&7));var H=v[w>>>3]|v[(w>>>3)+1]<<8;if(w+=32,H>0)for(!S&&W<y+H&&(F=Tt(F,y+H),W=F.length);H-- >0;)F[y++]=v[w>>>3],w+=8;continue}for(;;){!S&&W<y+32767&&(F=Tt(F,y+32767),W=F.length);var G=qe(v,w,K),ee=E>>>1==1?St[G]:Ze[G];if(w+=ee&15,ee>>>=4,!(ee>>>8&255))F[y++]=ee;else{if(ee==256)break;ee-=257;var fe=ee<8?0:ee-4>>2;fe>5&&(fe=0);var xe=y+j[ee];fe>0&&(xe+=qe(v,w,fe),w+=fe),G=qe(v,w,U),ee=E>>>1==1?Ta[G]:Vr[G],w+=ee&15,ee>>>=4;var te=ee<4?0:ee-2>>1,he=ae[ee];for(te>0&&(he+=qe(v,w,te),w+=te),!S&&W<xe&&(F=Tt(F,xe+100),W=F.length);y<xe;)F[y]=F[y-he],++y}}}return S?[F,w+7>>>3]:[F.slice(0,y),w+7>>>3]}function si(v,S){var w=v.slice(v.l||0),E=Wc(w,S);return v.l+=E[1],E[0]}function fi(v,S){if(v)typeof console<"u"&&console.error(S);else throw new Error(S)}function ci(v,S){var w=v;xr(w,0);var E=[],F=[],y={FileIndex:E,FullPaths:F};k(y,{root:S.root});for(var W=w.length-4;(w[W]!=80||w[W+1]!=75||w[W+2]!=5||w[W+3]!=6)&&W>=0;)--W;w.l=W+4,w.l+=4;var K=w.read_shift(2);w.l+=6;var U=w.read_shift(4);for(w.l=U,W=0;W<K;++W){w.l+=20;var H=w.read_shift(4),G=w.read_shift(4),ee=w.read_shift(2),fe=w.read_shift(2),xe=w.read_shift(2);w.l+=8;var te=w.read_shift(4),he=f(w.slice(w.l+ee,w.l+ee+fe));w.l+=ee+fe+xe;var ke=w.l;w.l=te+4,Vc(w,H,G,y,he),w.l=ke}return y}function Vc(v,S,w,E,F){v.l+=2;var y=v.read_shift(2),W=v.read_shift(2),K=s(v);if(y&8257)throw new Error("Unsupported ZIP encryption");for(var U=v.read_shift(4),H=v.read_shift(4),G=v.read_shift(4),ee=v.read_shift(2),fe=v.read_shift(2),xe="",te=0;te<ee;++te)xe+=String.fromCharCode(v[v.l++]);if(fe){var he=f(v.slice(v.l,v.l+fe));(he[21589]||{}).mt&&(K=he[21589].mt),((F||{})[21589]||{}).mt&&(K=F[21589].mt)}v.l+=fe;var ke=v.slice(v.l,v.l+H);switch(W){case 8:ke=B(v,G);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+W)}var Xe=!1;y&8&&(U=v.read_shift(4),U==134695760&&(U=v.read_shift(4),Xe=!0),H=v.read_shift(4),G=v.read_shift(4)),H!=S&&fi(Xe,"Bad compressed size: "+S+" != "+H),G!=w&&fi(Xe,"Bad uncompressed size: "+w+" != "+G),V0(E,xe,ke,{unsafe:!0,mt:K})}function Hc(v,S){var w=S||{},E=[],F=[],y=z(1),W=w.compression?8:0,K=0,U=0,H=0,G=0,ee=0,fe=v.FullPaths[0],xe=fe,te=v.FileIndex[0],he=[],ke=0;for(U=1;U<v.FullPaths.length;++U)if(xe=v.FullPaths[U].slice(fe.length),te=v.FileIndex[U],!(!te.size||!te.content||xe=="Sh33tJ5")){var Xe=G,ze=z(xe.length);for(H=0;H<xe.length;++H)ze.write_shift(1,xe.charCodeAt(H)&127);ze=ze.slice(0,ze.l),he[ee]=nl.buf(te.content,0);var pr=te.content;W==8&&(pr=b(pr)),y=z(30),y.write_shift(4,67324752),y.write_shift(2,20),y.write_shift(2,K),y.write_shift(2,W),te.mt?i(y,te.mt):y.write_shift(4,0),y.write_shift(-4,he[ee]),y.write_shift(4,pr.length),y.write_shift(4,te.content.length),y.write_shift(2,ze.length),y.write_shift(2,0),G+=y.length,E.push(y),G+=ze.length,E.push(ze),G+=pr.length,E.push(pr),y=z(46),y.write_shift(4,33639248),y.write_shift(2,0),y.write_shift(2,20),y.write_shift(2,K),y.write_shift(2,W),y.write_shift(4,0),y.write_shift(-4,he[ee]),y.write_shift(4,pr.length),y.write_shift(4,te.content.length),y.write_shift(2,ze.length),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(4,0),y.write_shift(4,Xe),ke+=y.l,F.push(y),ke+=ze.length,F.push(ze),++ee}return y=z(22),y.write_shift(4,101010256),y.write_shift(2,0),y.write_shift(2,0),y.write_shift(2,ee),y.write_shift(2,ee),y.write_shift(4,ke),y.write_shift(4,G),y.write_shift(2,0),or([or(E),or(F),y])}var s0={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Gc(v,S){if(v.ctype)return v.ctype;var w=v.name||"",E=w.match(/\.([^\.]+)$/);return E&&s0[E[1]]||S&&(E=(w=S).match(/[\.\\]([^\.\\])+$/),E&&s0[E[1]])?s0[E[1]]:"application/octet-stream"}function Xc(v){for(var S=Ua(v),w=[],E=0;E<S.length;E+=76)w.push(S.slice(E,E+76));return w.join(`\r
`)+`\r
`}function zc(v){var S=v.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(H){var G=H.charCodeAt(0).toString(16).toUpperCase();return"="+(G.length==1?"0"+G:G)});S=S.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),S.charAt(0)==`
`&&(S="=0D"+S.slice(1)),S=S.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var w=[],E=S.split(`\r
`),F=0;F<E.length;++F){var y=E[F];if(y.length==0){w.push("");continue}for(var W=0;W<y.length;){var K=76,U=y.slice(W,W+K);U.charAt(K-1)=="="?K--:U.charAt(K-2)=="="?K-=2:U.charAt(K-3)=="="&&(K-=3),U=y.slice(W,W+K),W+=K,W<y.length&&(U+="="),w.push(U)}}return w.join(`\r
`)}function $c(v){for(var S=[],w=0;w<v.length;++w){for(var E=v[w];w<=v.length&&E.charAt(E.length-1)=="=";)E=E.slice(0,E.length-1)+v[++w];S.push(E)}for(var F=0;F<S.length;++F)S[F]=S[F].replace(/[=][0-9A-Fa-f]{2}/g,function(y){return String.fromCharCode(parseInt(y.slice(1),16))});return Br(S.join(`\r
`))}function Kc(v,S,w){for(var E="",F="",y="",W,K=0;K<10;++K){var U=S[K];if(!U||U.match(/^\s*$/))break;var H=U.match(/^(.*?):\s*([^\s].*)$/);if(H)switch(H[1].toLowerCase()){case"content-location":E=H[2].trim();break;case"content-type":y=H[2].trim();break;case"content-transfer-encoding":F=H[2].trim();break}}switch(++K,F.toLowerCase()){case"base64":W=Br(Ur(S.slice(K).join("")));break;case"quoted-printable":W=$c(S.slice(K));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+F)}var G=V0(v,E.slice(w.length),W,{unsafe:!0});y&&(G.ctype=y)}function jc(v,S){if(V(v.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var w=S&&S.root||"",E=(Fe&&Buffer.isBuffer(v)?v.toString("binary"):V(v)).split(`\r
`),F=0,y="";for(F=0;F<E.length;++F)if(y=E[F],!!/^Content-Location:/i.test(y)&&(y=y.slice(y.indexOf("file")),w||(w=y.slice(0,y.lastIndexOf("/")+1)),y.slice(0,w.length)!=w))for(;w.length>0&&(w=w.slice(0,w.length-1),w=w.slice(0,w.lastIndexOf("/")+1),y.slice(0,w.length)!=w););var W=(E[1]||"").match(/boundary="(.*?)"/);if(!W)throw new Error("MAD cannot find boundary");var K="--"+(W[1]||""),U=[],H=[],G={FileIndex:U,FullPaths:H};k(G);var ee,fe=0;for(F=0;F<E.length;++F){var xe=E[F];xe!==K&&xe!==K+"--"||(fe++&&Kc(G,E.slice(ee,F),w),ee=F)}return G}function Yc(v,S){var w=S||{},E=w.boundary||"SheetJS";E="------="+E;for(var F=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+E.slice(2)+'"',"","",""],y=v.FullPaths[0],W=y,K=v.FileIndex[0],U=1;U<v.FullPaths.length;++U)if(W=v.FullPaths[U].slice(y.length),K=v.FileIndex[U],!(!K.size||!K.content||W=="Sh33tJ5")){W=W.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(ke){return"_x"+ke.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(ke){return"_u"+ke.charCodeAt(0).toString(16)+"_"});for(var H=K.content,G=Fe&&Buffer.isBuffer(H)?H.toString("binary"):V(H),ee=0,fe=Math.min(1024,G.length),xe=0,te=0;te<=fe;++te)(xe=G.charCodeAt(te))>=32&&xe<128&&++ee;var he=ee>=fe*4/5;F.push(E),F.push("Content-Location: "+(w.root||"file:///C:/SheetJS/")+W),F.push("Content-Transfer-Encoding: "+(he?"quoted-printable":"base64")),F.push("Content-Type: "+Gc(K,W)),F.push(""),F.push(he?zc(G):Xc(G))}return F.push(E+`--\r
`),F.join(`\r
`)}function Jc(v){var S={};return k(S,v),S}function V0(v,S,w,E){var F=E&&E.unsafe;F||k(v);var y=!F&&me.find(v,S);if(!y){var W=v.FullPaths[0];S.slice(0,W.length)==W?W=S:(W.slice(-1)!="/"&&(W+="/"),W=(W+S).replace("//","/")),y={name:n(S),type:2},v.FileIndex.push(y),v.FullPaths.push(W),F||me.utils.cfb_gc(v)}return y.content=w,y.size=w?w.length:0,E&&(E.CLSID&&(y.clsid=E.CLSID),E.mt&&(y.mt=E.mt),E.ct&&(y.ct=E.ct)),y}function qc(v,S){k(v);var w=me.find(v,S);if(w){for(var E=0;E<v.FileIndex.length;++E)if(v.FileIndex[E]==w)return v.FileIndex.splice(E,1),v.FullPaths.splice(E,1),!0}return!1}function Zc(v,S,w){k(v);var E=me.find(v,S);if(E){for(var F=0;F<v.FileIndex.length;++F)if(v.FileIndex[F]==E)return v.FileIndex[F].name=n(w),v.FullPaths[F]=w,!0}return!1}function Qc(v){O(v,!0)}return t.find=M,t.read=D,t.parse=l,t.write=pe,t.writeFile=Ie,t.utils={cfb_new:Jc,cfb_add:V0,cfb_del:qc,cfb_mov:Zc,cfb_gc:Qc,ReadShift:Oa,CheckField:lf,prep_blob:xr,bconcat:or,use_zlib:C,_deflateRaw:ye,_inflateRaw:si,consts:ce},t}();function il(e){return typeof e=="string"?b0(e):Array.isArray(e)?Oo(e):e}function Za(e,t,r){if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=b0(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a=r=="utf8"?ct(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(a,e);if(typeof Blob<"u"){var n=new Blob([il(a)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs<"u")return saveAs(n,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(n);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=Nt(t)),f.write(t),f.close(),t}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function sl(e){if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function Je(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function _i(e,t){for(var r=[],a=Je(e),n=0;n!==a.length;++n)r[e[a[n]][t]]==null&&(r[e[a[n]][t]]=a[n]);return r}function N0(e){for(var t=[],r=Je(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function P0(e){for(var t=[],r=Je(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}function fl(e){for(var t=[],r=Je(e),a=0;a!==r.length;++a)t[e[r[a]]]==null&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}var T0=new Date(1899,11,30,0,0,0);function fr(e,t){var r=e.getTime();t&&(r-=1462*24*60*60*1e3);var a=T0.getTime()+(e.getTimezoneOffset()-T0.getTimezoneOffset())*6e4;return(r-a)/(24*60*60*1e3)}var Vs=new Date,cl=T0.getTime()+(Vs.getTimezoneOffset()-T0.getTimezoneOffset())*6e4,wi=Vs.getTimezoneOffset();function L0(e){var t=new Date;return t.setTime(e*24*60*60*1e3+cl),t.getTimezoneOffset()!==wi&&t.setTime(t.getTime()+(t.getTimezoneOffset()-wi)*6e4),t}function ol(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=n.length;++i)if(n[i]){switch(r=1,i>3&&(a=!0),n[i].slice(n[i].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[i].slice(n[i].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[i],10)}return t}var ki=new Date("2017-02-19T19:06:09.000Z"),Hs=isNaN(ki.getFullYear())?new Date("2/19/17"):ki,ll=Hs.getFullYear()==2017;function Ve(e,t){var r=new Date(e);if(ll)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Hs.getFullYear()==1917&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function jt(e,t){if(Fe&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return ct(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return ct(Ds(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(t){if(e[0]==255&&e[1]==254)return ct(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return ct(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function He(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=He(e[r]));return t}function je(e,t){for(var r="";r.length<t;)r+=e;return r}function et(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(a))||(a=a.replace(/[(](.*)[)]/,function(n,i){return r=-r,i}),!isNaN(t=Number(a)))?t/r:t}var ul=["january","february","march","april","may","june","july","august","september","october","november","december"];function pa(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&ul.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||i>1)&&a!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}var hl=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,a,n){if(e||typeof a=="string")return r.split(a);for(var i=r.split(a),s=[i[0]],f=1;f<i.length;++f)s.push(n),s.push(i[f]);return s}}();function Gs(e){return e?e.content&&e.type?jt(e.content,!0):e.data?Ca(e.data):e.asNodeBuffer&&Fe?Ca(e.asNodeBuffer().toString("binary")):e.asBinary?Ca(e.asBinary()):e._data&&e._data.getContent?Ca(jt(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Xs(e){if(!e)return null;if(e.data)return oi(e.data);if(e.asNodeBuffer&&Fe)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return typeof t=="string"?oi(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function xl(e){return e&&e.name.slice(-4)===".bin"?Xs(e):Gs(e)}function $r(e,t){for(var r=e.FullPaths||Je(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),i=0;i<r.length;++i){var s=r[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==s||n==s)return e.files?e.files[r[i]]:e.FileIndex[i]}return null}function Sn(e,t){var r=$r(e,t);if(r==null)throw new Error("Cannot find file "+t+" in zip");return r}function ar(e,t,r){if(!r)return xl(Sn(e,t));if(!t)return null;try{return ar(e,t)}catch{return null}}function Mr(e,t,r){if(!r)return Gs(Sn(e,t));if(!t)return null;try{return Mr(e,t)}catch{return null}}function zs(e,t,r){if(!r)return Xs(Sn(e,t));if(!t)return null;try{return zs(e,t)}catch{return null}}function Ei(e){for(var t=e.FullPaths||Je(e.files),r=[],a=0;a<t.length;++a)t[a].slice(-1)!="/"&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function Te(e,t,r){if(e.FullPaths){if(typeof r=="string"){var a;return Fe?a=wt(r):a=bo(r),me.utils.cfb_add(e,t,a)}me.utils.cfb_add(e,t,r)}else e.file(t,r)}function Fn(){return me.utils.cfb_new()}function $s(e,t){switch(t.type){case"base64":return me.read(e,{type:"base64"});case"binary":return me.read(e,{type:"binary"});case"buffer":case"array":return me.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function Ia(e,t){if(e.charAt(0)=="/")return e.slice(1);var r=t.split("/");t.slice(-1)!="/"&&r.pop();for(var a=e.split("/");a.length!==0;){var n=a.shift();n===".."?r.pop():n!=="."&&r.push(n)}return r.join("/")}var rr=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,dl=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,Ti=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,pl=/<[^>]*>/g,Sr=rr.match(Ti)?Ti:pl,ml=/<\w*:/,vl=/<(\/?)\w+:/;function we(e,t,r){for(var a={},n=0,i=0;n!==e.length&&!((i=e.charCodeAt(n))===32||i===10||i===13);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var s=e.match(dl),f=0,c="",o=0,l="",u="",h=1;if(s)for(o=0;o!=s.length;++o){for(u=s[o],i=0;i!=u.length&&u.charCodeAt(i)!==61;++i);for(l=u.slice(0,i).trim();u.charCodeAt(i+1)==32;)++i;for(h=(n=u.charCodeAt(i+1))==34||n==39?1:0,c=u.slice(i+1+h,u.length-h),f=0;f!=l.length&&l.charCodeAt(f)!==58;++f);if(f===l.length)l.indexOf("_")>0&&(l=l.slice(0,l.indexOf("_"))),a[l]=c,r||(a[l.toLowerCase()]=c);else{var d=(f===5&&l.slice(0,5)==="xmlns"?"xmlns":"")+l.slice(f+1);if(a[d]&&l.slice(f-3,f)=="ext")continue;a[d]=c,r||(a[d.toLowerCase()]=c)}}return a}function ot(e){return e.replace(vl,"<$1")}var Ks={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},yn=N0(Ks),Oe=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",i=n.indexOf("<![CDATA[");if(i==-1)return n.replace(e,function(f,c){return Ks[f]||String.fromCharCode(parseInt(c,f.indexOf("x")>-1?16:10))||f}).replace(t,function(f,c){return String.fromCharCode(parseInt(c,16))});var s=n.indexOf("]]>");return r(n.slice(0,i))+n.slice(i+9,s)+r(n.slice(s+3))}}(),An=/[&<>'"]/g,gl=/[\u0000-\u0008\u000b-\u001f]/g;function Le(e){var t=e+"";return t.replace(An,function(r){return yn[r]}).replace(gl,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function Si(e){return Le(e).replace(/ /g,"_x0020_")}var js=/[\u0000-\u001f]/g;function Cn(e){var t=e+"";return t.replace(An,function(r){return yn[r]}).replace(/\n/g,"<br/>").replace(js,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function _l(e){var t=e+"";return t.replace(An,function(r){return yn[r]}).replace(js,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}var Fi=function(){var e=/&#(\d+);/g;function t(r,a){return String.fromCharCode(parseInt(a,10))}return function(a){return a.replace(e,t)}}();function wl(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function We(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function X0(e){for(var t="",r=0,a=0,n=0,i=0,s=0,f=0;r<e.length;){if(a=e.charCodeAt(r++),a<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){s=(a&31)<<6,s|=n&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),a<240){t+=String.fromCharCode((a&15)<<12|(n&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((a&7)<<18|(n&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function yi(e){var t=It(2*e.length),r,a,n=1,i=0,s=0,f;for(a=0;a<e.length;a+=n)n=1,(f=e.charCodeAt(a))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(a+1)&63),n=2):f<240?(r=(f&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63),n=3):(n=4,r=(f&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function Ai(e){return wt(e,"binary").toString("utf8")}var c0="foo bar bazâð£",Be=Fe&&(Ai(c0)==X0(c0)&&Ai||yi(c0)==X0(c0)&&yi)||X0,ct=Fe?function(e){return wt(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(a&63)));break;case(a>=55296&&a<57344):a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)))}return t.join("")},Va=function(){var e={};return function(r,a){var n=r+"|"+(a||"");return e[n]?e[n]:e[n]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",a||"")}}(),Ys=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var a=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}(),kl=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),El=/<\/?(?:vt:)?variant>/g,Tl=/<(?:vt:)([^>]*)>([\s\S]*)</;function Ci(e,t){var r=we(e),a=e.match(kl(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(i){var s=i.replace(El,"").match(Tl);s&&n.push({v:Be(s[2]),t:s[1]})}),n}var Js=/(^\s|\s$|\n)/;function dr(e,t){return"<"+e+(t.match(Js)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Ha(e){return Je(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function ie(e,t,r){return"<"+e+(r!=null?Ha(r):"")+(t!=null?(t.match(Js)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function an(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Sl(e,t){switch(typeof e){case"string":var r=ie("vt:lpwstr",Le(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return ie((e|0)==e?"vt:i4":"vt:r8",Le(String(e)));case"boolean":return ie("vt:bool",e?"true":"false")}if(e instanceof Date)return ie("vt:filetime",an(e));throw new Error("Unable to serialize "+e)}function Dn(e){if(Fe&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return Be(Nt(kn(e)));throw new Error("Bad input format: expected Buffer or string")}var Ga=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,sr={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},Qt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Pr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Fl(e,t){for(var r=1-2*(e[t+7]>>>7),a=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),n=e[t+6]&15,i=5;i>=0;--i)n=n*256+e[t+i];return a==2047?n==0?r*(1/0):NaN:(a==0?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}function yl(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,i=0,s=a?-t:t;isFinite(s)?s==0?n=i=0:(n=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-n),n<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?n=-1022:(i-=Math.pow(2,52),n+=1023)):(n=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(n&15)<<4|i&15,e[r+7]=n>>4|a}var Di=function(e){for(var t=[],r=10240,a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,i=e[0][a].length;n<i;n+=r)t.push.apply(t,e[0][a].slice(n,n+r));return t},Ii=Fe?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:wt(t)})):Di(e)}:Di,Oi=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(pt(e,n)));return a.join("").replace(Dr,"")},In=Fe?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(Dr,""):Oi(e,t,r)}:Oi,bi=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},qs=Fe?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):bi(e,t,r)}:bi,Ri=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(ca(e,n)));return a.join("")},Qa=Fe?function(t,r,a){return Buffer.isBuffer(t)?t.toString("utf8",r,a):Ri(t,r,a)}:Ri,Zs=function(e,t){var r=Lr(e,t);return r>0?Qa(e,t+4,t+4+r-1):""},Qs=Zs,ef=function(e,t){var r=Lr(e,t);return r>0?Qa(e,t+4,t+4+r-1):""},rf=ef,tf=function(e,t){var r=2*Lr(e,t);return r>0?Qa(e,t+4,t+4+r-1):""},af=tf,nf=function(t,r){var a=Lr(t,r);return a>0?In(t,r+4,r+4+a):""},sf=nf,ff=function(e,t){var r=Lr(e,t);return r>0?Qa(e,t+4,t+4+r):""},cf=ff,of=function(e,t){return Fl(e,t)},S0=of,On=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};Fe&&(Qs=function(t,r){if(!Buffer.isBuffer(t))return Zs(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},rf=function(t,r){if(!Buffer.isBuffer(t))return ef(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},af=function(t,r){if(!Buffer.isBuffer(t))return tf(t,r);var a=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a-1)},sf=function(t,r){if(!Buffer.isBuffer(t))return nf(t,r);var a=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a)},cf=function(t,r){if(!Buffer.isBuffer(t))return ff(t,r);var a=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+a)},S0=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):of(t,r)},On=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var ca=function(e,t){return e[t]},pt=function(e,t){return e[t+1]*256+e[t]},Al=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Lr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Vt=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Cl=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Oa(e,t){var r="",a,n,i=[],s,f,c,o;switch(t){case"dbcs":if(o=this.l,Fe&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)r+=String.fromCharCode(pt(this,o)),o+=2;e*=2;break;case"utf8":r=Qa(this,this.l,this.l+e);break;case"utf16le":e*=2,r=In(this,this.l,this.l+e);break;case"wstr":return Oa.call(this,e,"dbcs");case"lpstr-ansi":r=Qs(this,this.l),e=4+Lr(this,this.l);break;case"lpstr-cp":r=rf(this,this.l),e=4+Lr(this,this.l);break;case"lpwstr":r=af(this,this.l),e=4+2*Lr(this,this.l);break;case"lpp4":e=4+Lr(this,this.l),r=sf(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Lr(this,this.l),r=cf(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=ca(this,this.l+e++))!==0;)i.push(f0(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=pt(this,this.l+e))!==0;)i.push(f0(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",o=this.l,c=0;c<e;++c){if(this.lens&&this.lens.indexOf(o)!==-1)return s=ca(this,o),this.l=o+1,f=Oa.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(f0(pt(this,o))),o+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",o=this.l,c=0;c!=e;++c){if(this.lens&&this.lens.indexOf(o)!==-1)return s=ca(this,o),this.l=o+1,f=Oa.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(f0(ca(this,o))),o+=1}r=i.join("");break;default:switch(e){case 1:return a=ca(this,this.l),this.l++,a;case 2:return a=(t==="i"?Al:pt)(this,this.l),this.l+=2,a;case 4:case-4:return t==="i"||!(this[this.l+3]&128)?(a=(e>0?Vt:Cl)(this,this.l),this.l+=4,a):(n=Lr(this,this.l),this.l+=4,n);case 8:case-8:if(t==="f")return e==8?n=S0(this,this.l):n=S0([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:r=qs(this,this.l,e);break}}return this.l+=e,r}var Dl=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Il=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Ol=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function bl(e,t,r){var a=0,n=0;if(r==="dbcs"){for(n=0;n!=t.length;++n)Ol(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=t.charCodeAt(n)&255;a=t.length}else if(r==="hex"){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var s=t.charCodeAt(n);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=t&255;break;case 2:a=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:a=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:a=4,Dl(this,t,this.l);break;case 8:if(a=8,r==="f"){yl(this,t,this.l);break}case 16:break;case-4:a=4,Il(this,t,this.l);break}return this.l+=a,this}function lf(e,t){var r=qs(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function xr(e,t){e.l=t,e.read_shift=Oa,e.chk=lf,e.write_shift=bl}function Tr(e,t){e.l+=t}function z(e){var t=It(e);return xr(t,0),t}function kt(e,t,r){if(e){var a,n,i;xr(e,e.l||0);for(var s=e.length,f=0,c=0;e.l<s;){f=e.read_shift(1),f&128&&(f=(f&127)+((e.read_shift(1)&127)<<7));var o=Ya[f]||Ya[65535];for(a=e.read_shift(1),i=a&127,n=1;n<4&&a&128;++n)i+=((a=e.read_shift(1))&127)<<7*n;c=e.l+i;var l=o.f&&o.f(e,i,r);if(e.l=c,t(l,o,f))return}}}function br(){var e=[],t=Fe?256:2048,r=function(o){var l=z(o);return xr(l,0),l},a=r(t),n=function(){a&&(a.length>a.l&&(a=a.slice(0,a.l),a.l=a.length),a.length>0&&e.push(a),a=null)},i=function(o){return a&&o<a.length-a.l?a:(n(),a=r(Math.max(o+1,t)))},s=function(){return n(),or(e)},f=function(o){n(),a=o,a.l==null&&(a.l=a.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function Y(e,t,r,a){var n=+t,i;if(!isNaN(n)){a||(a=Ya[n].p||(r||[]).length||0),i=1+(n>=128?1:0)+1,a>=128&&++i,a>=16384&&++i,a>=2097152&&++i;var s=e.next(i);n<=127?s.write_shift(1,n):(s.write_shift(1,(n&127)+128),s.write_shift(1,n>>7));for(var f=0;f!=4;++f)if(a>=128)s.write_shift(1,(a&127)+128),a>>=7;else{s.write_shift(1,a);break}a>0&&On(r)&&e.push(r)}}function ba(e,t,r){var a=He(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Ni(e,t,r){var a=He(e);return a.s=ba(a.s,t.s,r),a.e=ba(a.e,t.s,r),a}function Ra(e,t){if(e.cRel&&e.c<0)for(e=He(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=He(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=_e(e);return!e.cRel&&e.cRel!=null&&(r=Pl(r)),!e.rRel&&e.rRel!=null&&(r=Rl(r)),r}function z0(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+$e(e.s.c)+":"+(e.e.cRel?"":"$")+$e(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+er(e.s.r)+":"+(e.e.rRel?"":"$")+er(e.e.r):Ra(e.s,t.biff)+":"+Ra(e.e,t.biff)}function bn(e){return parseInt(Nl(e),10)-1}function er(e){return""+(e+1)}function Rl(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Nl(e){return e.replace(/\$(\d+)$/,"$1")}function Rn(e){for(var t=Ll(e),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function $e(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Pl(e){return e.replace(/^([A-Z])/,"$$$1")}function Ll(e){return e.replace(/^\$([A-Z])/,"$1")}function Bl(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Ke(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function _e(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Rr(e){var t=e.indexOf(":");return t==-1?{s:Ke(e),e:Ke(e)}:{s:Ke(e.slice(0,t)),e:Ke(e.slice(t+1))}}function Ee(e,t){return typeof t>"u"||typeof t=="number"?Ee(e.s,e.e):(typeof e!="string"&&(e=_e(e)),typeof t!="string"&&(t=_e(t)),e==t?e:e+":"+t)}function be(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,i=e.length;for(r=0;a<i&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<i&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===i||n!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=i&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=i&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function Pi(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=Yr(e.z,r?fr(t):t)}catch{}try{return e.w=Yr((e.XF||{}).numFmtId||(r?14:0),r?fr(t):t)}catch{return""+t}}function _t(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Et[e.v]||e.v:t==null?Pi(e,e.v):Pi(e,t))}function Pt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function uf(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,i=e||(n?[]:{}),s=0,f=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var c=typeof a.origin=="string"?Ke(a.origin):a.origin;s=c.r,f=c.c}i["!ref"]||(i["!ref"]="A1:A1")}var o={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var l=be(i["!ref"]);o.s.c=l.s.c,o.s.r=l.s.r,o.e.c=Math.max(o.e.c,l.e.c),o.e.r=Math.max(o.e.r,l.e.r),s==-1&&(o.e.r=s=l.e.r+1)}for(var u=0;u!=t.length;++u)if(t[u]){if(!Array.isArray(t[u]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=t[u].length;++h)if(!(typeof t[u][h]>"u")){var d={v:t[u][h]},p=s+u,x=f+h;if(o.s.r>p&&(o.s.r=p),o.s.c>x&&(o.s.c=x),o.e.r<p&&(o.e.r=p),o.e.c<x&&(o.e.c=x),t[u][h]&&typeof t[u][h]=="object"&&!Array.isArray(t[u][h])&&!(t[u][h]instanceof Date))d=t[u][h];else if(Array.isArray(d.v)&&(d.f=t[u][h][1],d.v=d.v[0]),d.v===null)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else if(a.sheetStubs)d.t="z";else continue;else typeof d.v=="number"?d.t="n":typeof d.v=="boolean"?d.t="b":d.v instanceof Date?(d.z=a.dateNF||ge[14],a.cellDates?(d.t="d",d.w=Yr(d.z,fr(d.v))):(d.t="n",d.v=fr(d.v),d.w=Yr(d.z,d.v))):d.t="s";if(n)i[p]||(i[p]=[]),i[p][x]&&i[p][x].z&&(d.z=i[p][x].z),i[p][x]=d;else{var m=_e({c:x,r:p});i[m]&&i[m].z&&(d.z=i[m].z),i[m]=d}}}return o.s.c<1e7&&(i["!ref"]=Ee(o)),i}function wa(e,t){return uf(null,e,t)}function Ml(e){return e.read_shift(4,"i")}function rt(e,t){return t||(t=z(4)),t.write_shift(4,e),t}function Er(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function lr(e,t){var r=!1;return t==null&&(r=!0,t=z(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Ul(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Wl(e,t){return t||(t=z(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function Nn(e,t){var r=e.l,a=e.read_shift(1),n=Er(e),i=[],s={t:n,h:n};if(a&1){for(var f=e.read_shift(4),c=0;c!=f;++c)i.push(Ul(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Vl(e,t){var r=!1;return t==null&&(r=!0,t=z(15+4*e.t.length)),t.write_shift(1,0),lr(e.t,t),r?t.slice(0,t.l):t}var Hl=Nn;function Gl(e,t){var r=!1;return t==null&&(r=!0,t=z(23+4*e.t.length)),t.write_shift(1,1),lr(e.t,t),t.write_shift(4,1),Wl({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function Jr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function ea(e,t){return t==null&&(t=z(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function ra(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function ta(e,t){return t==null&&(t=z(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Xl=Er,hf=lr;function Pn(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function F0(e,t){var r=!1;return t==null&&(r=!0,t=z(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var zl=Er,nn=Pn,Ln=F0;function Bn(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,a=t[0]&2;e.l+=4;var n=a===0?S0([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):Vt(t,0)>>2;return r?n/100:n}function xf(e,t){t==null&&(t=z(4));var r=0,a=0,n=e*100;if(e==(e|0)&&e>=-(1<<29)&&e<1<<29?a=1:n==(n|0)&&n>=-(1<<29)&&n<1<<29&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function df(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function $l(e,t){return t||(t=z(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var aa=df,ka=$l;function kr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Yt(e,t){return(t||z(8)).write_shift(8,e,"f")}function Kl(e){var t={},r=e.read_shift(1),a=r>>>1,n=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),c=e.read_shift(1);switch(e.l++,a){case 0:t.auto=1;break;case 1:t.index=n;var o=Gt[n];o&&(t.rgb=za(o));break;case 2:t.rgb=za([s,f,c]);break;case 3:t.theme=n;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function y0(e,t){if(t||(t=z(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var a=e.rgb||"FFFFFF";typeof a=="number"&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}return t}function jl(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function Yl(e,t){t||(t=z(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}function pf(e,t){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},a=e.read_shift(4);switch(a){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));return e.l-=4,e.read_shift(0,t==1?"lpstr":"lpwstr")}function Jl(e){return pf(e,1)}function ql(e){return pf(e,2)}var Mn=2,Or=3,o0=11,Li=12,A0=19,l0=64,Zl=65,Ql=71,eu=4108,ru=4126,cr=80,mf=81,tu=[cr,mf],sn={1:{n:"CodePage",t:Mn},2:{n:"Category",t:cr},3:{n:"PresentationFormat",t:cr},4:{n:"ByteCount",t:Or},5:{n:"LineCount",t:Or},6:{n:"ParagraphCount",t:Or},7:{n:"SlideCount",t:Or},8:{n:"NoteCount",t:Or},9:{n:"HiddenCount",t:Or},10:{n:"MultimediaClipCount",t:Or},11:{n:"ScaleCrop",t:o0},12:{n:"HeadingPairs",t:eu},13:{n:"TitlesOfParts",t:ru},14:{n:"Manager",t:cr},15:{n:"Company",t:cr},16:{n:"LinksUpToDate",t:o0},17:{n:"CharacterCount",t:Or},19:{n:"SharedDoc",t:o0},22:{n:"HyperlinksChanged",t:o0},23:{n:"AppVersion",t:Or,p:"version"},24:{n:"DigSig",t:Zl},26:{n:"ContentType",t:cr},27:{n:"ContentStatus",t:cr},28:{n:"Language",t:cr},29:{n:"Version",t:cr},255:{},2147483648:{n:"Locale",t:A0},2147483651:{n:"Behavior",t:A0},1919054434:{}},fn={1:{n:"CodePage",t:Mn},2:{n:"Title",t:cr},3:{n:"Subject",t:cr},4:{n:"Author",t:cr},5:{n:"Keywords",t:cr},6:{n:"Comments",t:cr},7:{n:"Template",t:cr},8:{n:"LastAuthor",t:cr},9:{n:"RevNumber",t:cr},10:{n:"EditTime",t:l0},11:{n:"LastPrinted",t:l0},12:{n:"CreatedDate",t:l0},13:{n:"ModifiedDate",t:l0},14:{n:"PageCount",t:Or},15:{n:"WordCount",t:Or},16:{n:"CharCount",t:Or},17:{n:"Thumbnail",t:Ql},18:{n:"Application",t:cr},19:{n:"DocSecurity",t:Or},255:{},2147483648:{n:"Locale",t:A0},2147483651:{n:"Behavior",t:A0},1919054434:{}},Bi={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},au=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function nu(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var iu=nu([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Gt=He(iu),Et={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},vf={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},cn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},u0={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Un(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function su(e){var t=Un();if(!e||!e.match)return t;var r={};if((e.match(Sr)||[]).forEach(function(a){var n=we(a);switch(n[0].replace(ml,"<")){case"<?xml":break;case"<Types":t.xmlns=n["xmlns"+(n[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[n.Extension]=n.ContentType;break;case"<Override":t[cn[n.ContentType]]!==void 0&&t[cn[n.ContentType]].push(n.PartName);break}}),t.xmlns!==sr.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}function gf(e,t){var r=fl(cn),a=[],n;a[a.length]=rr,a[a.length]=ie("Types",null,{xmlns:sr.CT,"xmlns:xsd":sr.xsd,"xmlns:xsi":sr.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(c){return ie("Default",null,{Extension:c[0],ContentType:c[1]})}));var i=function(c){e[c]&&e[c].length>0&&(n=e[c][0],a[a.length]=ie("Override",null,{PartName:(n[0]=="/"?"":"/")+n,ContentType:u0[c][t.bookType]||u0[c].xlsx}))},s=function(c){(e[c]||[]).forEach(function(o){a[a.length]=ie("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:u0[c][t.bookType]||u0[c].xlsx})})},f=function(c){(e[c]||[]).forEach(function(o){a[a.length]=ie("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:r[c][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var Ae={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Xa(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Na(e,t){var r={"!id":{}};if(!e)return r;t.charAt(0)!=="/"&&(t="/"+t);var a={};return(e.match(Sr)||[]).forEach(function(n){var i=we(n);if(i[0]==="<Relationship"){var s={};s.Type=i.Type,s.Target=i.Target,s.Id=i.Id,i.TargetMode&&(s.TargetMode=i.TargetMode);var f=i.TargetMode==="External"?i.Target:Ia(i.Target,t);r[f]=s,a[i.Id]=s}}),r["!id"]=a,r}function ha(e){var t=[rr,ie("Relationships",null,{xmlns:sr.RELS})];return Je(e["!id"]).forEach(function(r){t[t.length]=ie("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Pe(e,t,r,a,n,i){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,i?n.TargetMode=i:[Ae.HLINK,Ae.XPATH,Ae.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}var fu="application/vnd.oasis.opendocument.spreadsheet";function cu(e,t){for(var r=Dn(e),a,n;a=Ga.exec(r);)switch(a[3]){case"manifest":break;case"file-entry":if(n=we(a[0],!1),n.path=="/"&&n.type!==fu)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw a}}function ou(e){var t=[rr];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function Mi(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function lu(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function uu(e){var t=[rr];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(Mi(e[r][0],e[r][1])),t.push(lu("",e[r][0]));return t.push(Mi("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function _f(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+_0.version+"</meta:generator></office:meta></office:document-meta>"}var jr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],hu=function(){for(var e=new Array(jr.length),t=0;t<jr.length;++t){var r=jr[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function wf(e){var t={};e=Be(e);for(var r=0;r<jr.length;++r){var a=jr[r],n=e.match(hu[r]);n!=null&&n.length>0&&(t[a[1]]=Oe(n[1])),a[2]==="date"&&t[a[1]]&&(t[a[1]]=Ve(t[a[1]]))}return t}function $0(e,t,r,a,n){n[e]!=null||t==null||t===""||(n[e]=t,t=Le(t),a[a.length]=r?ie(e,t,r):dr(e,t))}function kf(e,t){var r=t||{},a=[rr,ie("cp:coreProperties",null,{"xmlns:cp":sr.CORE_PROPS,"xmlns:dc":sr.dc,"xmlns:dcterms":sr.dcterms,"xmlns:dcmitype":sr.dcmitype,"xmlns:xsi":sr.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(e.CreatedDate!=null&&$0("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:an(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),e.ModifiedDate!=null&&$0("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:an(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var i=0;i!=jr.length;++i){var s=jr[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&$0(s[0],f,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var Xt=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],Ef=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function Tf(e,t,r,a){var n=[];if(typeof e=="string")n=Ci(e,a);else for(var i=0;i<e.length;++i)n=n.concat(e[i].map(function(l){return{v:l}}));var s=typeof t=="string"?Ci(t,a).map(function(l){return l.v}):t,f=0,c=0;if(s.length>0)for(var o=0;o!==n.length;o+=2){switch(c=+n[o+1].v,n[o].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=s.slice(f,f+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=c,r.DefinedNames=s.slice(f,f+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=s.slice(f,f+c);break}f+=c}}function xu(e,t,r){var a={};return t||(t={}),e=Be(e),Xt.forEach(function(n){var i=(e.match(Va(n[0]))||[])[1];switch(n[2]){case"string":i&&(t[n[1]]=Oe(i));break;case"bool":t[n[1]]=i==="true";break;case"raw":var s=e.match(new RegExp("<"+n[0]+"[^>]*>([\\s\\S]*?)</"+n[0]+">"));s&&s.length>0&&(a[n[1]]=s[1]);break}}),a.HeadingPairs&&a.TitlesOfParts&&Tf(a.HeadingPairs,a.TitlesOfParts,t,r),t}function Sf(e){var t=[],r=ie;return e||(e={}),e.Application="SheetJS",t[t.length]=rr,t[t.length]=ie("Properties",null,{xmlns:sr.EXT_PROPS,"xmlns:vt":sr.vt}),Xt.forEach(function(a){if(e[a[1]]!==void 0){var n;switch(a[2]){case"string":n=Le(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break}n!==void 0&&(t[t.length]=r(a[0],n))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(a){return"<vt:lpstr>"+Le(a)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var du=/<[^>]+>[^<]*/g;function pu(e,t){var r={},a="",n=e.match(du);if(n)for(var i=0;i!=n.length;++i){var s=n[i],f=we(s);switch(f[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Oe(f.name);break;case"</property>":a=null;break;default:if(s.indexOf("<vt:")===0){var c=s.split(">"),o=c[0].slice(4),l=c[1];switch(o){case"lpstr":case"bstr":case"lpwstr":r[a]=Oe(l);break;case"bool":r[a]=We(l);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(l,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(l);break;case"filetime":case"date":r[a]=Ve(l);break;case"cy":case"error":r[a]=Oe(l);break;default:if(o.slice(-1)=="/")break;t.WTF&&typeof console<"u"&&console.warn("Unexpected",s,o,c)}}else if(s.slice(0,2)!=="</"){if(t.WTF)throw new Error(s)}}}return r}function Ff(e){var t=[rr,ie("Properties",null,{xmlns:sr.CUST_PROPS,"xmlns:vt":sr.vt})];if(!e)return t.join("");var r=1;return Je(e).forEach(function(n){++r,t[t.length]=ie("property",Sl(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Le(n)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var on={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},K0;function mu(e,t,r){K0||(K0=N0(on)),t=K0[t]||t,e[t]=r}function vu(e,t){var r=[];return Je(on).map(function(a){for(var n=0;n<jr.length;++n)if(jr[n][1]==a)return jr[n];for(n=0;n<Xt.length;++n)if(Xt[n][1]==a)return Xt[n];throw a}).forEach(function(a){if(e[a[1]]!=null){var n=t&&t.Props&&t.Props[a[1]]!=null?t.Props[a[1]]:e[a[1]];switch(a[2]){case"date":n=new Date(n).toISOString().replace(/\.\d*Z/,"Z");break}typeof n=="number"?n=String(n):n===!0||n===!1?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(dr(on[a[1]]||a[1],n))}}),ie("DocumentProperties",r.join(""),{xmlns:Pr.o})}function gu(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&Je(e).forEach(function(i){if(Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<jr.length;++s)if(i==jr[s][1])return;for(s=0;s<Xt.length;++s)if(i==Xt[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],c="string";typeof f=="number"?(c="float",f=String(f)):f===!0||f===!1?(c="boolean",f=f?"1":"0"):f=String(f),n.push(ie(Si(i),f,{"dt:dt":c}))}}),t&&Je(t).forEach(function(i){if(Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),n.push(ie(Si(i),s,{"dt:dt":f}))}}),"<"+a+' xmlns="'+Pr.o+'">'+n.join("")+"</"+a+">"}function Wn(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function _u(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,a=r%Math.pow(2,32),n=(r-a)/Math.pow(2,32);a*=1e7,n*=1e7;var i=a/Math.pow(2,32)|0;i>0&&(a=a%Math.pow(2,32),n+=i);var s=z(8);return s.write_shift(4,a),s.write_shift(4,n),s}function yf(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function Af(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function Cf(e,t,r){return t===31?Af(e):yf(e,t,r)}function ln(e,t,r){return Cf(e,t,r===!1?0:4)}function wu(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return Cf(e,t,0)}function ku(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(Dr,""),e.l-n&2&&(e.l+=2)}return r}function Eu(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(Dr,"");return r}function Tu(e){var t=e.l,r=C0(e,mf);e[e.l]==0&&e[e.l+1]==0&&e.l-t&2&&(e.l+=2);var a=C0(e,Or);return[r,a]}function Su(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(Tu(e));return r}function Ui(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var i=e.read_shift(4),s=e.read_shift(4);a[i]=e.read_shift(s,t===1200?"utf16le":"utf8").replace(Dr,"").replace(Da,"!"),t===1200&&s%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>2+1<<2),a}function Df(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(t&3)>0&&(e.l+=4-(t&3)&3),r}function Fu(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}function C0(e,t,r){var a=e.read_shift(2),n,i=r||{};if(e.l+=2,t!==Li&&a!==t&&tu.indexOf(t)===-1&&!((t&65534)==4126&&(a&65534)==4126))throw new Error("Expected type "+t+" saw "+a);switch(t===Li?a:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return n=e.read_shift(4,"i"),n;case 11:return e.read_shift(4)!==0;case 19:return n=e.read_shift(4),n;case 30:return yf(e,a,4).replace(Dr,"");case 31:return Af(e);case 64:return Wn(e);case 65:return Df(e);case 71:return Fu(e);case 80:return ln(e,a,!i.raw).replace(Dr,"");case 81:return wu(e,a).replace(Dr,"");case 4108:return Su(e);case 4126:case 4127:return a==4127?ku(e):Eu(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+a)}}function Wi(e,t){var r=z(4),a=z(4);switch(r.write_shift(4,e==80?31:e),e){case 3:a.write_shift(-4,t);break;case 5:a=z(8),a.write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=_u(t);break;case 31:case 80:for(a=z(4+2*(t.length+1)+(t.length%2?0:2)),a.write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return or([r,a])}function Vi(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),i=[],s=0,f=0,c=-1,o={};for(s=0;s!=n;++s){var l=e.read_shift(4),u=e.read_shift(4);i[s]=[l,u+r]}i.sort(function(T,g){return T[1]-g[1]});var h={};for(s=0;s!=n;++s){if(e.l!==i[s][1]){var d=!0;if(s>0&&t)switch(t[i[s-1][0]].t){case 2:e.l+2===i[s][1]&&(e.l+=2,d=!1);break;case 80:e.l<=i[s][1]&&(e.l=i[s][1],d=!1);break;case 4108:e.l<=i[s][1]&&(e.l=i[s][1],d=!1);break}if((!t||s==0)&&e.l<=i[s][1]&&(d=!1,e.l=i[s][1]),d)throw new Error("Read Error: Expected address "+i[s][1]+" at "+e.l+" :"+s)}if(t){var p=t[i[s][0]];if(h[p.n]=C0(e,p.t,{raw:!0}),p.p==="version"&&(h[p.n]=String(h[p.n]>>16)+"."+("0000"+String(h[p.n]&65535)).slice(-4)),p.n=="CodePage")switch(h[p.n]){case 0:h[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:Zr(f=h[p.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+h[p.n])}}else if(i[s][0]===1){if(f=h.CodePage=C0(e,Mn),Zr(f),c!==-1){var x=e.l;e.l=i[c][1],o=Ui(e,f),e.l=x}}else if(i[s][0]===0){if(f===0){c=s,e.l=i[s+1][1];continue}o=Ui(e,f)}else{var m=o[i[s][0]],_;switch(e[e.l]){case 65:e.l+=4,_=Df(e);break;case 30:e.l+=4,_=ln(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,_=ln(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,_=e.read_shift(4,"i");break;case 19:e.l+=4,_=e.read_shift(4);break;case 5:e.l+=4,_=e.read_shift(8,"f");break;case 11:e.l+=4,_=Qe(e,4);break;case 64:e.l+=4,_=Ve(Wn(e));break;default:throw new Error("unparsed value: "+e[e.l])}h[m]=_}}return e.l=r+a,h}var If=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function yu(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Hi(e,t,r){var a=z(8),n=[],i=[],s=8,f=0,c=z(8),o=z(8);if(c.write_shift(4,2),c.write_shift(4,1200),o.write_shift(4,1),i.push(c),n.push(o),s+=8+c.length,!t){o=z(8),o.write_shift(4,0),n.unshift(o);var l=[z(4)];for(l[0].write_shift(4,e.length),f=0;f<e.length;++f){var u=e[f][0];for(c=z(4+4+2*(u.length+1)+(u.length%2?0:2)),c.write_shift(4,f+2),c.write_shift(4,u.length+1),c.write_shift(0,u,"dbcs");c.l!=c.length;)c.write_shift(1,0);l.push(c)}c=or(l),i.unshift(c),s+=8+c.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(If.indexOf(e[f][0])>-1||Ef.indexOf(e[f][0])>-1)&&e[f][1]!=null){var h=e[f][1],d=0;if(t){d=+t[e[f][0]];var p=r[d];if(p.p=="version"&&typeof h=="string"){var x=h.split(".");h=(+x[0]<<16)+(+x[1]||0)}c=Wi(p.t,h)}else{var m=yu(h);m==-1&&(m=31,h=String(h)),c=Wi(m,h)}i.push(c),o=z(8),o.write_shift(4,t?d:2+f),n.push(o),s+=8+c.length}var _=8*(i.length+1);for(f=0;f<i.length;++f)n[f].write_shift(4,_),_+=i[f].length;return a.write_shift(4,s),a.write_shift(4,i.length),or([a].concat(n).concat(i))}function Gi(e,t,r){var a=e.content;if(!a)return{};xr(a,0);var n,i,s,f,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var o=a.read_shift(4),l=a.read_shift(16);if(l!==me.utils.consts.HEADER_CLSID&&l!==r)throw new Error("Bad PropertySet CLSID "+l);if(n=a.read_shift(4),n!==1&&n!==2)throw new Error("Unrecognized #Sets: "+n);if(i=a.read_shift(16),f=a.read_shift(4),n===1&&f!==a.l)throw new Error("Length mismatch: "+f+" !== "+a.l);n===2&&(s=a.read_shift(16),c=a.read_shift(4));var u=Vi(a,t),h={SystemIdentifier:o};for(var d in u)h[d]=u[d];if(h.FMTID=i,n===1)return h;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);var p;try{p=Vi(a,null)}catch{}for(d in p)h[d]=p[d];return h.FMTID=[i,s],h}function Xi(e,t,r,a,n,i){var s=z(n?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,me.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,n?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,n?68:48);var c=Hi(e,r,a);if(f.push(c),n){var o=Hi(n,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+c.length),f.push(o)}return or(f)}function Ft(e,t){return e.read_shift(t),null}function Au(e,t){t||(t=z(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function Cu(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function Qe(e,t){return e.read_shift(t)===1}function Cr(e,t){return t||(t=z(2)),t.write_shift(2,+!!e),t}function nr(e){return e.read_shift(2,"u")}function Kr(e,t){return t||(t=z(2)),t.write_shift(2,e),t}function Of(e,t){return Cu(e,t,nr)}function Du(e){var t=e.read_shift(1),r=e.read_shift(1);return r===1?t:t===1}function bf(e,t,r){return r||(r=z(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function e0(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(n="dbcs-cont")}else r.biff==12&&(n="wstr");r.biff>=2&&r.biff<=5&&(n="cpstr");var s=a?e.read_shift(a,n):"";return s}function Iu(e){var t=e.read_shift(2),r=e.read_shift(1),a=r&4,n=r&8,i=1+(r&1),s=0,f,c={};n&&(s=e.read_shift(2)),a&&(f=e.read_shift(4));var o=i==2?"dbcs-cont":"sbcs-cont",l=t===0?"":e.read_shift(t,o);return n&&(e.l+=4*s),a&&(e.l+=f),c.t=l,n||(c.raw="<t>"+c.t+"</t>",c.r=c.t),c}function Ou(e){var t=e.t||"",r=z(3+0);r.write_shift(2,t.length),r.write_shift(1,1);var a=z(2*t.length);a.write_shift(2*t.length,t,"utf16le");var n=[r,a];return or(n)}function Jt(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var n=e.read_shift(1);return n===0?a=e.read_shift(t,"sbcs-cont"):a=e.read_shift(t,"dbcs-cont"),a}function r0(e,t,r){var a=e.read_shift(r&&r.biff==2?1:2);return a===0?(e.l++,""):Jt(e,a,r)}function na(e,t,r){if(r.biff>5)return r0(e,t,r);var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Rf(e,t,r){return r||(r=z(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function bu(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}function Ru(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(Dr,"");return a&&(e.l+=24),n}function Nu(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var n=e.read_shift(4);if(n===0)return r+a.replace(/\\/g,"/");var i=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var s=e.read_shift(i>>1,"utf16le").replace(Dr,"");return r+s}function Pu(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return Ru(e);case"0303000000000000c000000000000046":return Nu(e);default:throw new Error("Unsupported Moniker "+r)}}function h0(e){var t=e.read_shift(4),r=t>0?e.read_shift(t,"utf16le").replace(Dr,""):"";return r}function zi(e,t){t||(t=z(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function Lu(e,t){var r=e.l+t,a=e.read_shift(4);if(a!==2)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var i,s,f,c,o="",l,u;n&16&&(i=h0(e,r-e.l)),n&128&&(s=h0(e,r-e.l)),(n&257)===257&&(f=h0(e,r-e.l)),(n&257)===1&&(c=Pu(e,r-e.l)),n&8&&(o=h0(e,r-e.l)),n&32&&(l=e.read_shift(16)),n&64&&(u=Wn(e)),e.l=r;var h=s||f||c||"";h&&o&&(h+="#"+o),h||(h="#"+o),n&2&&h.charAt(0)=="/"&&h.charAt(1)!="/"&&(h="file://"+h);var d={Target:h};return l&&(d.guid=l),u&&(d.time=u),i&&(d.Tooltip=i),d}function Bu(e){var t=z(512),r=0,a=e.Target;a.slice(0,7)=="file://"&&(a=a.slice(7));var n=a.indexOf("#"),i=n>-1?31:23;switch(a.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)a=a.slice(1),zi(a,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&zi(n>-1?a.slice(n+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var c=0;a.slice(c*3,c*3+3)=="../"||a.slice(c*3,c*3+3)=="..\\";)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,a.charCodeAt(r+3*c)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Nf(e){var t=e.read_shift(1),r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(1);return[t,r,a,n]}function Pf(e,t){var r=Nf(e);return r[3]=0,r}function lt(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return{r:t,c:r,ixfe:a}}function qt(e,t,r,a){return a||(a=z(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function Mu(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}function Uu(e,t,r){return t===0?"":na(e,t,r)}function Wu(e,t,r){var a=r.biff>8?4:2,n=e.read_shift(a),i=e.read_shift(a,"i"),s=e.read_shift(a,"i");return[n,i,s]}function Lf(e){var t=e.read_shift(2),r=Bn(e);return[t,r]}function Vu(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=e0(e,t,r),i=e.read_shift(2);if(a-=e.l,i!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+i);return e.l+=i,n}function B0(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2);return{s:{c:a,r:t},e:{c:n,r}}}function Bf(e,t){return t||(t=z(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function Mf(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(1),n=e.read_shift(1);return{s:{c:a,r:t},e:{c:n,r}}}var Hu=Mf;function Uf(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function Gu(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t}function Xu(e){var t={};return e.l+=4,e.cf=e.read_shift(2),t}function mr(e){e.l+=2,e.l+=e.read_shift(2)}var zu={0:mr,4:mr,5:mr,6:mr,7:Xu,8:mr,9:mr,10:mr,11:mr,12:mr,13:Gu,14:mr,15:mr,16:mr,17:mr,18:mr,19:mr,20:mr,21:Uf};function $u(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(zu[n](e,r-e.l))}catch{return e.l=r,a}}return e.l!=r&&(e.l=r),a}function x0(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),t-=2,t>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function Vn(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=z(n);return i.write_shift(2,a),i.write_shift(2,t),n>4&&i.write_shift(2,29282),n>6&&i.write_shift(2,1997),n>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function Ku(e,t){return t===0||e.read_shift(2),1200}function ju(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=na(e,0,r);return e.read_shift(t+a-e.l),n}function Yu(e,t){var r=!t||t.biff==8,a=z(r?112:54);for(a.write_shift(t.biff==8?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}function Ju(e,t,r){var a=r&&r.biff==8||t==2?e.read_shift(2):(e.l+=t,0);return{fDialog:a&16,fBelow:a&64,fRight:a&128}}function qu(e,t,r){var a=e.read_shift(4),n=e.read_shift(1)&3,i=e.read_shift(1);switch(i){case 0:i="Worksheet";break;case 1:i="Macrosheet";break;case 2:i="Chartsheet";break;case 6:i="VBAModule";break}var s=e0(e,0,r);return s.length===0&&(s="Sheet1"),{pos:a,hs:n,dt:i,name:s}}function Zu(e,t){var r=!t||t.biff>=8?2:1,a=z(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function Qu(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),i=[],s=0;s!=n&&e.l<r;++s)i.push(Iu(e));return i.Count=a,i.Unique=n,i}function eh(e,t){var r=z(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=Ou(e[n]);var i=or([r].concat(a));return i.parts=[r.length].concat(a.map(function(s){return s.length})),i}function rh(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}function th(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,a&7&&(t.level=a&7),a&32&&(t.hidden=!0),a&64&&(t.hpt=r/20),t}function ah(e){var t=Mu(e);if(t.type!=2211)throw new Error("Invalid Future Record "+t.type);var r=e.read_shift(4);return r!==0}function nh(e){return e.read_shift(2),e.read_shift(4)}function $i(e,t,r){var a=0;r&&r.biff==2||(a=e.read_shift(2));var n=e.read_shift(2);r&&r.biff==2&&(a=1-(n>>15),n&=32767);var i={Unsynced:a&1,DyZero:(a&2)>>1,ExAsc:(a&4)>>2,ExDsc:(a&8)>>3};return[i,n]}function ih(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),f=e.read_shift(2),c=e.read_shift(2),o=e.read_shift(2);return{Pos:[t,r],Dim:[a,n],Flags:i,CurTab:s,FirstTab:f,Selected:c,TabRatio:o}}function sh(){var e=z(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function fh(e,t,r){if(r&&r.biff>=2&&r.biff<5)return{};var a=e.read_shift(2);return{RTL:a&64}}function ch(e){var t=z(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function oh(){}function lh(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return a.name=e0(e,0,r),a}function uh(e,t){var r=e.name||"Arial",a=t&&t.biff==5,n=a?15+r.length:16+2*r.length,i=z(n);return i.write_shift(2,(e.sz||12)*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),a||i.write_shift(1,1),i.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),i}function hh(e){var t=lt(e);return t.isst=e.read_shift(4),t}function xh(e,t,r,a){var n=z(10);return qt(e,t,a,n),n.write_shift(4,r),n}function dh(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=e.l+t,n=lt(e);r.biff==2&&e.l++;var i=r0(e,a-e.l,r);return n.val=i,n}function ph(e,t,r,a,n){var i=!n||n.biff==8,s=z(6+2+ +i+(1+i)*r.length);return qt(e,t,a,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function mh(e,t,r){var a=e.read_shift(2),n=na(e,0,r);return[a,n]}function vh(e,t,r,a){var n=r&&r.biff==5;a||(a=z(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var i=a.length>a.l?a.slice(0,a.l):a;return i.l==null&&(i.l=i.length),i}var gh=na;function Ki(e,t,r){var a=e.l+t,n=r.biff==8||!r.biff?4:2,i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:i,c:f},e:{r:s,c}}}function _h(e,t){var r=t.biff==8||!t.biff?4:2,a=z(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}function wh(e){var t=e.read_shift(2),r=e.read_shift(2),a=Lf(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}function kh(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),i=[];e.l<r;)i.push(Lf(e));if(e.l!==r)throw new Error("MulRK read error");var s=e.read_shift(2);if(i.length!=s-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:s,rkrec:i}}function Eh(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),i=[];e.l<r;)i.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var s=e.read_shift(2);if(i.length!=s-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:s,ixfe:i}}function Th(e,t,r,a){var n={},i=e.read_shift(4),s=e.read_shift(4),f=e.read_shift(4),c=e.read_shift(2);return n.patternType=au[f>>26],a.cellStyles&&(n.alc=i&7,n.fWrap=i>>3&1,n.alcV=i>>4&7,n.fJustLast=i>>7&1,n.trot=i>>8&255,n.cIndent=i>>16&15,n.fShrinkToFit=i>>20&1,n.iReadOrder=i>>22&2,n.fAtrNum=i>>26&1,n.fAtrFnt=i>>27&1,n.fAtrAlc=i>>28&1,n.fAtrBdr=i>>29&1,n.fAtrPat=i>>30&1,n.fAtrProt=i>>31&1,n.dgLeft=s&15,n.dgRight=s>>4&15,n.dgTop=s>>8&15,n.dgBottom=s>>12&15,n.icvLeft=s>>16&127,n.icvRight=s>>23&127,n.grbitDiag=s>>30&3,n.icvTop=f&127,n.icvBottom=f>>7&127,n.icvDiag=f>>14&127,n.dgDiag=f>>21&15,n.icvFore=c&127,n.icvBack=c>>7&127,n.fsxButton=c>>14&1),n}function Sh(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,t-=6,a.data=Th(e,t,a.fStyle,r),a}function ji(e,t,r,a){var n=r&&r.biff==5;a||(a=z(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&n&&(i|=1024),a.write_shift(4,i),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Fh(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(t[0]!==0&&t[0]--,t[1]!==0&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}function yh(e){var t=z(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function Yi(e,t,r){var a=lt(e);(r.biff==2||t==9)&&++e.l;var n=Du(e);return a.val=n,a.t=n===!0||n===!1?"b":"e",a}function Ah(e,t,r,a,n,i){var s=z(8);return qt(e,t,a,s),bf(r,i,s),s}function Ch(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=lt(e),n=kr(e);return a.val=n,a}function Dh(e,t,r,a){var n=z(14);return qt(e,t,a,n),Yt(r,n),n}var Ji=Uu;function Ih(e,t,r){var a=e.l+t,n=e.read_shift(2),i=e.read_shift(2);if(r.sbcch=i,i==1025||i==14849)return[i,n];if(i<1||i>255)throw new Error("Unexpected SupBook type: "+i);for(var s=Jt(e,i),f=[];a>e.l;)f.push(r0(e));return[i,n,s,f]}function qi(e,t,r){var a=e.read_shift(2),n,i={fBuiltIn:a&1,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return r.sbcch===14849&&(n=Vu(e,t-2,r)),i.body=n||e.read_shift(t-2),typeof n=="string"&&(i.Name=n),i}var Oh=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Zi(e,t,r){var a=e.l+t,n=e.read_shift(2),i=e.read_shift(1),s=e.read_shift(1),f=e.read_shift(r&&r.biff==2?1:2),c=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),c=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var o=Jt(e,s,r);n&32&&(o=Oh[o.charCodeAt(0)]);var l=a-e.l;r&&r.biff==2&&--l;var u=a==e.l||f===0||!(l>0)?[]:$p(e,l,r,f);return{chKey:i,Name:o,itab:c,rgce:u}}function Wf(e,t,r){if(r.biff<8)return bh(e,t,r);for(var a=[],n=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)a.push(Wu(e,r.biff>8?12:6,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function bh(e,t,r){e[e.l+1]==3&&e[e.l]++;var a=e0(e,t,r);return a.charCodeAt(0)==3?a.slice(1):a}function Rh(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2),i=Jt(e,a,r),s=Jt(e,n,r);return[i,s]}function Nh(e,t,r){var a=Mf(e);e.l++;var n=e.read_shift(1);return t-=8,[Kp(e,t,r),n,a]}function Qi(e,t,r){var a=Hu(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,Xp(e,t,r)]}function Ph(e){var t=e.read_shift(4)!==0,r=e.read_shift(4)!==0,a=e.read_shift(4);return[t,r,a]}function Lh(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2),s=e.read_shift(2),f=na(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},f,s,i]}}function Bh(e,t,r){return Lh(e,t,r)}function Mh(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(B0(e));return r}function Uh(e){var t=z(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)Bf(e[r],t);return t}function Wh(e,t,r){if(r&&r.biff<8)return Hh(e,t,r);var a=Uf(e),n=$u(e,t-22,a[1]);return{cmo:a,ft:n}}var Vh={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function Hh(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var s=[];return s.push((Vh[a]||Tr)(e,t,r)),{cmo:[n,a,i],ft:s}}function Gh(e,t,r){var a=e.l,n="";try{e.l+=4;var i=(r.lastobj||{cmo:[0,0]}).cmo[1],s;[0,5,7,11,12,14].indexOf(i)==-1?e.l+=6:s=bu(e,6,r);var f=e.read_shift(2);e.read_shift(2),nr(e,2);var c=e.read_shift(2);e.l+=c;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw new Error("TxO: bad continue record");var l=e[e.l],u=Jt(e,e.lens[o+1]-e.lens[o]-1);if(n+=u,n.length>=(l?f:2*f))break}if(n.length!==f&&n.length!==f*2)throw new Error("cchText: "+f+" != "+n.length);return e.l=a+t,{t:n}}catch{return e.l=a+t,{t:n}}}function Xh(e,t){var r=B0(e);e.l+=16;var a=Lu(e,t-24);return[r,a]}function zh(e){var t=z(24),r=Ke(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return or([t,Bu(e[1])])}function $h(e,t){e.read_shift(2);var r=B0(e),a=e.read_shift((t-10)/2,"dbcs-cont");return a=a.replace(Dr,""),[r,a]}function Kh(e){var t=e[1].Tooltip,r=z(10+2*(t.length+1));r.write_shift(2,2048);var a=Ke(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function jh(e){var t=[0,0],r;return r=e.read_shift(2),t[0]=Bi[r]||r,r=e.read_shift(2),t[1]=Bi[r]||r,t}function Yh(e){return e||(e=z(4)),e.write_shift(2,1),e.write_shift(2,1),e}function Jh(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(Pf(e));return r}function qh(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(Pf(e));return r}function Zh(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t}function Vf(e,t,r){if(!r.cellStyles)return Tr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),i=e.read_shift(a),s=e.read_shift(a),f=e.read_shift(a),c=e.read_shift(2);a==2&&(e.l+=2);var o={s:n,e:i,w:s,ixfe:f,flags:c};return(r.biff>=5||!r.biff)&&(o.level=c>>8&7),o}function Qh(e,t){var r=z(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}function e1(e,t){var r={};return t<32||(e.l+=16,r.header=kr(e),r.footer=kr(e),e.l+=2),r}function r1(e,t,r){var a={area:!1};if(r.biff!=5)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,n&16&&(a.area=!0),a}function t1(e){for(var t=z(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}var a1=lt,n1=Of,i1=r0;function s1(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}function f1(e,t,r){r.biffguess&&r.biff==5&&(r.biff=2);var a=lt(e);++e.l;var n=na(e,t-7,r);return a.t="str",a.val=n,a}function c1(e){var t=lt(e);++e.l;var r=kr(e);return t.t="n",t.val=r,t}function o1(e,t,r){var a=z(15);return i0(a,e,t),a.write_shift(8,r,"f"),a}function l1(e){var t=lt(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}function u1(e,t,r){var a=z(9);return i0(a,e,t),a.write_shift(2,r),a}function h1(e){var t=e.read_shift(1);return t===0?(e.l++,""):e.read_shift(t,"sbcs-cont")}function x1(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}function d1(e,t,r){var a=e.l+t,n=lt(e),i=e.read_shift(2),s=Jt(e,i,r);return e.l=a,n.t="str",n.val=s,n}var p1=[2,3,48,49,131,139,140,245],un=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=N0({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,c){var o=[],l=It(1);switch(c.type){case"base64":l=Br(Ur(f));break;case"binary":l=Br(f);break;case"buffer":case"array":l=f;break}xr(l,0);var u=l.read_shift(1),h=!!(u&136),d=!1,p=!1;switch(u){case 2:break;case 3:break;case 48:d=!0,h=!0;break;case 49:d=!0,h=!0;break;case 131:break;case 139:break;case 140:p=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+u.toString(16))}var x=0,m=521;u==2&&(x=l.read_shift(2)),l.l+=3,u!=2&&(x=l.read_shift(4)),x>1048576&&(x=1e6),u!=2&&(m=l.read_shift(2));var _=l.read_shift(2),T=c.codepage||1252;u!=2&&(l.l+=16,l.read_shift(1),l[l.l]!==0&&(T=e[l[l.l]]),l.l+=1,l.l+=2),p&&(l.l+=36);for(var g=[],I={},L=Math.min(l.length,u==2?521:m-10-(d?264:0)),D=p?32:11;l.l<L&&l[l.l]!=13;)switch(I={},I.name=yt.utils.decode(T,l.slice(l.l,l.l+D)).replace(/[\u0000\r\n].*$/g,""),l.l+=D,I.type=String.fromCharCode(l.read_shift(1)),u!=2&&!p&&(I.offset=l.read_shift(4)),I.len=l.read_shift(1),u==2&&(I.offset=l.read_shift(2)),I.dec=l.read_shift(1),I.name.length&&g.push(I),u!=2&&(l.l+=p?13:14),I.type){case"B":(!d||I.len!=8)&&c.WTF&&console.log("Skipping "+I.name+":"+I.type);break;case"G":case"P":c.WTF&&console.log("Skipping "+I.name+":"+I.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+I.type)}if(l[l.l]!==13&&(l.l=m-1),l.read_shift(1)!==13)throw new Error("DBF Terminator not found "+l.l+" "+l[l.l]);l.l=m;var k=0,R=0;for(o[0]=[],R=0;R!=g.length;++R)o[0][R]=g[R].name;for(;x-- >0;){if(l[l.l]===42){l.l+=_;continue}for(++l.l,o[++k]=[],R=0,R=0;R!=g.length;++R){var O=l.slice(l.l,l.l+g[R].len);l.l+=g[R].len,xr(O,0);var X=yt.utils.decode(T,O);switch(g[R].type){case"C":X.trim().length&&(o[k][R]=X.replace(/\s+$/,""));break;case"D":X.length===8?o[k][R]=new Date(+X.slice(0,4),+X.slice(4,6)-1,+X.slice(6,8)):o[k][R]=X;break;case"F":o[k][R]=parseFloat(X.trim());break;case"+":case"I":o[k][R]=p?O.read_shift(-4,"i")^2147483648:O.read_shift(4,"i");break;case"L":switch(X.trim().toUpperCase()){case"Y":case"T":o[k][R]=!0;break;case"N":case"F":o[k][R]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+X+"|")}break;case"M":if(!h)throw new Error("DBF Unexpected MEMO for type "+u.toString(16));o[k][R]="##MEMO##"+(p?parseInt(X.trim(),10):O.read_shift(4));break;case"N":X=X.replace(/\u0000/g,"").trim(),X&&X!="."&&(o[k][R]=+X||0);break;case"@":o[k][R]=new Date(O.read_shift(-8,"f")-621356832e5);break;case"T":o[k][R]=new Date((O.read_shift(4)-2440588)*864e5+O.read_shift(4));break;case"Y":o[k][R]=O.read_shift(4,"i")/1e4+O.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":o[k][R]=-O.read_shift(-8,"f");break;case"B":if(d&&g[R].len==8){o[k][R]=O.read_shift(8,"f");break}case"G":case"P":O.l+=g[R].len;break;case"0":if(g[R].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+g[R].type)}}}if(u!=2&&l.l<l.length&&l[l.l++]!=26)throw new Error("DBF EOF Marker missing "+(l.l-1)+" of "+l.length+" "+l[l.l-1].toString(16));return c&&c.sheetRows&&(o=o.slice(0,c.sheetRows)),c.DBF=g,o}function a(f,c){var o=c||{};o.dateNF||(o.dateNF="yyyymmdd");var l=wa(r(f,o),o);return l["!cols"]=o.DBF.map(function(u){return{wch:u.len,DBF:u}}),delete o.DBF,l}function n(f,c){try{return Pt(a(f,c),c)}catch(o){if(c&&c.WTF)throw o}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,c){var o=c||{};if(+o.codepage>=0&&Zr(+o.codepage),o.type=="string")throw new Error("Cannot write DBF to JS string");var l=br(),u=O0(f,{header:1,raw:!0,cellDates:!0}),h=u[0],d=u.slice(1),p=f["!cols"]||[],x=0,m=0,_=0,T=1;for(x=0;x<h.length;++x){if(((p[x]||{}).DBF||{}).name){h[x]=p[x].DBF.name,++_;continue}if(h[x]!=null){if(++_,typeof h[x]=="number"&&(h[x]=h[x].toString(10)),typeof h[x]!="string")throw new Error("DBF Invalid column name "+h[x]+" |"+typeof h[x]+"|");if(h.indexOf(h[x])!==x){for(m=0;m<1024;++m)if(h.indexOf(h[x]+"_"+m)==-1){h[x]+="_"+m;break}}}}var g=be(f["!ref"]),I=[],L=[],D=[];for(x=0;x<=g.e.c-g.s.c;++x){var k="",R="",O=0,X=[];for(m=0;m<d.length;++m)d[m][x]!=null&&X.push(d[m][x]);if(X.length==0||h[x]==null){I[x]="?";continue}for(m=0;m<X.length;++m){switch(typeof X[m]){case"number":R="B";break;case"string":R="C";break;case"boolean":R="L";break;case"object":R=X[m]instanceof Date?"D":"C";break;default:R="C"}O=Math.max(O,String(X[m]).length),k=k&&k!=R?"C":R}O>250&&(O=250),R=((p[x]||{}).DBF||{}).type,R=="C"&&p[x].DBF.len>O&&(O=p[x].DBF.len),k=="B"&&R=="N"&&(k="N",D[x]=p[x].DBF.dec,O=p[x].DBF.len),L[x]=k=="C"||R=="N"?O:i[k]||0,T+=L[x],I[x]=k}var M=l.next(32);for(M.write_shift(4,318902576),M.write_shift(4,d.length),M.write_shift(2,296+32*_),M.write_shift(2,T),x=0;x<4;++x)M.write_shift(4,0);for(M.write_shift(4,0|(+t[Cs]||3)<<8),x=0,m=0;x<h.length;++x)if(h[x]!=null){var P=l.next(32),J=(h[x].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);P.write_shift(1,J,"sbcs"),P.write_shift(1,I[x]=="?"?"C":I[x],"sbcs"),P.write_shift(4,m),P.write_shift(1,L[x]||i[I[x]]||0),P.write_shift(1,D[x]||0),P.write_shift(1,2),P.write_shift(4,0),P.write_shift(1,0),P.write_shift(4,0),P.write_shift(4,0),m+=L[x]||i[I[x]]||0}var le=l.next(264);for(le.write_shift(4,13),x=0;x<65;++x)le.write_shift(4,0);for(x=0;x<d.length;++x){var Z=l.next(T);for(Z.write_shift(1,0),m=0;m<h.length;++m)if(h[m]!=null)switch(I[m]){case"L":Z.write_shift(1,d[x][m]==null?63:d[x][m]?84:70);break;case"B":Z.write_shift(8,d[x][m]||0,"f");break;case"N":var ue="0";for(typeof d[x][m]=="number"&&(ue=d[x][m].toFixed(D[m]||0)),_=0;_<L[m]-ue.length;++_)Z.write_shift(1,32);Z.write_shift(1,ue,"sbcs");break;case"D":d[x][m]?(Z.write_shift(4,("0000"+d[x][m].getFullYear()).slice(-4),"sbcs"),Z.write_shift(2,("00"+(d[x][m].getMonth()+1)).slice(-2),"sbcs"),Z.write_shift(2,("00"+d[x][m].getDate()).slice(-2),"sbcs")):Z.write_shift(8,"00000000","sbcs");break;case"C":var ce=String(d[x][m]!=null?d[x][m]:"").slice(0,L[m]);for(Z.write_shift(1,ce,"sbcs"),_=0;_<L[m]-ce.length;++_)Z.write_shift(1,32);break}}return l.next(1).write_shift(1,26),l.end()}return{to_workbook:n,to_sheet:a,from_sheet:s}}(),Hf=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+Je(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(h,d){var p=e[d];return typeof p=="number"?li(p):p},a=function(h,d,p){var x=d.charCodeAt(0)-32<<4|p.charCodeAt(0)-48;return x==59?h:li(x)};e["|"]=254;function n(h,d){switch(d.type){case"base64":return i(Ur(h),d);case"binary":return i(h,d);case"buffer":return i(Fe&&Buffer.isBuffer(h)?h.toString("binary"):Nt(h),d);case"array":return i(jt(h),d)}throw new Error("Unrecognized type "+d.type)}function i(h,d){var p=h.split(/[\n\r]+/),x=-1,m=-1,_=0,T=0,g=[],I=[],L=null,D={},k=[],R=[],O=[],X=0,M;for(+d.codepage>=0&&Zr(+d.codepage);_!==p.length;++_){X=0;var P=p[_].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),J=P.replace(/;;/g,"\0").split(";").map(function(N){return N.replace(/\u0000/g,";")}),le=J[0],Z;if(P.length>0)switch(le){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":J[1].charAt(0)=="P"&&I.push(P.slice(3).replace(/;;/g,";"));break;case"C":var ue=!1,ce=!1,Ie=!1,V=!1,pe=-1,ve=-1;for(T=1;T<J.length;++T)switch(J[T].charAt(0)){case"A":break;case"X":m=parseInt(J[T].slice(1))-1,ce=!0;break;case"Y":for(x=parseInt(J[T].slice(1))-1,ce||(m=0),M=g.length;M<=x;++M)g[M]=[];break;case"K":Z=J[T].slice(1),Z.charAt(0)==='"'?Z=Z.slice(1,Z.length-1):Z==="TRUE"?Z=!0:Z==="FALSE"?Z=!1:isNaN(et(Z))?isNaN(pa(Z).getDate())||(Z=Ve(Z)):(Z=et(Z),L!==null&&ga(L)&&(Z=L0(Z))),ue=!0;break;case"E":V=!0;var C=xa(J[T].slice(1),{r:x,c:m});g[x][m]=[g[x][m],C];break;case"S":Ie=!0,g[x][m]=[g[x][m],"S5S"];break;case"G":break;case"R":pe=parseInt(J[T].slice(1))-1;break;case"C":ve=parseInt(J[T].slice(1))-1;break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+P)}if(ue&&(g[x][m]&&g[x][m].length==2?g[x][m][0]=Z:g[x][m]=Z,L=null),Ie){if(V)throw new Error("SYLK shared formula cannot have own formula");var B=pe>-1&&g[pe][ve];if(!B||!B[1])throw new Error("SYLK shared formula cannot find base");g[x][m][1]=sc(B[1],{r:x-pe,c:m-ve})}break;case"F":var b=0;for(T=1;T<J.length;++T)switch(J[T].charAt(0)){case"X":m=parseInt(J[T].slice(1))-1,++b;break;case"Y":for(x=parseInt(J[T].slice(1))-1,M=g.length;M<=x;++M)g[M]=[];break;case"M":X=parseInt(J[T].slice(1))/20;break;case"F":break;case"G":break;case"P":L=I[parseInt(J[T].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(O=J[T].slice(1).split(" "),M=parseInt(O[0],10);M<=parseInt(O[1],10);++M)X=parseInt(O[2],10),R[M-1]=X===0?{hidden:!0}:{wch:X},Ot(R[M-1]);break;case"C":m=parseInt(J[T].slice(1))-1,R[m]||(R[m]={});break;case"R":x=parseInt(J[T].slice(1))-1,k[x]||(k[x]={}),X>0?(k[x].hpt=X,k[x].hpx=va(X)):X===0&&(k[x].hidden=!0);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+P)}b<1&&(L=null);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+P)}}return k.length>0&&(D["!rows"]=k),R.length>0&&(D["!cols"]=R),d&&d.sheetRows&&(g=g.slice(0,d.sheetRows)),[g,D]}function s(h,d){var p=n(h,d),x=p[0],m=p[1],_=wa(x,d);return Je(m).forEach(function(T){_[T]=m[T]}),_}function f(h,d){return Pt(s(h,d),d)}function c(h,d,p,x){var m="C;Y"+(p+1)+";X"+(x+1)+";K";switch(h.t){case"n":m+=h.v||0,h.f&&!h.F&&(m+=";E"+Kn(h.f,{r:p,c:x}));break;case"b":m+=h.v?"TRUE":"FALSE";break;case"e":m+=h.w||h.v;break;case"d":m+='"'+(h.w||h.v)+'"';break;case"s":m+='"'+h.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return m}function o(h,d){d.forEach(function(p,x){var m="F;W"+(x+1)+" "+(x+1)+" ";p.hidden?m+="0":(typeof p.width=="number"&&!p.wpx&&(p.wpx=$a(p.width)),typeof p.wpx=="number"&&!p.wch&&(p.wch=Ka(p.wpx)),typeof p.wch=="number"&&(m+=Math.round(p.wch))),m.charAt(m.length-1)!=" "&&h.push(m)})}function l(h,d){d.forEach(function(p,x){var m="F;";p.hidden?m+="M0;":p.hpt?m+="M"+20*p.hpt+";":p.hpx&&(m+="M"+20*ja(p.hpx)+";"),m.length>2&&h.push(m+"R"+(x+1))})}function u(h,d){var p=["ID;PWXL;N;E"],x=[],m=be(h["!ref"]),_,T=Array.isArray(h),g=`\r
`;p.push("P;PGeneral"),p.push("F;P0;DG0G8;M255"),h["!cols"]&&o(p,h["!cols"]),h["!rows"]&&l(p,h["!rows"]),p.push("B;Y"+(m.e.r-m.s.r+1)+";X"+(m.e.c-m.s.c+1)+";D"+[m.s.c,m.s.r,m.e.c,m.e.r].join(" "));for(var I=m.s.r;I<=m.e.r;++I)for(var L=m.s.c;L<=m.e.c;++L){var D=_e({r:I,c:L});_=T?(h[I]||[])[L]:h[D],!(!_||_.v==null&&(!_.f||_.F))&&x.push(c(_,h,I,L))}return p.join(g)+g+x.join(g)+g+"E"+g}return{to_workbook:f,to_sheet:s,from_sheet:u}}(),Gf=function(){function e(i,s){switch(s.type){case"base64":return t(Ur(i),s);case"binary":return t(i,s);case"buffer":return t(Fe&&Buffer.isBuffer(i)?i.toString("binary"):Nt(i),s);case"array":return t(jt(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),c=-1,o=-1,l=0,u=[];l!==f.length;++l){if(f[l].trim()==="BOT"){u[++c]=[],o=0;continue}if(!(c<0)){var h=f[l].trim().split(","),d=h[0],p=h[1];++l;for(var x=f[l]||"";(x.match(/["]/g)||[]).length&1&&l<f.length-1;)x+=`
`+f[++l];switch(x=x.trim(),+d){case-1:if(x==="BOT"){u[++c]=[],o=0;continue}else if(x!=="EOD")throw new Error("Unrecognized DIF special command "+x);break;case 0:x==="TRUE"?u[c][o]=!0:x==="FALSE"?u[c][o]=!1:isNaN(et(p))?isNaN(pa(p).getDate())?u[c][o]=p:u[c][o]=Ve(p):u[c][o]=et(p),++o;break;case 1:x=x.slice(1,x.length-1),x=x.replace(/""/g,'"'),x&&x.match(/^=".*"$/)&&(x=x.slice(2,-1)),u[c][o++]=x!==""?x:null;break}if(x==="EOD")break}}return s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),u}function r(i,s){return wa(e(i,s),s)}function a(i,s){return Pt(r(i,s),s)}var n=function(){var i=function(c,o,l,u,h){c.push(o),c.push(l+","+u),c.push('"'+h.replace(/"/g,'""')+'"')},s=function(c,o,l,u){c.push(o+","+l),c.push(o==1?'"'+u.replace(/"/g,'""')+'"':u)};return function(c){var o=[],l=be(c["!ref"]),u,h=Array.isArray(c);i(o,"TABLE",0,1,"sheetjs"),i(o,"VECTORS",0,l.e.r-l.s.r+1,""),i(o,"TUPLES",0,l.e.c-l.s.c+1,""),i(o,"DATA",0,0,"");for(var d=l.s.r;d<=l.e.r;++d){s(o,-1,0,"BOT");for(var p=l.s.c;p<=l.e.c;++p){var x=_e({r:d,c:p});if(u=h?(c[d]||[])[p]:c[x],!u){s(o,1,0,"");continue}switch(u.t){case"n":var m=u.w;!m&&u.v!=null&&(m=u.v),m==null?u.f&&!u.F?s(o,1,0,"="+u.f):s(o,1,0,""):s(o,0,m,"V");break;case"b":s(o,0,u.v?1:0,u.v?"TRUE":"FALSE");break;case"s":s(o,1,0,isNaN(u.v)?u.v:'="'+u.v+'"');break;case"d":u.w||(u.w=Yr(u.z||ge[14],fr(Ve(u.v)))),s(o,0,u.w,"V");break;default:s(o,1,0,"")}}}s(o,-1,0,"EOD");var _=`\r
`,T=o.join(_);return T}}();return{to_workbook:a,to_sheet:r,from_sheet:n}}(),Xf=function(){function e(u){return u.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(u){return u.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(u,h){for(var d=u.split(`
`),p=-1,x=-1,m=0,_=[];m!==d.length;++m){var T=d[m].trim().split(":");if(T[0]==="cell"){var g=Ke(T[1]);if(_.length<=g.r)for(p=_.length;p<=g.r;++p)_[p]||(_[p]=[]);switch(p=g.r,x=g.c,T[2]){case"t":_[p][x]=e(T[3]);break;case"v":_[p][x]=+T[3];break;case"vtf":var I=T[T.length-1];case"vtc":switch(T[3]){case"nl":_[p][x]=!!+T[4];break;default:_[p][x]=+T[4];break}T[2]=="vtf"&&(_[p][x]=[_[p][x],I])}}}return h&&h.sheetRows&&(_=_.slice(0,h.sheetRows)),_}function a(u,h){return wa(r(u,h),h)}function n(u,h){return Pt(a(u,h),h)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),c="--SocialCalcSpreadsheetControlSave--";function o(u){if(!u||!u["!ref"])return"";for(var h=[],d=[],p,x="",m=Rr(u["!ref"]),_=Array.isArray(u),T=m.s.r;T<=m.e.r;++T)for(var g=m.s.c;g<=m.e.c;++g)if(x=_e({r:T,c:g}),p=_?(u[T]||[])[g]:u[x],!(!p||p.v==null||p.t==="z")){switch(d=["cell",x,"t"],p.t){case"s":case"str":d.push(t(p.v));break;case"n":p.f?(d[2]="vtf",d[3]="n",d[4]=p.v,d[5]=t(p.f)):(d[2]="v",d[3]=p.v);break;case"b":d[2]="vt"+(p.f?"f":"c"),d[3]="nl",d[4]=p.v?"1":"0",d[5]=t(p.f||(p.v?"TRUE":"FALSE"));break;case"d":var I=fr(Ve(p.v));d[2]="vtc",d[3]="nd",d[4]=""+I,d[5]=p.w||Yr(p.z||ge[14],I);break;case"e":continue}h.push(d.join(":"))}return h.push("sheet:c:"+(m.e.c-m.s.c+1)+":r:"+(m.e.r-m.s.r+1)+":tvf:1"),h.push("valueformat:1:text-wiki"),h.join(`
`)}function l(u){return[i,s,f,s,o(u),c].join(`
`)}return{to_workbook:n,to_sheet:a,from_sheet:l}}(),ma=function(){function e(l,u,h,d,p){p.raw?u[h][d]=l:l===""||(l==="TRUE"?u[h][d]=!0:l==="FALSE"?u[h][d]=!1:isNaN(et(l))?isNaN(pa(l).getDate())?u[h][d]=l:u[h][d]=Ve(l):u[h][d]=et(l))}function t(l,u){var h=u||{},d=[];if(!l||l.length===0)return d;for(var p=l.split(/[\r\n]/),x=p.length-1;x>=0&&p[x].length===0;)--x;for(var m=10,_=0,T=0;T<=x;++T)_=p[T].indexOf(" "),_==-1?_=p[T].length:_++,m=Math.max(m,_);for(T=0;T<=x;++T){d[T]=[];var g=0;for(e(p[T].slice(0,m).trim(),d,T,g,h),g=1;g<=(p[T].length-m)/10+1;++g)e(p[T].slice(m+(g-1)*10,m+g*10).trim(),d,T,g,h)}return h.sheetRows&&(d=d.slice(0,h.sheetRows)),d}var r={44:",",9:"	",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(l){for(var u={},h=!1,d=0,p=0;d<l.length;++d)(p=l.charCodeAt(d))==34?h=!h:!h&&p in r&&(u[p]=(u[p]||0)+1);p=[];for(d in u)Object.prototype.hasOwnProperty.call(u,d)&&p.push([u[d],d]);if(!p.length){u=a;for(d in u)Object.prototype.hasOwnProperty.call(u,d)&&p.push([u[d],d])}return p.sort(function(x,m){return x[0]-m[0]||a[x[1]]-a[m[1]]}),r[p.pop()[1]]||44}function i(l,u){var h=u||{},d="",p=h.dense?[]:{},x={s:{c:0,r:0},e:{c:0,r:0}};l.slice(0,4)=="sep="?l.charCodeAt(5)==13&&l.charCodeAt(6)==10?(d=l.charAt(4),l=l.slice(7)):l.charCodeAt(5)==13||l.charCodeAt(5)==10?(d=l.charAt(4),l=l.slice(6)):d=n(l.slice(0,1024)):h&&h.FS?d=h.FS:d=n(l.slice(0,1024));var m=0,_=0,T=0,g=0,I=0,L=d.charCodeAt(0),D=!1,k=0,R=l.charCodeAt(0);l=l.replace(/\r\n/mg,`
`);var O=h.dateNF!=null?tl(h.dateNF):null;function X(){var M=l.slice(g,I),P={};if(M.charAt(0)=='"'&&M.charAt(M.length-1)=='"'&&(M=M.slice(1,-1).replace(/""/g,'"')),M.length===0)P.t="z";else if(h.raw)P.t="s",P.v=M;else if(M.trim().length===0)P.t="s",P.v=M;else if(M.charCodeAt(0)==61)M.charCodeAt(1)==34&&M.charCodeAt(M.length-1)==34?(P.t="s",P.v=M.slice(2,-1).replace(/""/g,'"')):Id(M)?(P.t="n",P.f=M.slice(1)):(P.t="s",P.v=M);else if(M=="TRUE")P.t="b",P.v=!0;else if(M=="FALSE")P.t="b",P.v=!1;else if(!isNaN(T=et(M)))P.t="n",h.cellText!==!1&&(P.w=M),P.v=T;else if(!isNaN(pa(M).getDate())||O&&M.match(O)){P.z=h.dateNF||ge[14];var J=0;O&&M.match(O)&&(M=al(M,h.dateNF,M.match(O)||[]),J=1),h.cellDates?(P.t="d",P.v=Ve(M,J)):(P.t="n",P.v=fr(Ve(M,J))),h.cellText!==!1&&(P.w=Yr(P.z,P.v instanceof Date?fr(P.v):P.v)),h.cellNF||delete P.z}else P.t="s",P.v=M;if(P.t=="z"||(h.dense?(p[m]||(p[m]=[]),p[m][_]=P):p[_e({c:_,r:m})]=P),g=I+1,R=l.charCodeAt(g),x.e.c<_&&(x.e.c=_),x.e.r<m&&(x.e.r=m),k==L)++_;else if(_=0,++m,h.sheetRows&&h.sheetRows<=m)return!0}e:for(;I<l.length;++I)switch(k=l.charCodeAt(I)){case 34:R===34&&(D=!D);break;case L:case 10:case 13:if(!D&&X())break e;break}return I-g>0&&X(),p["!ref"]=Ee(x),p}function s(l,u){return!(u&&u.PRN)||u.FS||l.slice(0,4)=="sep="||l.indexOf("	")>=0||l.indexOf(",")>=0||l.indexOf(";")>=0?i(l,u):wa(t(l,u),u)}function f(l,u){var h="",d=u.type=="string"?[0,0,0,0]:ri(l,u);switch(u.type){case"base64":h=Ur(l);break;case"binary":h=l;break;case"buffer":u.codepage==65001?h=l.toString("utf8"):u.codepage&&typeof yt<"u"?h=yt.utils.decode(u.codepage,l):h=Fe&&Buffer.isBuffer(l)?l.toString("binary"):Nt(l);break;case"array":h=jt(l);break;case"string":h=l;break;default:throw new Error("Unrecognized type "+u.type)}return d[0]==239&&d[1]==187&&d[2]==191?h=Be(h.slice(3)):u.type!="string"&&u.type!="buffer"&&u.codepage==65001?h=Be(h):u.type=="binary"&&typeof yt<"u"&&u.codepage&&(h=yt.utils.decode(u.codepage,yt.utils.encode(28591,h))),h.slice(0,19)=="socialcalc:version:"?Xf.to_sheet(u.type=="string"?h:Be(h),u):s(h,u)}function c(l,u){return Pt(f(l,u),u)}function o(l){for(var u=[],h=be(l["!ref"]),d,p=Array.isArray(l),x=h.s.r;x<=h.e.r;++x){for(var m=[],_=h.s.c;_<=h.e.c;++_){var T=_e({r:x,c:_});if(d=p?(l[x]||[])[_]:l[T],!d||d.v==null){m.push("          ");continue}for(var g=(d.w||(_t(d),d.w)||"").slice(0,10);g.length<10;)g+=" ";m.push(g+(_===0?" ":""))}u.push(m.join(""))}return u.join(`
`)}return{to_workbook:c,to_sheet:f,from_sheet:o}}();function m1(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=Hf.to_workbook(e,r);return r.WTF=a,n}catch(i){if(r.WTF=a,!i.message.match(/SYLK bad record ID/)&&a)throw i;return ma.to_workbook(e,t)}}var zt=function(){function e(C,B,b){if(C){xr(C,C.l||0);for(var N=b.Enum||pe;C.l<C.length;){var j=C.read_shift(2),ae=N[j]||N[65535],ne=C.read_shift(2),re=C.l+ne,Q=ae.f&&ae.f(C,ne,b);if(C.l=re,B(Q,ae,j))return}}}function t(C,B){switch(B.type){case"base64":return r(Br(Ur(C)),B);case"binary":return r(Br(C),B);case"buffer":case"array":return r(C,B)}throw"Unsupported type "+B.type}function r(C,B){if(!C)return C;var b=B||{},N=b.dense?[]:{},j="Sheet1",ae="",ne=0,re={},Q=[],Se=[],A={s:{r:0,c:0},e:{r:0,c:0}},Ue=b.sheetRows||0;if(C[2]==0&&(C[3]==8||C[3]==9)&&C.length>=16&&C[14]==5&&C[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(C[2]==2)b.Enum=pe,e(C,function(oe,qe,Wr){switch(Wr){case 0:b.vers=oe,oe>=4096&&(b.qpro=!0);break;case 6:A=oe;break;case 204:oe&&(ae=oe);break;case 222:ae=oe;break;case 15:case 51:b.qpro||(oe[1].v=oe[1].v.slice(1));case 13:case 14:case 16:Wr==14&&(oe[2]&112)==112&&(oe[2]&15)>1&&(oe[2]&15)<15&&(oe[1].z=b.dateNF||ge[14],b.cellDates&&(oe[1].t="d",oe[1].v=L0(oe[1].v))),b.qpro&&oe[3]>ne&&(N["!ref"]=Ee(A),re[j]=N,Q.push(j),N=b.dense?[]:{},A={s:{r:0,c:0},e:{r:0,c:0}},ne=oe[3],j=ae||"Sheet"+(ne+1),ae="");var tt=b.dense?(N[oe[0].r]||[])[oe[0].c]:N[_e(oe[0])];if(tt){tt.t=oe[1].t,tt.v=oe[1].v,oe[1].z!=null&&(tt.z=oe[1].z),oe[1].f!=null&&(tt.f=oe[1].f);break}b.dense?(N[oe[0].r]||(N[oe[0].r]=[]),N[oe[0].r][oe[0].c]=oe[1]):N[_e(oe[0])]=oe[1];break}},b);else if(C[2]==26||C[2]==14)b.Enum=ve,C[2]==14&&(b.qpro=!0,C.l=0),e(C,function(oe,qe,Wr){switch(Wr){case 204:j=oe;break;case 22:oe[1].v=oe[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(oe[3]>ne&&(N["!ref"]=Ee(A),re[j]=N,Q.push(j),N=b.dense?[]:{},A={s:{r:0,c:0},e:{r:0,c:0}},ne=oe[3],j="Sheet"+(ne+1)),Ue>0&&oe[0].r>=Ue)break;b.dense?(N[oe[0].r]||(N[oe[0].r]=[]),N[oe[0].r][oe[0].c]=oe[1]):N[_e(oe[0])]=oe[1],A.e.c<oe[0].c&&(A.e.c=oe[0].c),A.e.r<oe[0].r&&(A.e.r=oe[0].r);break;case 27:oe[14e3]&&(Se[oe[14e3][0]]=oe[14e3][1]);break;case 1537:Se[oe[0]]=oe[1],oe[0]==ne&&(j=oe[1]);break}},b);else throw new Error("Unrecognized LOTUS BOF "+C[2]);if(N["!ref"]=Ee(A),re[ae||j]=N,Q.push(ae||j),!Se.length)return{SheetNames:Q,Sheets:re};for(var De={},Me=[],Ce=0;Ce<Se.length;++Ce)re[Q[Ce]]?(Me.push(Se[Ce]||Q[Ce]),De[Se[Ce]]=re[Se[Ce]]||re[Q[Ce]]):(Me.push(Se[Ce]),De[Se[Ce]]={"!ref":"A1"});return{SheetNames:Me,Sheets:De}}function a(C,B){var b=B||{};if(+b.codepage>=0&&Zr(+b.codepage),b.type=="string")throw new Error("Cannot write WK1 to JS string");var N=br(),j=be(C["!ref"]),ae=Array.isArray(C),ne=[];se(N,0,i(1030)),se(N,6,c(j));for(var re=Math.min(j.e.r,8191),Q=j.s.r;Q<=re;++Q)for(var Se=er(Q),A=j.s.c;A<=j.e.c;++A){Q===j.s.r&&(ne[A]=$e(A));var Ue=ne[A]+Se,De=ae?(C[Q]||[])[A]:C[Ue];if(!(!De||De.t=="z"))if(De.t=="n")(De.v|0)==De.v&&De.v>=-32768&&De.v<=32767?se(N,13,d(Q,A,De.v)):se(N,14,x(Q,A,De.v));else{var Me=_t(De);se(N,15,u(Q,A,Me.slice(0,239)))}}return se(N,1),N.end()}function n(C,B){var b=B||{};if(+b.codepage>=0&&Zr(+b.codepage),b.type=="string")throw new Error("Cannot write WK3 to JS string");var N=br();se(N,0,s(C));for(var j=0,ae=0;j<C.SheetNames.length;++j)(C.Sheets[C.SheetNames[j]]||{})["!ref"]&&se(N,27,V(C.SheetNames[j],ae++));var ne=0;for(j=0;j<C.SheetNames.length;++j){var re=C.Sheets[C.SheetNames[j]];if(!(!re||!re["!ref"])){for(var Q=be(re["!ref"]),Se=Array.isArray(re),A=[],Ue=Math.min(Q.e.r,8191),De=Q.s.r;De<=Ue;++De)for(var Me=er(De),Ce=Q.s.c;Ce<=Q.e.c;++Ce){De===Q.s.r&&(A[Ce]=$e(Ce));var oe=A[Ce]+Me,qe=Se?(re[De]||[])[Ce]:re[oe];if(!(!qe||qe.t=="z"))if(qe.t=="n")se(N,23,X(De,Ce,ne,qe.v));else{var Wr=_t(qe);se(N,22,k(De,Ce,ne,Wr.slice(0,239)))}}++ne}}return se(N,1),N.end()}function i(C){var B=z(2);return B.write_shift(2,C),B}function s(C){var B=z(26);B.write_shift(2,4096),B.write_shift(2,4),B.write_shift(4,0);for(var b=0,N=0,j=0,ae=0;ae<C.SheetNames.length;++ae){var ne=C.SheetNames[ae],re=C.Sheets[ne];if(!(!re||!re["!ref"])){++j;var Q=Rr(re["!ref"]);b<Q.e.r&&(b=Q.e.r),N<Q.e.c&&(N=Q.e.c)}}return b>8191&&(b=8191),B.write_shift(2,b),B.write_shift(1,j),B.write_shift(1,N),B.write_shift(2,0),B.write_shift(2,0),B.write_shift(1,1),B.write_shift(1,2),B.write_shift(4,0),B.write_shift(4,0),B}function f(C,B,b){var N={s:{c:0,r:0},e:{c:0,r:0}};return B==8&&b.qpro?(N.s.c=C.read_shift(1),C.l++,N.s.r=C.read_shift(2),N.e.c=C.read_shift(1),C.l++,N.e.r=C.read_shift(2),N):(N.s.c=C.read_shift(2),N.s.r=C.read_shift(2),B==12&&b.qpro&&(C.l+=2),N.e.c=C.read_shift(2),N.e.r=C.read_shift(2),B==12&&b.qpro&&(C.l+=2),N.s.c==65535&&(N.s.c=N.e.c=N.s.r=N.e.r=0),N)}function c(C){var B=z(8);return B.write_shift(2,C.s.c),B.write_shift(2,C.s.r),B.write_shift(2,C.e.c),B.write_shift(2,C.e.r),B}function o(C,B,b){var N=[{c:0,r:0},{t:"n",v:0},0,0];return b.qpro&&b.vers!=20768?(N[0].c=C.read_shift(1),N[3]=C.read_shift(1),N[0].r=C.read_shift(2),C.l+=2):(N[2]=C.read_shift(1),N[0].c=C.read_shift(2),N[0].r=C.read_shift(2)),N}function l(C,B,b){var N=C.l+B,j=o(C,B,b);if(j[1].t="s",b.vers==20768){C.l++;var ae=C.read_shift(1);return j[1].v=C.read_shift(ae,"utf8"),j}return b.qpro&&C.l++,j[1].v=C.read_shift(N-C.l,"cstr"),j}function u(C,B,b){var N=z(7+b.length);N.write_shift(1,255),N.write_shift(2,B),N.write_shift(2,C),N.write_shift(1,39);for(var j=0;j<N.length;++j){var ae=b.charCodeAt(j);N.write_shift(1,ae>=128?95:ae)}return N.write_shift(1,0),N}function h(C,B,b){var N=o(C,B,b);return N[1].v=C.read_shift(2,"i"),N}function d(C,B,b){var N=z(7);return N.write_shift(1,255),N.write_shift(2,B),N.write_shift(2,C),N.write_shift(2,b,"i"),N}function p(C,B,b){var N=o(C,B,b);return N[1].v=C.read_shift(8,"f"),N}function x(C,B,b){var N=z(13);return N.write_shift(1,255),N.write_shift(2,B),N.write_shift(2,C),N.write_shift(8,b,"f"),N}function m(C,B,b){var N=C.l+B,j=o(C,B,b);if(j[1].v=C.read_shift(8,"f"),b.qpro)C.l=N;else{var ae=C.read_shift(2);I(C.slice(C.l,C.l+ae),j),C.l+=ae}return j}function _(C,B,b){var N=B&32768;return B&=-32769,B=(N?C:0)+(B>=8192?B-16384:B),(N?"":"$")+(b?$e(B):er(B))}var T={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},g=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function I(C,B){xr(C,0);for(var b=[],N=0,j="",ae="",ne="",re="";C.l<C.length;){var Q=C[C.l++];switch(Q){case 0:b.push(C.read_shift(8,"f"));break;case 1:ae=_(B[0].c,C.read_shift(2),!0),j=_(B[0].r,C.read_shift(2),!1),b.push(ae+j);break;case 2:{var Se=_(B[0].c,C.read_shift(2),!0),A=_(B[0].r,C.read_shift(2),!1);ae=_(B[0].c,C.read_shift(2),!0),j=_(B[0].r,C.read_shift(2),!1),b.push(Se+A+":"+ae+j)}break;case 3:if(C.l<C.length){console.error("WK1 premature formula end");return}break;case 4:b.push("("+b.pop()+")");break;case 5:b.push(C.read_shift(2));break;case 6:{for(var Ue="";Q=C[C.l++];)Ue+=String.fromCharCode(Q);b.push('"'+Ue.replace(/"/g,'""')+'"')}break;case 8:b.push("-"+b.pop());break;case 23:b.push("+"+b.pop());break;case 22:b.push("NOT("+b.pop()+")");break;case 20:case 21:re=b.pop(),ne=b.pop(),b.push(["AND","OR"][Q-20]+"("+ne+","+re+")");break;default:if(Q<32&&g[Q])re=b.pop(),ne=b.pop(),b.push(ne+g[Q]+re);else if(T[Q]){if(N=T[Q][1],N==69&&(N=C[C.l++]),N>b.length){console.error("WK1 bad formula parse 0x"+Q.toString(16)+":|"+b.join("|")+"|");return}var De=b.slice(-N);b.length-=N,b.push(T[Q][0]+"("+De.join(",")+")")}else return Q<=7?console.error("WK1 invalid opcode "+Q.toString(16)):Q<=24?console.error("WK1 unsupported op "+Q.toString(16)):Q<=30?console.error("WK1 invalid opcode "+Q.toString(16)):Q<=115?console.error("WK1 unsupported function opcode "+Q.toString(16)):console.error("WK1 unrecognized opcode "+Q.toString(16))}}b.length==1?B[1].f=""+b[0]:console.error("WK1 bad formula parse |"+b.join("|")+"|")}function L(C){var B=[{c:0,r:0},{t:"n",v:0},0];return B[0].r=C.read_shift(2),B[3]=C[C.l++],B[0].c=C[C.l++],B}function D(C,B){var b=L(C);return b[1].t="s",b[1].v=C.read_shift(B-4,"cstr"),b}function k(C,B,b,N){var j=z(6+N.length);j.write_shift(2,C),j.write_shift(1,b),j.write_shift(1,B),j.write_shift(1,39);for(var ae=0;ae<N.length;++ae){var ne=N.charCodeAt(ae);j.write_shift(1,ne>=128?95:ne)}return j.write_shift(1,0),j}function R(C,B){var b=L(C);b[1].v=C.read_shift(2);var N=b[1].v>>1;if(b[1].v&1)switch(N&7){case 0:N=(N>>3)*5e3;break;case 1:N=(N>>3)*500;break;case 2:N=(N>>3)/20;break;case 3:N=(N>>3)/200;break;case 4:N=(N>>3)/2e3;break;case 5:N=(N>>3)/2e4;break;case 6:N=(N>>3)/16;break;case 7:N=(N>>3)/64;break}return b[1].v=N,b}function O(C,B){var b=L(C),N=C.read_shift(4),j=C.read_shift(4),ae=C.read_shift(2);if(ae==65535)return N===0&&j===3221225472?(b[1].t="e",b[1].v=15):N===0&&j===3489660928?(b[1].t="e",b[1].v=42):b[1].v=0,b;var ne=ae&32768;return ae=(ae&32767)-16446,b[1].v=(1-ne*2)*(j*Math.pow(2,ae+32)+N*Math.pow(2,ae)),b}function X(C,B,b,N){var j=z(14);if(j.write_shift(2,C),j.write_shift(1,b),j.write_shift(1,B),N==0)return j.write_shift(4,0),j.write_shift(4,0),j.write_shift(2,65535),j;var ae=0,ne=0,re=0,Q=0;return N<0&&(ae=1,N=-N),ne=Math.log2(N)|0,N/=Math.pow(2,ne-31),Q=N>>>0,Q&2147483648||(N/=2,++ne,Q=N>>>0),N-=Q,Q|=2147483648,Q>>>=0,N*=Math.pow(2,32),re=N>>>0,j.write_shift(4,re),j.write_shift(4,Q),ne+=16383+(ae?32768:0),j.write_shift(2,ne),j}function M(C,B){var b=O(C);return C.l+=B-14,b}function P(C,B){var b=L(C),N=C.read_shift(4);return b[1].v=N>>6,b}function J(C,B){var b=L(C),N=C.read_shift(8,"f");return b[1].v=N,b}function le(C,B){var b=J(C);return C.l+=B-10,b}function Z(C,B){return C[C.l+B-1]==0?C.read_shift(B,"cstr"):""}function ue(C,B){var b=C[C.l++];b>B-1&&(b=B-1);for(var N="";N.length<b;)N+=String.fromCharCode(C[C.l++]);return N}function ce(C,B,b){if(!(!b.qpro||B<21)){var N=C.read_shift(1);C.l+=17,C.l+=1,C.l+=2;var j=C.read_shift(B-21,"cstr");return[N,j]}}function Ie(C,B){for(var b={},N=C.l+B;C.l<N;){var j=C.read_shift(2);if(j==14e3){for(b[j]=[0,""],b[j][0]=C.read_shift(2);C[C.l];)b[j][1]+=String.fromCharCode(C[C.l]),C.l++;C.l++}}return b}function V(C,B){var b=z(5+C.length);b.write_shift(2,14e3),b.write_shift(2,B);for(var N=0;N<C.length;++N){var j=C.charCodeAt(N);b[b.l++]=j>127?95:j}return b[b.l++]=0,b}var pe={0:{n:"BOF",f:nr},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:h},14:{n:"NUMBER",f:p},15:{n:"LABEL",f:l},16:{n:"FORMULA",f:m},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:l},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:Z},222:{n:"SHEETNAMELP",f:ue},65535:{n:""}},ve={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:D},23:{n:"NUMBER17",f:O},24:{n:"NUMBER18",f:R},25:{n:"FORMULA19",f:M},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Ie},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:P},38:{n:"??"},39:{n:"NUMBER27",f:J},40:{n:"FORMULA28",f:le},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:Z},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:ce},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:t}}();function v1(e){var t={},r=e.match(Sr),a=0,n=!1;if(r)for(;a!=r.length;++a){var i=we(r[a]);switch(i[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!i.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if(i.val=="1")break;t.cp=gn[parseInt(i.val,10)];break;case"<outline":if(!i.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=i.val;break;case"<sz":t.sz=i.val;break;case"<strike":if(!i.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!i.val)break;switch(i.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting";break}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if(i.val=="0")break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if(i.val=="0")break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":i.rgb&&(t.color=i.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=i.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=i.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(i[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+i[0])}}return t}var g1=function(){var e=Va("t"),t=Va("rPr");function r(i){var s=i.match(e);if(!s)return{t:"s",v:""};var f={t:"s",v:Oe(s[1])},c=i.match(t);return c&&(f.s=v1(c[1])),f}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(s){return s.replace(a,"").split(n).map(r).filter(function(f){return f.v})}}(),_1=function(){var t=/(\r\n|\n)/g;function r(n,i,s){var f=[];n.u&&f.push("text-decoration: underline;"),n.uval&&f.push("text-underline-style:"+n.uval+";"),n.sz&&f.push("font-size:"+n.sz+"pt;"),n.outline&&f.push("text-effect: outline;"),n.shadow&&f.push("text-shadow: auto;"),i.push('<span style="'+f.join("")+'">'),n.b&&(i.push("<b>"),s.push("</b>")),n.i&&(i.push("<i>"),s.push("</i>")),n.strike&&(i.push("<s>"),s.push("</s>"));var c=n.valign||"";return c=="superscript"||c=="super"?c="sup":c=="subscript"&&(c="sub"),c!=""&&(i.push("<"+c+">"),s.push("</"+c+">")),s.push("</span>"),n}function a(n){var i=[[],n.v,[]];return n.v?(n.s&&r(n.s,i[0],i[2]),i[0].join("")+i[1].replace(t,"<br/>")+i[2].join("")):""}return function(i){return i.map(a).join("")}}(),w1=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,k1=/<(?:\w+:)?r>/,E1=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Hn(e,t){var r=t?t.cellHTML:!0,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=Oe(Be(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=Be(e),r&&(a.h=Cn(a.t))):e.match(k1)&&(a.r=Be(e),a.t=Oe(Be((e.replace(E1,"").match(w1)||[]).join("").replace(Sr,""))),r&&(a.h=_1(g1(a.r)))),a):{t:""}}var T1=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,S1=/<(?:\w+:)?(?:si|sstItem)>/g,F1=/<\/(?:\w+:)?(?:si|sstItem)>/;function y1(e,t){var r=[],a="";if(!e)return r;var n=e.match(T1);if(n){a=n[2].replace(S1,"").split(F1);for(var i=0;i!=a.length;++i){var s=Hn(a[i].trim(),t);s!=null&&(r[r.length]=s)}n=we(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}var A1=/^\s|\s$|[\t\n\r]/;function zf(e,t){if(!t.bookSST)return"";var r=[rr];r[r.length]=ie("sst",null,{xmlns:Qt[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(e[a]!=null){var n=e[a],i="<si>";n.r?i+=n.r:(i+="<t",n.t||(n.t=""),n.t.match(A1)&&(i+=' xml:space="preserve"'),i+=">"+Le(n.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function C1(e){return[e.read_shift(4),e.read_shift(4)]}function D1(e,t){var r=[],a=!1;return kt(e,function(i,s,f){switch(f){case 159:r.Count=i[0],r.Unique=i[1];break;case 19:r.push(i);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(s.T,!a||t.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}),r}function I1(e,t){return t||(t=z(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var O1=Vl;function b1(e){var t=br();Y(t,159,I1(e));for(var r=0;r<e.length;++r)Y(t,19,O1(e[r]));return Y(t,160),t.end()}function $f(e){for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function gt(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function R1(e){var t={};return t.id=e.read_shift(0,"lpp4"),t.R=gt(e,4),t.U=gt(e,4),t.W=gt(e,4),t}function N1(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),i=[];n-- >0;)i.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=i,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function P1(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(N1(e));return t}function L1(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}function B1(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=gt(e,4),t.U=gt(e,4),t.W=gt(e,4),t}function M1(e){var t=B1(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return t}function Kf(e,t){var r=e.l+t,a={};a.Flags=e.read_shift(4)&63,e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=a.Flags==36;break;case 26625:n=a.Flags==4;break;case 0:n=a.Flags==16||a.Flags==4||a.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function jf(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function U1(e){var t=gt(e);switch(t.Minor){case 2:return[t.Minor,W1(e)];case 3:return[t.Minor,V1()];case 4:return[t.Minor,H1(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function W1(e){var t=e.read_shift(4);if((t&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),a=Kf(e,r),n=jf(e,e.length-e.l);return{t:"Std",h:a,v:n}}function V1(){throw new Error("File is password-protected: ECMA-376 Extensible")}function H1(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(Sr,function(i){var s=we(i);switch(ot(s[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":t.forEach(function(f){a[f]=s[f]});break;case"<dataIntegrity":a.encryptedHmacKey=s.encryptedHmacKey,a.encryptedHmacValue=s.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=s.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(s);break;default:throw s[0]}}),a}function G1(e,t){var r={},a=r.EncryptionVersionInfo=gt(e,4);if(t-=4,a.Minor!=2)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=Kf(e,n),t-=n,r.EncryptionVerifier=jf(e,t),r}function X1(e){var t={},r=t.EncryptionVersionInfo=gt(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}function Gn(e){var t=0,r,a=$f(e),n=a.length+1,i,s,f,c,o;for(r=It(n),r[0]=a.length,i=1;i!=n;++i)r[i]=a[i-1];for(i=n-1;i>=0;--i)s=r[i],f=t&16384?1:0,c=t<<1&32767,o=f|c,t=o^s;return t^52811}var Yf=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(s){return(s/2|s*128)&255},n=function(s,f){return a(s^f)},i=function(s){for(var f=t[s.length-1],c=104,o=s.length-1;o>=0;--o)for(var l=s[o],u=0;u!=7;++u)l&64&&(f^=r[c]),l*=2,--c;return f};return function(s){for(var f=$f(s),c=i(f),o=f.length,l=It(16),u=0;u!=16;++u)l[u]=0;var h,d,p;for((o&1)===1&&(h=c>>8,l[o]=n(e[0],h),--o,h=c&255,d=f[f.length-1],l[o]=n(d,h));o>0;)--o,h=c>>8,l[o]=n(f[o],h),--o,h=c&255,l[o]=n(f[o],h);for(o=15,p=15-f.length;p>0;)h=c>>8,l[o]=n(e[p],h),--o,--p,h=c&255,l[o]=n(f[o],h),--o,--p;return l}}(),z1=function(e,t,r,a,n){n||(n=t),a||(a=Yf(e));var i,s;for(i=0;i!=t.length;++i)s=t[i],s^=a[r],s=(s>>5|s<<3)&255,n[i]=s,++r;return[n,r,a]},$1=function(e){var t=0,r=Yf(e);return function(a){var n=z1("",a,t,r);return t=n[1],n[0]}};function K1(e,t,r,a){var n={key:nr(e),verificationBytes:nr(e)};return r.password&&(n.verifier=Gn(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=$1(r.password)),n}function j1(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,a.Info===1?a.Data=X1(e):a.Data=G1(e,t),a}function Y1(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?j1(e,t-2,a):K1(e,r.biff>=8?t:t-2,r,a),a}var Jf=function(){function e(n,i){switch(i.type){case"base64":return t(Ur(n),i);case"binary":return t(n,i);case"buffer":return t(Fe&&Buffer.isBuffer(n)?n.toString("binary"):Nt(n),i);case"array":return t(jt(n),i)}throw new Error("Unrecognized type "+i.type)}function t(n,i){var s=i||{},f=s.dense?[]:{},c=n.match(/\\trowd.*?\\row\b/g);if(!c.length)throw new Error("RTF missing table");var o={s:{c:0,r:0},e:{c:0,r:c.length-1}};return c.forEach(function(l,u){Array.isArray(f)&&(f[u]=[]);for(var h=/\\\w+\b/g,d=0,p,x=-1;p=h.exec(l);){switch(p[0]){case"\\cell":var m=l.slice(d,h.lastIndex-p[0].length);if(m[0]==" "&&(m=m.slice(1)),++x,m.length){var _={v:m,t:"s"};Array.isArray(f)?f[u][x]=_:f[_e({r:u,c:x})]=_}break}d=h.lastIndex}x>o.e.c&&(o.e.c=x)}),f["!ref"]=Ee(o),f}function r(n,i){return Pt(e(n,i),i)}function a(n){for(var i=["{\\rtf1\\ansi"],s=be(n["!ref"]),f,c=Array.isArray(n),o=s.s.r;o<=s.e.r;++o){i.push("\\trowd\\trautofit1");for(var l=s.s.c;l<=s.e.c;++l)i.push("\\cellx"+(l+1));for(i.push("\\pard\\intbl"),l=s.s.c;l<=s.e.c;++l){var u=_e({r:o,c:l});f=c?(n[o]||[])[l]:n[u],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(_t(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:a}}();function J1(e){var t=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}function za(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function q1(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),i=Math.min(t,r,a),s=n-i;if(s===0)return[0,0,t];var f=0,c=0,o=n+i;switch(c=s/(o>1?2-o:o),n){case t:f=((r-a)/s+6)%6;break;case r:f=(a-t)/s+2;break;case a:f=(t-r)/s+4;break}return[f/6,c,o/2]}function Z1(e){var t=e[0],r=e[1],a=e[2],n=r*2*(a<.5?a:1-a),i=a-n/2,s=[i,i,i],f=6*t,c;if(r!==0)switch(f|0){case 0:case 6:c=n*f,s[0]+=n,s[1]+=c;break;case 1:c=n*(2-f),s[0]+=c,s[1]+=n;break;case 2:c=n*(f-2),s[1]+=n,s[2]+=c;break;case 3:c=n*(4-f),s[1]+=c,s[2]+=n;break;case 4:c=n*(f-4),s[2]+=n,s[0]+=c;break;case 5:c=n*(6-f),s[2]+=c,s[0]+=n;break}for(var o=0;o!=3;++o)s[o]=Math.round(s[o]*255);return s}function D0(e,t){if(t===0)return e;var r=q1(J1(e));return t<0?r[2]=r[2]*(1+t):r[2]=1-(1-r[2])*(1-t),za(Z1(r))}var qf=6,Q1=15,ex=1,wr=qf;function $a(e){return Math.floor((e+Math.round(128/wr)/256)*wr)}function Ka(e){return Math.floor((e-5)/wr*100+.5)/100}function I0(e){return Math.round((e*wr+5)/wr*256)/256}function j0(e){return I0(Ka($a(e)))}function Xn(e){var t=Math.abs(e-j0(e)),r=wr;if(t>.005)for(wr=ex;wr<Q1;++wr)Math.abs(e-j0(e))<=t&&(t=Math.abs(e-j0(e)),r=wr);wr=r}function Ot(e){e.width?(e.wpx=$a(e.width),e.wch=Ka(e.wpx),e.MDW=wr):e.wpx?(e.wch=Ka(e.wpx),e.width=I0(e.wch),e.MDW=wr):typeof e.wch=="number"&&(e.width=I0(e.wch),e.wpx=$a(e.width),e.MDW=wr),e.customWidth&&delete e.customWidth}var rx=96,Zf=rx;function ja(e){return e*96/Zf}function va(e){return e*Zf/96}var tx={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function ax(e,t,r,a){t.Borders=[];var n={},i=!1;(e[0].match(Sr)||[]).forEach(function(s){var f=we(s);switch(ot(f[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":n={},f.diagonalUp&&(n.diagonalUp=We(f.diagonalUp)),f.diagonalDown&&(n.diagonalDown=We(f.diagonalDown)),t.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in borders")}})}function nx(e,t,r,a){t.Fills=[];var n={},i=!1;(e[0].match(Sr)||[]).forEach(function(s){var f=we(s);switch(ot(f[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":f.patternType&&(n.patternType=f.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":n.bgColor||(n.bgColor={}),f.indexed&&(n.bgColor.indexed=parseInt(f.indexed,10)),f.theme&&(n.bgColor.theme=parseInt(f.theme,10)),f.tint&&(n.bgColor.tint=parseFloat(f.tint)),f.rgb&&(n.bgColor.rgb=f.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":n.fgColor||(n.fgColor={}),f.theme&&(n.fgColor.theme=parseInt(f.theme,10)),f.tint&&(n.fgColor.tint=parseFloat(f.tint)),f.rgb!=null&&(n.fgColor.rgb=f.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in fills")}})}function ix(e,t,r,a){t.Fonts=[];var n={},i=!1;(e[0].match(Sr)||[]).forEach(function(s){var f=we(s);switch(ot(f[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":f.val&&(n.name=Be(f.val));break;case"<name/>":case"</name>":break;case"<b":n.bold=f.val?We(f.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=f.val?We(f.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(f.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=f.val?We(f.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=f.val?We(f.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=f.val?We(f.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=f.val?We(f.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=f.val?We(f.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":f.val&&(n.sz=+f.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":f.val&&(n.vertAlign=f.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":f.val&&(n.family=parseInt(f.val,10));break;case"<family/>":case"</family>":break;case"<scheme":f.val&&(n.scheme=f.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(f.val=="1")break;f.codepage=gn[parseInt(f.val,10)];break;case"<color":if(n.color||(n.color={}),f.auto&&(n.color.auto=We(f.auto)),f.rgb)n.color.rgb=f.rgb.slice(-6);else if(f.indexed){n.color.index=parseInt(f.indexed,10);var c=Gt[n.color.index];n.color.index==81&&(c=Gt[1]),c||(c=Gt[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else f.theme&&(n.color.theme=parseInt(f.theme,10),f.tint&&(n.color.tint=parseFloat(f.tint)),f.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=D0(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":i=!0;break;case"</AlternateContent>":i=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(a&&a.WTF&&!i)throw new Error("unrecognized "+f[0]+" in fonts")}})}function sx(e,t,r){t.NumberFmt=[];for(var a=Je(ge),n=0;n<a.length;++n)t.NumberFmt[a[n]]=ge[a[n]];var i=e[0].match(Sr);if(i)for(n=0;n<i.length;++n){var s=we(i[n]);switch(ot(s[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var f=Oe(Be(s.formatCode)),c=parseInt(s.numFmtId,10);if(t.NumberFmt[c]=f,c>0){if(c>392){for(c=392;c>60&&t.NumberFmt[c]!=null;--c);t.NumberFmt[c]=f}vt(f,c)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+s[0]+" in numFmts")}}}function fx(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)e[a]!=null&&(t[t.length]=ie("numFmt",null,{numFmtId:a,formatCode:Le(e[a])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=ie("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var d0=["numFmtId","fillId","fontId","borderId","xfId"],p0=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function cx(e,t,r){t.CellXf=[];var a,n=!1;(e[0].match(Sr)||[]).forEach(function(i){var s=we(i),f=0;switch(ot(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(a=s,delete a[0],f=0;f<d0.length;++f)a[d0[f]]&&(a[d0[f]]=parseInt(a[d0[f]],10));for(f=0;f<p0.length;++f)a[p0[f]]&&(a[p0[f]]=We(a[p0[f]]));if(t.NumberFmt&&a.numFmtId>392){for(f=392;f>60;--f)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[f]){a.numFmtId=f;break}}t.CellXf.push(a);break;case"</xf>":break;case"<alignment":case"<alignment/>":var c={};s.vertical&&(c.vertical=s.vertical),s.horizontal&&(c.horizontal=s.horizontal),s.textRotation!=null&&(c.textRotation=s.textRotation),s.indent&&(c.indent=s.indent),s.wrapText&&(c.wrapText=We(s.wrapText)),a.alignment=c;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":n=!0;break;case"</AlternateContent>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}})}function ox(e){var t=[];return t[t.length]=ie("cellXfs",null),e.forEach(function(r){t[t.length]=ie("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=ie("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var lx=function(){var t=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,a=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,n=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,i=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(f,c,o){var l={};if(!f)return l;f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var u;return(u=f.match(t))&&sx(u,l,o),(u=f.match(n))&&ix(u,l,c,o),(u=f.match(a))&&nx(u,l,c,o),(u=f.match(i))&&ax(u,l,c,o),(u=f.match(r))&&cx(u,l,o),l}}();function Qf(e,t){var r=[rr,ie("styleSheet",null,{xmlns:Qt[0],"xmlns:vt":sr.vt})],a;return e.SSF&&(a=fx(e.SSF))!=null&&(r[r.length]=a),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(a=ox(t.cellXfs))&&(r[r.length]=a),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function ux(e,t){var r=e.read_shift(2),a=Er(e);return[r,a]}function hx(e,t,r){r||(r=z(6+4*t.length)),r.write_shift(2,e),lr(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),a}function xx(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=jl(e);n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1);var i=e.read_shift(2);switch(i===700&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(a.underline=s);var f=e.read_shift(1);f>0&&(a.family=f);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=Kl(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break}return a.name=Er(e),a}function dx(e,t){t||(t=z(25+4*32)),t.write_shift(2,e.sz*20),Yl(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),y0(e.color,t);var a=0;return e.scheme=="major"&&(a=1),e.scheme=="minor"&&(a=2),t.write_shift(1,a),lr(e.name,t),t.length>t.l?t.slice(0,t.l):t}var px=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Y0,mx=Tr;function es(e,t){t||(t=z(4*3+8*7+16*1)),Y0||(Y0=N0(px));var r=Y0[e.patternType];r==null&&(r=40),t.write_shift(4,r);var a=0;if(r!=40)for(y0({auto:1},t),y0({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function vx(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}function ec(e,t,r){r||(r=z(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var a=0;return r.write_shift(1,a),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Aa(e,t){return t||(t=z(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var gx=Tr;function _x(e,t){return t||(t=z(51)),t.write_shift(1,0),Aa(null,t),Aa(null,t),Aa(null,t),Aa(null,t),Aa(null,t),t.length>t.l?t.slice(0,t.l):t}function wx(e,t){return t||(t=z(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),F0(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function kx(e,t,r){var a=z(2052);return a.write_shift(4,e),F0(t,a),F0(r,a),a.length>a.l?a.slice(0,a.l):a}function Ex(e,t,r){var a={};a.NumberFmt=[];for(var n in ge)a.NumberFmt[n]=ge[n];a.CellXf=[],a.Fonts=[];var i=[],s=!1;return kt(e,function(c,o,l){switch(l){case 44:a.NumberFmt[c[0]]=c[1],vt(c[1],c[0]);break;case 43:a.Fonts.push(c),c.color.theme!=null&&t&&t.themeElements&&t.themeElements.clrScheme&&(c.color.rgb=D0(t.themeElements.clrScheme[c.color.theme].rgb,c.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:i[i.length-1]==617&&a.CellXf.push(c);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:i.push(l),s=!0;break;case 38:i.pop(),s=!1;break;default:if(o.T>0)i.push(l);else if(o.T<0)i.pop();else if(!s||r.WTF&&i[i.length-1]!=37)throw new Error("Unexpected record 0x"+l.toString(16))}}),a}function Tx(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&++r}),r!=0&&(Y(e,615,rt(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&Y(e,44,hx(n,t[n]))}),Y(e,616))}}function Sx(e){var t=1;Y(e,611,rt(t)),Y(e,43,dx({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),Y(e,612)}function Fx(e){var t=2;Y(e,603,rt(t)),Y(e,45,es({patternType:"none"})),Y(e,45,es({patternType:"gray125"})),Y(e,604)}function yx(e){var t=1;Y(e,613,rt(t)),Y(e,46,_x()),Y(e,614)}function Ax(e){var t=1;Y(e,626,rt(t)),Y(e,47,ec({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),Y(e,627)}function Cx(e,t){Y(e,617,rt(t.length)),t.forEach(function(r){Y(e,47,ec(r,0))}),Y(e,618)}function Dx(e){var t=1;Y(e,619,rt(t)),Y(e,48,wx({xfId:0,builtinId:0,name:"Normal"})),Y(e,620)}function Ix(e){var t=0;Y(e,505,rt(t)),Y(e,506)}function Ox(e){var t=0;Y(e,508,kx(t,"TableStyleMedium9","PivotStyleMedium4")),Y(e,509)}function bx(e,t){var r=br();return Y(r,278),Tx(r,e.SSF),Sx(r),Fx(r),yx(r),Ax(r),Cx(r,t.cellXfs),Dx(r),Ix(r),Ox(r),Y(r,279),r.end()}var Rx=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Nx(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(Sr)||[]).forEach(function(n){var i=we(n);switch(i[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=i.val;break;case"<a:sysClr":a.rgb=i.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":i[0].charAt(1)==="/"?(t.themeElements.clrScheme[Rx.indexOf(i[0])]=a,a={}):a.name=i[0].slice(3,i[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+i[0]+" in clrScheme")}})}function Px(){}function Lx(){}var Bx=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,Mx=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Ux=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function Wx(e,t,r){t.themeElements={};var a;[["clrScheme",Bx,Nx],["fontScheme",Mx,Px],["fmtScheme",Ux,Lx]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)})}var Vx=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function rc(e,t){(!e||e.length===0)&&(e=zn());var r,a={};if(!(r=e.match(Vx)))throw new Error("themeElements not found in theme");return Wx(r[0],a,t),a.raw=e,a}function zn(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[rr];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function Hx(e,t,r){var a=e.l+t,n=e.read_shift(4);if(n!==124226){if(!r.cellStyles){e.l=a;return}var i=e.slice(e.l);e.l=a;var s;try{s=$s(i,{type:"array"})}catch{return}var f=Mr(s,"theme/theme/theme1.xml",!0);if(f)return rc(f,r)}}function Gx(e){return e.read_shift(4)}function Xx(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=zx(e,4);break;case 2:t.xclrValue=Nf(e);break;case 3:t.xclrValue=Gx(e);break;case 4:e.l+=4;break}return e.l+=8,t}function zx(e,t){return Tr(e,t)}function $x(e,t){return Tr(e,t)}function Kx(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=Xx(e);break;case 6:a[1]=$x(e,r);break;case 14:case 15:a[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function jx(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),i=[];n-- >0;)i.push(Kx(e,r-e.l));return{ixfe:a,ext:i}}function Yx(e,t){t.forEach(function(r){switch(r[0]){}})}function Jx(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Er(e)}}function qx(e){var t=z(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),lr(e.name,t),t.slice(0,t.l)}function Zx(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function Qx(e){var t=z(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function ed(e,t){var r=z(8+2*t.length);return r.write_shift(4,e),lr(t,r),r.slice(0,r.l)}function rd(e){return e.l+=4,e.read_shift(4)!=0}function td(e,t){var r=z(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function ad(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},i=[],s=!1,f=2;return kt(e,function(c,o,l){switch(l){case 335:a.Types.push({name:c.name});break;case 51:c.forEach(function(u){f==1?a.Cell.push({type:a.Types[u[0]-1].name,index:u[1]}):f==0&&a.Value.push({type:a.Types[u[0]-1].name,index:u[1]})});break;case 337:f=c?1:0;break;case 338:f=2;break;case 35:i.push(l),s=!0;break;case 36:i.pop(),s=!1;break;default:if(!o.T){if(!s||n.WTF&&i[i.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}}),a}function nd(){var e=br();return Y(e,332),Y(e,334,rt(1)),Y(e,335,qx({name:"XLDAPR",version:12e4,flags:3496657072})),Y(e,336),Y(e,339,ed(1,"XLDAPR")),Y(e,52),Y(e,35,rt(514)),Y(e,4096,rt(0)),Y(e,4097,Kr(1)),Y(e,36),Y(e,53),Y(e,340),Y(e,337,td(1,!0)),Y(e,51,Qx([[1,0]])),Y(e,338),Y(e,333),e.end()}function id(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=!1,i=2,s;return e.replace(Sr,function(f){var c=we(f);switch(ot(c[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:c.name});break;case"</metadataType>":break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==c.name&&(s=a.Types[o]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":i==1?a.Cell.push({type:a.Types[c.t-1].name,index:+c.v}):i==0&&a.Value.push({type:a.Types[c.t-1].name,index:+c.v});break;case"</rc>":break;case"<cellMetadata":i=1;break;case"</cellMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"</valueMetadata>":i=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<rvb":if(!s)break;s.offsets||(s.offsets=[]),s.offsets.push(+c.i);break;default:if(!n&&r.WTF)throw new Error("unrecognized "+c[0]+" in metadata")}return f}),a}function tc(){var e=[rr];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function sd(e){var t=[];if(!e)return t;var r=1;return(e.match(Sr)||[]).forEach(function(a){var n=we(a);switch(n[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete n[0],n.i?r=n.i:n.i=r,t.push(n);break}}),t}function fd(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=_e(r);var a=e.read_shift(1);return a&2&&(t.l="1"),a&8&&(t.a="1"),t}function cd(e,t,r){var a=[];return kt(e,function(i,s,f){switch(f){case 63:a.push(i);break;default:if(!s.T)throw new Error("Unexpected record 0x"+f.toString(16))}}),a}function od(e,t,r,a){if(!e)return e;var n=a||{},i=!1;kt(e,function(f,c,o){switch(o){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:i=!0;break;case 36:i=!1;break;default:if(!c.T){if(!i||n.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}},n)}function ld(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}var oa=1024;function ac(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[ie("xml",null,{"xmlns:v":Pr.v,"xmlns:o":Pr.o,"xmlns:x":Pr.x,"xmlns:mv":Pr.mv}).replace(/\/>/,">"),ie("o:shapelayout",ie("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),ie("v:shapetype",[ie("v:stroke",null,{joinstyle:"miter"}),ie("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];oa<e*1e3;)oa+=1e3;return t.forEach(function(i){var s=Ke(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var c=f.type=="gradient"?ie("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,o=ie("v:fill",c,f),l={on:"t",obscured:"t"};++oa,n=n.concat(["<v:shape"+Ha({id:"_x0000_s"+oa,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",o,ie("v:shadow",null,l),ie("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",dr("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),dr("x:AutoFill","False"),dr("x:Row",String(s.r)),dr("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function rs(e,t,r,a){var n=Array.isArray(e),i;t.forEach(function(s){var f=Ke(s.ref);if(n?(e[f.r]||(e[f.r]=[]),i=e[f.r][f.c]):i=e[s.ref],!i){i={t:"z"},n?e[f.r][f.c]=i:e[s.ref]=i;var c=be(e["!ref"]||"BDWGO1000001:A1");c.s.r>f.r&&(c.s.r=f.r),c.e.r<f.r&&(c.e.r=f.r),c.s.c>f.c&&(c.s.c=f.c),c.e.c<f.c&&(c.e.c=f.c);var o=Ee(c);o!==e["!ref"]&&(e["!ref"]=o)}i.c||(i.c=[]);var l={a:s.author,t:s.t,r:s.r,T:r};s.h&&(l.h=s.h);for(var u=i.c.length-1;u>=0;--u){if(!r&&i.c[u].T)return;r&&!i.c[u].T&&i.c.splice(u,1)}if(r&&a){for(u=0;u<a.length;++u)if(l.a==a[u].id){l.a=a[u].name||l.a;break}}i.c.push(l)})}function ud(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach(function(s){if(!(s===""||s.trim()==="")){var f=s.match(/<(?:\w+:)?author[^>]*>(.*)/);f&&r.push(f[1])}});var i=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return i&&i[1]&&i[1].split(/<\/\w*:?comment>/).forEach(function(s){if(!(s===""||s.trim()==="")){var f=s.match(/<(?:\w+:)?comment[^>]*>/);if(f){var c=we(f[0]),o={author:c.authorId&&r[c.authorId]||"sheetjsghost",ref:c.ref,guid:c.guid},l=Ke(c.ref);if(!(t.sheetRows&&t.sheetRows<=l.r)){var u=s.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),h=!!u&&!!u[1]&&Hn(u[1])||{r:"",t:"",h:""};o.r=h.r,h.r=="<t></t>"&&(h.t=h.h=""),o.t=(h.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),t.cellHTML&&(o.h=h.h),a.push(o)}}}}),a}function nc(e){var t=[rr,ie("comments",null,{xmlns:Qt[0]})],r=[];return t.push("<authors>"),e.forEach(function(a){a[1].forEach(function(n){var i=Le(n.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),n.T&&n.ID&&r.indexOf("tc="+n.ID)==-1&&(r.push("tc="+n.ID),t.push("<author>tc="+n.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(a){var n=0,i=[];if(a[1][0]&&a[1][0].T&&a[1][0].ID?n=r.indexOf("tc="+a[1][0].ID):a[1].forEach(function(c){c.a&&(n=r.indexOf(Le(c.a))),i.push(c.t||"")}),t.push('<comment ref="'+a[0]+'" authorId="'+n+'"><text>'),i.length<=1)t.push(dr("t",Le(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(dr("t",Le(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function hd(e,t){var r=[],a=!1,n={},i=0;return e.replace(Sr,function(f,c){var o=we(f);switch(ot(o[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:o.personId,guid:o.id,ref:o.ref,T:1};break;case"</threadedComment>":n.t!=null&&r.push(n);break;case"<text>":case"<text":i=c+f.length;break;case"</text>":n.t=e.slice(i,c).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":a=!0;break;case"</mentions>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+o[0]+" in threaded comments")}return f}),r}function xd(e,t,r){var a=[rr,ie("ThreadedComments",null,{xmlns:sr.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(n){var i="";(n[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var c={ref:n[0],id:"{54EE7951-**************-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=c.id:c.parentId=i,s.ID=c.id,s.a&&(c.personId="{54EE7950-**************-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(ie("threadedComment",dr("text",s.t||""),c))})}),a.push("</ThreadedComments>"),a.join("")}function dd(e,t){var r=[],a=!1;return e.replace(Sr,function(i){var s=we(i);switch(ot(s[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:s.displayname,id:s.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+s[0]+" in threaded comments")}return i}),r}function pd(e){var t=[rr,ie("personList",null,{xmlns:sr.TCMNT,"xmlns:x":Qt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,a){t.push(ie("person",null,{displayName:r,id:"{54EE7950-**************-"+("000000000000"+a).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function md(e){var t={};t.iauthor=e.read_shift(4);var r=aa(e);return t.rfx=r.s,t.ref=_e(r.s),e.l+=16,t}function vd(e,t){return t==null&&(t=z(36)),t.write_shift(4,e[1].iauthor),ka(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var gd=Er;function _d(e){return lr(e.slice(0,54))}function wd(e,t){var r=[],a=[],n={},i=!1;return kt(e,function(f,c,o){switch(o){case 632:a.push(f);break;case 635:n=f;break;case 637:n.t=f.t,n.h=f.h,n.r=f.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!c.T){if(!i||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}}),r}function kd(e){var t=br(),r=[];return Y(t,628),Y(t,630),e.forEach(function(a){a[1].forEach(function(n){r.indexOf(n.a)>-1||(r.push(n.a.slice(0,54)),Y(t,632,_d(n.a)))})}),Y(t,631),Y(t,633),e.forEach(function(a){a[1].forEach(function(n){n.iauthor=r.indexOf(n.a);var i={s:Ke(a[0]),e:Ke(a[0])};Y(t,635,vd([i,n])),n.t&&n.t.length>0&&Y(t,637,Gl(n)),Y(t,636),delete n.iauthor})}),Y(t,634),Y(t,629),t.end()}var Ed="application/vnd.ms-office.vbaProject";function Td(e){var t=me.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,a){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");me.utils.cfb_add(t,n,e.FileIndex[a].content)}}),me.write(t)}function Sd(e,t){t.FullPaths.forEach(function(r,a){if(a!=0){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");n.slice(-1)!=="/"&&me.utils.cfb_add(e,n,t.FileIndex[a].content)}})}var ic=["xlsb","xlsm","xlam","biff8","xla"];function Fd(){return{"!type":"dialog"}}function yd(){return{"!type":"dialog"}}function Ad(){return{"!type":"macro"}}function Cd(){return{"!type":"macro"}}var xa=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(a,n,i,s){var f=!1,c=!1;i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var o=i.length>0?parseInt(i,10)|0:0,l=s.length>0?parseInt(s,10)|0:0;return f?l+=t.c:--l,c?o+=t.r:--o,n+(f?"":"$")+$e(l)+(c?"":"$")+er(o)}return function(n,i){return t=i,n.replace(e,r)}}(),$n=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Kn=function(){return function(t,r){return t.replace($n,function(a,n,i,s,f,c){var o=Rn(s)-(i?0:r.c),l=bn(c)-(f?0:r.r),u=l==0?"":f?l+1:"["+l+"]",h=o==0?"":i?o+1:"["+o+"]";return n+"R"+u+"C"+h})}}();function sc(e,t){return e.replace($n,function(r,a,n,i,s,f){return a+(n=="$"?n+i:$e(Rn(i)+t.c))+(s=="$"?s+f:er(bn(f)+t.r))})}function Dd(e,t,r){var a=Rr(t),n=a.s,i=Ke(r),s={r:i.r-n.r,c:i.c-n.c};return sc(e,s)}function Id(e){return e.length!=1}function ts(e){return e.replace(/_xlfn\./g,"")}function tr(e){e.l+=1}function bt(e,t){var r=e.read_shift(t==1?1:2);return[r&16383,r>>14&1,r>>15&1]}function fc(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return cc(e);r.biff==12&&(a=4)}var n=e.read_shift(a),i=e.read_shift(a),s=bt(e,2),f=bt(e,2);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function cc(e){var t=bt(e,2),r=bt(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function Od(e,t,r){if(r.biff<8)return cc(e);var a=e.read_shift(r.biff==12?4:2),n=e.read_shift(r.biff==12?4:2),i=bt(e,2),s=bt(e,2);return{s:{r:a,c:i[0],cRel:i[1],rRel:i[2]},e:{r:n,c:s[0],cRel:s[1],rRel:s[2]}}}function oc(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return bd(e);var a=e.read_shift(r&&r.biff==12?4:2),n=bt(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function bd(e){var t=bt(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function Rd(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Nd(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return Pd(e);var n=e.read_shift(a>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;n>524287;)n-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:n,c:i,cRel:s,rRel:f}}function Pd(e){var t=e.read_shift(2),r=e.read_shift(1),a=(t&32768)>>15,n=(t&16384)>>14;return t&=16383,a==1&&t>=8192&&(t=t-16384),n==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:n,rRel:a}}function Ld(e,t,r){var a=(e[e.l++]&96)>>5,n=fc(e,r.biff>=2&&r.biff<=5?6:8,r);return[a,n]}function Bd(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=fc(e,i,r);return[a,n,s]}function Md(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}function Ud(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[a,n]}function Wd(e,t,r){var a=(e[e.l++]&96)>>5,n=Od(e,t-1,r);return[a,n]}function Vd(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[a]}function as(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Hd(e,t,r){e.l+=2;for(var a=e.read_shift(r&&r.biff==2?1:2),n=[],i=0;i<=a;++i)n.push(e.read_shift(r&&r.biff==2?1:2));return n}function Gd(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function Xd(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function zd(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function $d(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[a]}function lc(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function Kd(e){return e.read_shift(2),lc(e)}function jd(e){return e.read_shift(2),lc(e)}function Yd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=oc(e,0,r);return[a,n]}function Jd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=Nd(e,0,r);return[a,n]}function qd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=oc(e,0,r);return[a,n,i]}function Zd(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[rm[n],xc[n],a]}function Qd(e,t,r){var a=e[e.l++],n=e.read_shift(1),i=r&&r.biff<=3?[a==88?-1:0,e.read_shift(1)]:ep(e);return[n,(i[0]===0?xc:em)[i[1]]]}function ep(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function rp(e,t,r){e.l+=r&&r.biff==2?3:4}function tp(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var a=e.read_shift(2),n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function ap(e){return e.l++,Et[e.read_shift(1)]}function np(e){return e.l++,e.read_shift(2)}function ip(e){return e.l++,e.read_shift(1)!==0}function sp(e){return e.l++,kr(e)}function fp(e,t,r){return e.l++,e0(e,t-1,r)}function cp(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Qe(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Et[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=kr(e);break;case 2:r[1]=na(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function op(e,t,r){for(var a=e.read_shift(r.biff==12?4:2),n=[],i=0;i!=a;++i)n.push((r.biff==12?aa:B0)(e));return n}function lp(e,t,r){var a=0,n=0;r.biff==12?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,--n==0&&(n=256));for(var i=0,s=[];i!=a&&(s[i]=[]);++i)for(var f=0;f!=n;++f)s[i][f]=cp(e,r.biff);return s}function up(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,i=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[a,0,i]}function hp(e,t,r){if(r.biff==5)return xp(e);var a=e.read_shift(1)>>>5&3,n=e.read_shift(2),i=e.read_shift(4);return[a,n,i]}function xp(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}function dp(e,t,r){var a=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function pp(e,t,r){var a=e.read_shift(1)>>>5&3,n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function mp(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[a]}function vp(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[a,n]}var gp=Tr,_p=Tr,wp=Tr;function t0(e,t,r){return e.l+=2,[Rd(e)]}function jn(e){return e.l+=6,[]}var kp=t0,Ep=jn,Tp=jn,Sp=t0;function uc(e){return e.l+=2,[nr(e),e.read_shift(2)&1]}var Fp=t0,yp=uc,Ap=jn,Cp=t0,Dp=t0,Ip=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Op(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),i=e.read_shift(2),s=Ip[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:a,c:n,C:i}}function bp(e){return e.l+=2,[e.read_shift(4)]}function Rp(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Np(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Pp(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Lp(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Bp(e){return e.l+=4,[0,0]}var ns={1:{n:"PtgExp",f:tp},2:{n:"PtgTbl",f:wp},3:{n:"PtgAdd",f:tr},4:{n:"PtgSub",f:tr},5:{n:"PtgMul",f:tr},6:{n:"PtgDiv",f:tr},7:{n:"PtgPower",f:tr},8:{n:"PtgConcat",f:tr},9:{n:"PtgLt",f:tr},10:{n:"PtgLe",f:tr},11:{n:"PtgEq",f:tr},12:{n:"PtgGe",f:tr},13:{n:"PtgGt",f:tr},14:{n:"PtgNe",f:tr},15:{n:"PtgIsect",f:tr},16:{n:"PtgUnion",f:tr},17:{n:"PtgRange",f:tr},18:{n:"PtgUplus",f:tr},19:{n:"PtgUminus",f:tr},20:{n:"PtgPercent",f:tr},21:{n:"PtgParen",f:tr},22:{n:"PtgMissArg",f:tr},23:{n:"PtgStr",f:fp},26:{n:"PtgSheet",f:Rp},27:{n:"PtgEndSheet",f:Np},28:{n:"PtgErr",f:ap},29:{n:"PtgBool",f:ip},30:{n:"PtgInt",f:np},31:{n:"PtgNum",f:sp},32:{n:"PtgArray",f:Vd},33:{n:"PtgFunc",f:Zd},34:{n:"PtgFuncVar",f:Qd},35:{n:"PtgName",f:up},36:{n:"PtgRef",f:Yd},37:{n:"PtgArea",f:Ld},38:{n:"PtgMemArea",f:dp},39:{n:"PtgMemErr",f:gp},40:{n:"PtgMemNoMem",f:_p},41:{n:"PtgMemFunc",f:pp},42:{n:"PtgRefErr",f:mp},43:{n:"PtgAreaErr",f:Md},44:{n:"PtgRefN",f:Jd},45:{n:"PtgAreaN",f:Wd},46:{n:"PtgMemAreaN",f:Pp},47:{n:"PtgMemNoMemN",f:Lp},57:{n:"PtgNameX",f:hp},58:{n:"PtgRef3d",f:qd},59:{n:"PtgArea3d",f:Bd},60:{n:"PtgRefErr3d",f:vp},61:{n:"PtgAreaErr3d",f:Ud},255:{}},Mp={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Up={1:{n:"PtgElfLel",f:uc},2:{n:"PtgElfRw",f:Cp},3:{n:"PtgElfCol",f:kp},6:{n:"PtgElfRwV",f:Dp},7:{n:"PtgElfColV",f:Sp},10:{n:"PtgElfRadical",f:Fp},11:{n:"PtgElfRadicalS",f:Ap},13:{n:"PtgElfColS",f:Ep},15:{n:"PtgElfColSV",f:Tp},16:{n:"PtgElfRadicalLel",f:yp},25:{n:"PtgList",f:Op},29:{n:"PtgSxName",f:bp},255:{}},Wp={0:{n:"PtgAttrNoop",f:Bp},1:{n:"PtgAttrSemi",f:$d},2:{n:"PtgAttrIf",f:Xd},4:{n:"PtgAttrChoose",f:Hd},8:{n:"PtgAttrGoto",f:Gd},16:{n:"PtgAttrSum",f:rp},32:{n:"PtgAttrBaxcel",f:as},33:{n:"PtgAttrBaxcel",f:as},64:{n:"PtgAttrSpace",f:Kd},65:{n:"PtgAttrSpaceSemi",f:jd},128:{n:"PtgAttrIfError",f:zd},255:{}};function a0(e,t,r,a){if(a.biff<8)return Tr(e,t);for(var n=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=lp(e,0,a),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=op(e,r[s][1],a),i.push(r[s][2]);break;case"PtgExp":a&&a.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=n-e.l,t!==0&&i.push(Tr(e,t)),i}function n0(e,t,r){for(var a=e.l+t,n,i,s=[];a!=e.l;)t=a-e.l,i=e[e.l],n=ns[i]||ns[Mp[i]],(i===24||i===25)&&(n=(i===24?Up:Wp)[e[e.l+1]]),!n||!n.f?Tr(e,t):s.push([n.n,n.f(e,t,r)]);return s}function Vp(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],i=0;i<a.length;++i){var s=a[i];if(s)switch(s[0]){case 2:n.push('"'+s[1].replace(/"/g,'""')+'"');break;default:n.push(s[1])}else n.push("")}t.push(n.join(","))}return t.join(";")}var Hp={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Gp(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function hc(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=a[1]==-1?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];case 355:default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=a[1]==-1?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[a[0]][0][3]?(n=a[1]==-1?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function is(e,t,r){var a=hc(e,t,r);return a=="#REF"?a:Gp(a,r)}function _r(e,t,r,a,n){var i=n&&n.biff||8,s={s:{c:0,r:0},e:{c:0,r:0}},f=[],c,o,l,u=0,h=0,d,p="";if(!e[0]||!e[0][0])return"";for(var x=-1,m="",_=0,T=e[0].length;_<T;++_){var g=e[0][_];switch(g[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(c=f.pop(),o=f.pop(),x>=0){switch(e[0][x][1][0]){case 0:m=je(" ",e[0][x][1][1]);break;case 1:m=je("\r",e[0][x][1][1]);break;default:if(m="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][x][1][0])}o=o+m,x=-1}f.push(o+Hp[g[0]]+c);break;case"PtgIsect":c=f.pop(),o=f.pop(),f.push(o+" "+c);break;case"PtgUnion":c=f.pop(),o=f.pop(),f.push(o+","+c);break;case"PtgRange":c=f.pop(),o=f.pop(),f.push(o+":"+c);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":l=ba(g[1][1],s,n),f.push(Ra(l,i));break;case"PtgRefN":l=r?ba(g[1][1],r,n):g[1][1],f.push(Ra(l,i));break;case"PtgRef3d":u=g[1][1],l=ba(g[1][2],s,n),p=is(a,u,n),f.push(p+"!"+Ra(l,i));break;case"PtgFunc":case"PtgFuncVar":var I=g[1][0],L=g[1][1];I||(I=0),I&=127;var D=I==0?[]:f.slice(-I);f.length-=I,L==="User"&&(L=D.shift()),f.push(L+"("+D.join(",")+")");break;case"PtgBool":f.push(g[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(g[1]);break;case"PtgNum":f.push(String(g[1]));break;case"PtgStr":f.push('"'+g[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(g[1]);break;case"PtgAreaN":d=Ni(g[1][1],r?{s:r}:s,n),f.push(z0(d,n));break;case"PtgArea":d=Ni(g[1][1],s,n),f.push(z0(d,n));break;case"PtgArea3d":u=g[1][1],d=g[1][2],p=is(a,u,n),f.push(p+"!"+z0(d,n));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":h=g[1][2];var k=(a.names||[])[h-1]||(a[0]||[])[h],R=k?k.Name:"SH33TJSNAME"+String(h);R&&R.slice(0,6)=="_xlfn."&&!n.xlfn&&(R=R.slice(6)),f.push(R);break;case"PtgNameX":var O=g[1][1];h=g[1][2];var X;if(n.biff<=5)O<0&&(O=-O),a[O]&&(X=a[O][h]);else{var M="";if(((a[O]||[])[0]||[])[0]==14849||(((a[O]||[])[0]||[])[0]==1025?a[O][h]&&a[O][h].itab>0&&(M=a.SheetNames[a[O][h].itab-1]+"!"):M=a.SheetNames[h-1]+"!"),a[O]&&a[O][h])M+=a[O][h].Name;else if(a[0]&&a[0][h])M+=a[0][h].Name;else{var P=(hc(a,O,n)||"").split(";;");P[h-1]?M=P[h-1]:M+="SH33TJSERRX"}f.push(M);break}X||(X={Name:"SH33TJSERRY"}),f.push(X.Name);break;case"PtgParen":var J="(",le=")";if(x>=0){switch(m="",e[0][x][1][0]){case 2:J=je(" ",e[0][x][1][1])+J;break;case 3:J=je("\r",e[0][x][1][1])+J;break;case 4:le=je(" ",e[0][x][1][1])+le;break;case 5:le=je("\r",e[0][x][1][1])+le;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][x][1][0])}x=-1}f.push(J+f.pop()+le);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":l={c:g[1][1],r:g[1][0]};var Z={c:r.c,r:r.r};if(a.sharedf[_e(l)]){var ue=a.sharedf[_e(l)];f.push(_r(ue,s,Z,a,n))}else{var ce=!1;for(c=0;c!=a.arrayf.length;++c)if(o=a.arrayf[c],!(l.c<o[0].s.c||l.c>o[0].e.c)&&!(l.r<o[0].s.r||l.r>o[0].e.r)){f.push(_r(o[1],s,Z,a,n)),ce=!0;break}ce||f.push(g[1])}break;case"PtgArray":f.push("{"+Vp(g[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":x=_;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+g[1].idx+"[#"+g[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(g));default:throw new Error("Unrecognized Formula Token: "+String(g))}var Ie=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(n.biff!=3&&x>=0&&Ie.indexOf(e[0][_][0])==-1){g=e[0][x];var V=!0;switch(g[1][0]){case 4:V=!1;case 0:m=je(" ",g[1][1]);break;case 5:V=!1;case 1:m=je("\r",g[1][1]);break;default:if(m="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+g[1][0])}f.push((V?m:"")+f.pop()+(V?"":m)),x=-1}}if(f.length>1&&n.WTF)throw new Error("bad formula stack");return f[0]}function Xp(e,t,r){var a=e.l+t,n=r.biff==2?1:2,i,s=e.read_shift(n);if(s==65535)return[[],Tr(e,t-2)];var f=n0(e,s,r);return t!==s+n&&(i=a0(e,t-s-n,f,r)),e.l=a,[f,i]}function zp(e,t,r){var a=e.l+t,n=r.biff==2?1:2,i,s=e.read_shift(n);if(s==65535)return[[],Tr(e,t-2)];var f=n0(e,s,r);return t!==s+n&&(i=a0(e,t-s-n,f,r)),e.l=a,[f,i]}function $p(e,t,r,a){var n=e.l+t,i=n0(e,a,r),s;return n!==e.l&&(s=a0(e,n-e.l,i,r)),[i,s]}function Kp(e,t,r){var a=e.l+t,n,i=e.read_shift(2),s=n0(e,i,r);return i==65535?[[],Tr(e,t-2)]:(t!==i+2&&(n=a0(e,a-i-2,s,r)),[s,n])}function jp(e){var t;if(pt(e,e.l+6)!==65535)return[kr(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=e[e.l+2]===1,e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}function Yp(e){if(e==null){var t=z(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return Yt(e);return Yt(0)}function J0(e,t,r){var a=e.l+t,n=lt(e);r.biff==2&&++e.l;var i=jp(e),s=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var f=zp(e,a-e.l,r);return{cell:n,val:i[0],formula:f,shared:s>>3&1,tt:i[1]}}function Jp(e,t,r,a,n){var i=qt(t,r,n),s=Yp(e.v),f=z(6),c=33;f.write_shift(2,c),f.write_shift(4,0);for(var o=z(e.bf.length),l=0;l<e.bf.length;++l)o[l]=e.bf[l];var u=or([i,s,f,o]);return u}function M0(e,t,r){var a=e.read_shift(4),n=n0(e,a,r),i=e.read_shift(4),s=i>0?a0(e,i,n,r):null;return[n,s]}var qp=M0,U0=M0,Zp=M0,Qp=M0,em={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},xc={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},rm={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function ss(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(t,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function tm(e){var t="of:="+e.replace($n,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function q0(e){var t=e.split(":"),r=t[0].split(".")[0];return[r,t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}function am(e){return e.replace(/\./,"!")}var Pa={},da={},La=typeof Map<"u";function Yn(e,t,r){var a=0,n=e.length;if(r){if(La?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=La?r.get(t):r[t];a<i.length;++a)if(e[i[a]].t===t)return e.Count++,i[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t},e.Count++,e.Unique++,r&&(La?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function W0(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(wr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?a=Ka(t.wpx):t.wch!=null&&(a=t.wch),a>-1?(r.width=I0(a),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function $t(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Lt(e,t,r){var a=r.revssf[t.z!=null?t.z:"General"],n=60,i=e.length;if(a==null&&r.ssf){for(;n<392;++n)if(r.ssf[n]==null){vt(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=i;++n)if(e[n].numFmtId===a)return n;return e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function dc(e,t,r,a,n,i){try{a.cellNF&&(e.z=ge[t])}catch(f){if(a.WTF)throw f}if(!(e.t==="z"&&!a.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=Ve(e.v)),(!a||a.cellText!==!1)&&e.t!=="z")try{if(ge[t]==null&&vt(rl[t]||"General",t),e.t==="e")e.w=e.w||Et[e.v];else if(t===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Wa(e.v);else if(e.t==="d"){var s=fr(e.v);(s|0)===s?e.w=s.toString(10):e.w=Wa(s)}else{if(e.v===void 0)return"";e.w=Kt(e.v,da)}else e.t==="d"?e.w=Yr(t,fr(e.v),da):e.w=Yr(t,e.v,da)}catch(f){if(a.WTF)throw f}if(a.cellStyles&&r!=null)try{e.s=i.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=D0(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=D0(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(f){if(a.WTF&&i.Fills)throw f}}}function nm(e,t,r){if(e&&e["!ref"]){var a=be(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function im(e,t){var r=be(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=Ee(r))}var sm=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,fm=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,cm=/<(?:\w:)?hyperlink [^>]*>/mg,om=/"(\w*:\w*)"/,lm=/<(?:\w:)?col\b[^>]*[\/]?>/g,um=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,hm=/<(?:\w:)?pageMargins[^>]*\/>/g,pc=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,xm=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,dm=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function pm(e,t,r,a,n,i,s){if(!e)return e;a||(a={"!id":{}});var f=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},o="",l="",u=e.match(fm);u?(o=e.slice(0,u.index),l=e.slice(u.index+u[0].length)):o=l=e;var h=o.match(pc);h?Jn(h[0],f,n,r):(h=o.match(xm))&&vm(h[0],h[1]||"",f,n,r);var d=(o.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=o.slice(d,d+50).match(om);p&&im(f,p[1])}var x=o.match(dm);x&&x[1]&&Im(x[1],n);var m=[];if(t.cellStyles){var _=o.match(lm);_&&Fm(m,_)}u&&Rm(u[1],f,t,c,i,s);var T=l.match(um);T&&(f["!autofilter"]=Am(T[0]));var g=[],I=l.match(sm);if(I)for(d=0;d!=I.length;++d)g[d]=be(I[d].slice(I[d].indexOf('"')+1));var L=l.match(cm);L&&Em(f,L,a);var D=l.match(hm);if(D&&(f["!margins"]=Tm(we(D[0]))),!f["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(f["!ref"]=Ee(c)),t.sheetRows>0&&f["!ref"]){var k=be(f["!ref"]);t.sheetRows<=+k.e.r&&(k.e.r=t.sheetRows-1,k.e.r>c.e.r&&(k.e.r=c.e.r),k.e.r<k.s.r&&(k.s.r=k.e.r),k.e.c>c.e.c&&(k.e.c=c.e.c),k.e.c<k.s.c&&(k.s.c=k.e.c),f["!fullref"]=f["!ref"],f["!ref"]=Ee(k))}return m.length>0&&(f["!cols"]=m),g.length>0&&(f["!merges"]=g),f}function mm(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Ee(e[r])+'"/>';return t+"</mergeCells>"}function Jn(e,t,r,a){var n=we(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=Oe(Be(n.codeName)))}function vm(e,t,r,a,n){Jn(e.slice(0,e.indexOf(">")),r,a,n)}function gm(e,t,r,a,n){var i=!1,s={},f=null;if(a.bookType!=="xlsx"&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch{}i=!0,s.codeName=ct(Le(c))}if(e&&e["!outline"]){var o={summaryBelow:1,summaryRight:1};e["!outline"].above&&(o.summaryBelow=0),e["!outline"].left&&(o.summaryRight=0),f=(f||"")+ie("outlinePr",null,o)}!i&&!f||(n[n.length]=ie("sheetPr",f,s))}var _m=["objects","scenarios","selectLockedCells","selectUnlockedCells"],wm=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function km(e){var t={sheet:1};return _m.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),wm.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=Gn(e.password).toString(16).toUpperCase()),ie("sheetProtection",null,t)}function Em(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var i=we(Be(t[n]),!0);if(!i.ref)return;var s=((r||{})["!id"]||[])[i.id];s?(i.Target=s.Target,i.location&&(i.Target+="#"+Oe(i.location))):(i.Target="#"+Oe(i.location),s={Target:i.Target,TargetMode:"Internal"}),i.Rel=s,i.tooltip&&(i.Tooltip=i.tooltip,delete i.tooltip);for(var f=be(i.ref),c=f.s.r;c<=f.e.r;++c)for(var o=f.s.c;o<=f.e.c;++o){var l=_e({c:o,r:c});a?(e[c]||(e[c]=[]),e[c][o]||(e[c][o]={t:"z",v:void 0}),e[c][o].l=i):(e[l]||(e[l]={t:"z",v:void 0}),e[l].l=i)}}}function Tm(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}function Sm(e){return $t(e),ie("pageMargins",null,e)}function Fm(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=we(t[a],!0);n.hidden&&(n.hidden=We(n.hidden));var i=parseInt(n.min,10)-1,s=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,Xn(n.width)),Ot(n);i<=s;)e[i++]=He(n)}}function ym(e,t){for(var r=["<cols>"],a,n=0;n!=t.length;++n)(a=t[n])&&(r[r.length]=ie("col",null,W0(n,a)));return r[r.length]="</cols>",r.join("")}function Am(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}function Cm(e,t,r,a){var n=typeof e.ref=="string"?e.ref:Ee(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=Rr(n);s.s.r==s.e.r&&(s.e.r=Rr(t["!ref"]).e.r,n=Ee(s));for(var f=0;f<i.length;++f){var c=i[f];if(c.Name=="_xlnm._FilterDatabase"&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),ie("autoFilter",null,{ref:n})}var Dm=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Im(e,t){t.Views||(t.Views=[{}]),(e.match(Dm)||[]).forEach(function(r,a){var n=we(r);t.Views[a]||(t.Views[a]={}),+n.zoomScale&&(t.Views[a].zoom=+n.zoomScale),We(n.rightToLeft)&&(t.Views[a].RTL=!0)})}function Om(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),ie("sheetViews",ie("sheetView",null,n),{})}function bm(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var n="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=Et[e.v];break;case"d":a&&a.cellDates?n=Ve(e.v,-1).toISOString():(e=He(e),e.t="n",n=""+(e.v=fr(Ve(e.v)))),typeof e.z>"u"&&(e.z=ge[14]);break;default:n=e.v;break}var f=dr("v",Le(n)),c={r:t},o=Lt(a.cellXfs,e,a);switch(o!==0&&(c.s=o),e.t){case"n":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){f=dr("v",""+Yn(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var l=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=ie("f",Le(e.f),l)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),ie("c",f,c)}var Rm=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,i=Va("v"),s=Va("f");return function(c,o,l,u,h,d){for(var p=0,x="",m=[],_=[],T=0,g=0,I=0,L="",D,k,R=0,O=0,X,M,P=0,J=0,le=Array.isArray(d.CellXf),Z,ue=[],ce=[],Ie=Array.isArray(o),V=[],pe={},ve=!1,C=!!l.sheetStubs,B=c.split(t),b=0,N=B.length;b!=N;++b){x=B[b].trim();var j=x.length;if(j!==0){var ae=0;e:for(p=0;p<j;++p)switch(x[p]){case">":if(x[p-1]!="/"){++p;break e}if(l&&l.cellStyles){if(k=we(x.slice(ae,p),!0),R=k.r!=null?parseInt(k.r,10):R+1,O=-1,l.sheetRows&&l.sheetRows<R)continue;pe={},ve=!1,k.ht&&(ve=!0,pe.hpt=parseFloat(k.ht),pe.hpx=va(pe.hpt)),k.hidden=="1"&&(ve=!0,pe.hidden=!0),k.outlineLevel!=null&&(ve=!0,pe.level=+k.outlineLevel),ve&&(V[R-1]=pe)}break;case"<":ae=p;break}if(ae>=p)break;if(k=we(x.slice(ae,p),!0),R=k.r!=null?parseInt(k.r,10):R+1,O=-1,!(l.sheetRows&&l.sheetRows<R)){u.s.r>R-1&&(u.s.r=R-1),u.e.r<R-1&&(u.e.r=R-1),l&&l.cellStyles&&(pe={},ve=!1,k.ht&&(ve=!0,pe.hpt=parseFloat(k.ht),pe.hpx=va(pe.hpt)),k.hidden=="1"&&(ve=!0,pe.hidden=!0),k.outlineLevel!=null&&(ve=!0,pe.level=+k.outlineLevel),ve&&(V[R-1]=pe)),m=x.slice(p).split(e);for(var ne=0;ne!=m.length&&m[ne].trim().charAt(0)=="<";++ne);for(m=m.slice(ne),p=0;p!=m.length;++p)if(x=m[p].trim(),x.length!==0){if(_=x.match(r),T=p,g=0,I=0,x="<c "+(x.slice(0,1)=="<"?">":"")+x,_!=null&&_.length===2){for(T=0,L=_[1],g=0;g!=L.length&&!((I=L.charCodeAt(g)-64)<1||I>26);++g)T=26*T+I;--T,O=T}else++O;for(g=0;g!=x.length&&x.charCodeAt(g)!==62;++g);if(++g,k=we(x.slice(0,g),!0),k.r||(k.r=_e({r:R-1,c:O})),L=x.slice(g),D={t:""},(_=L.match(i))!=null&&_[1]!==""&&(D.v=Oe(_[1])),l.cellFormula){if((_=L.match(s))!=null&&_[1]!==""){if(D.f=Oe(Be(_[1])).replace(/\r\n/g,`
`),l.xlfn||(D.f=ts(D.f)),_[0].indexOf('t="array"')>-1)D.F=(L.match(n)||[])[1],D.F.indexOf(":")>-1&&ue.push([be(D.F),D.F]);else if(_[0].indexOf('t="shared"')>-1){M=we(_[0]);var re=Oe(Be(_[1]));l.xlfn||(re=ts(re)),ce[parseInt(M.si,10)]=[M,re,k.r]}}else(_=L.match(/<f[^>]*\/>/))&&(M=we(_[0]),ce[M.si]&&(D.f=Dd(ce[M.si][1],ce[M.si][2],k.r)));var Q=Ke(k.r);for(g=0;g<ue.length;++g)Q.r>=ue[g][0].s.r&&Q.r<=ue[g][0].e.r&&Q.c>=ue[g][0].s.c&&Q.c<=ue[g][0].e.c&&(D.F=ue[g][1])}if(k.t==null&&D.v===void 0)if(D.f||D.F)D.v=0,D.t="n";else if(C)D.t="z";else continue;else D.t=k.t||"n";switch(u.s.c>O&&(u.s.c=O),u.e.c<O&&(u.e.c=O),D.t){case"n":if(D.v==""||D.v==null){if(!C)continue;D.t="z"}else D.v=parseFloat(D.v);break;case"s":if(typeof D.v>"u"){if(!C)continue;D.t="z"}else X=Pa[parseInt(D.v,10)],D.v=X.t,D.r=X.r,l.cellHTML&&(D.h=X.h);break;case"str":D.t="s",D.v=D.v!=null?Be(D.v):"",l.cellHTML&&(D.h=Cn(D.v));break;case"inlineStr":_=L.match(a),D.t="s",_!=null&&(X=Hn(_[1]))?(D.v=X.t,l.cellHTML&&(D.h=X.h)):D.v="";break;case"b":D.v=We(D.v);break;case"d":l.cellDates?D.v=Ve(D.v,1):(D.v=fr(Ve(D.v,1)),D.t="n");break;case"e":(!l||l.cellText!==!1)&&(D.w=D.v),D.v=vf[D.v];break}if(P=J=0,Z=null,le&&k.s!==void 0&&(Z=d.CellXf[k.s],Z!=null&&(Z.numFmtId!=null&&(P=Z.numFmtId),l.cellStyles&&Z.fillId!=null&&(J=Z.fillId))),dc(D,P,J,l,h,d),l.cellDates&&le&&D.t=="n"&&ga(ge[P])&&(D.t="d",D.v=L0(D.v)),k.cm&&l.xlmeta){var Se=(l.xlmeta.Cell||[])[+k.cm-1];Se&&Se.type=="XLDAPR"&&(D.D=!0)}if(Ie){var A=Ke(k.r);o[A.r]||(o[A.r]=[]),o[A.r][A.c]=D}else o[k.r]=D}}}}V.length>0&&(o["!rows"]=V)}}();function Nm(e,t,r,a){var n=[],i=[],s=be(e["!ref"]),f="",c,o="",l=[],u=0,h=0,d=e["!rows"],p=Array.isArray(e),x={r:o},m,_=-1;for(h=s.s.c;h<=s.e.c;++h)l[h]=$e(h);for(u=s.s.r;u<=s.e.r;++u){for(i=[],o=er(u),h=s.s.c;h<=s.e.c;++h){c=l[h]+o;var T=p?(e[u]||[])[h]:e[c];T!==void 0&&(f=bm(T,c,e,t))!=null&&i.push(f)}(i.length>0||d&&d[u])&&(x={r:o},d&&d[u]&&(m=d[u],m.hidden&&(x.hidden=1),_=-1,m.hpx?_=ja(m.hpx):m.hpt&&(_=m.hpt),_>-1&&(x.ht=_,x.customHeight=1),m.level&&(x.outlineLevel=m.level)),n[n.length]=ie("row",i.join(""),x))}if(d)for(;u<d.length;++u)d&&d[u]&&(x={r:u+1},m=d[u],m.hidden&&(x.hidden=1),_=-1,m.hpx?_=ja(m.hpx):m.hpt&&(_=m.hpt),_>-1&&(x.ht=_,x.customHeight=1),m.level&&(x.outlineLevel=m.level),n[n.length]=ie("row","",x));return n.join("")}function mc(e,t,r,a){var n=[rr,ie("worksheet",null,{xmlns:Qt[0],"xmlns:r":sr.r})],i=r.SheetNames[e],s=0,f="",c=r.Sheets[i];c==null&&(c={});var o=c["!ref"]||"A1",l=be(o);if(l.e.c>16383||l.e.r>1048575){if(t.WTF)throw new Error("Range "+o+" exceeds format limit A1:XFD1048576");l.e.c=Math.min(l.e.c,16383),l.e.r=Math.min(l.e.c,1048575),o=Ee(l)}a||(a={}),c["!comments"]=[];var u=[];gm(c,r,e,t,n),n[n.length]=ie("dimension",null,{ref:o}),n[n.length]=Om(c,t,e,r),t.sheetFormat&&(n[n.length]=ie("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),c["!cols"]!=null&&c["!cols"].length>0&&(n[n.length]=ym(c,c["!cols"])),n[s=n.length]="<sheetData/>",c["!links"]=[],c["!ref"]!=null&&(f=Nm(c,t),f.length>0&&(n[n.length]=f)),n.length>s+1&&(n[n.length]="</sheetData>",n[s]=n[s].replace("/>",">")),c["!protect"]&&(n[n.length]=km(c["!protect"])),c["!autofilter"]!=null&&(n[n.length]=Cm(c["!autofilter"],c,r,e)),c["!merges"]!=null&&c["!merges"].length>0&&(n[n.length]=mm(c["!merges"]));var h=-1,d,p=-1;return c["!links"].length>0&&(n[n.length]="<hyperlinks>",c["!links"].forEach(function(x){x[1].Target&&(d={ref:x[0]},x[1].Target.charAt(0)!="#"&&(p=Pe(a,-1,Le(x[1].Target).replace(/#.*$/,""),Ae.HLINK),d["r:id"]="rId"+p),(h=x[1].Target.indexOf("#"))>-1&&(d.location=Le(x[1].Target.slice(h+1))),x[1].Tooltip&&(d.tooltip=Le(x[1].Tooltip)),n[n.length]=ie("hyperlink",null,d))}),n[n.length]="</hyperlinks>"),delete c["!links"],c["!margins"]!=null&&(n[n.length]=Sm(c["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(n[n.length]=dr("ignoredErrors",ie("ignoredError",null,{numberStoredAsText:1,sqref:o}))),u.length>0&&(p=Pe(a,-1,"../drawings/drawing"+(e+1)+".xml",Ae.DRAW),n[n.length]=ie("drawing",null,{"r:id":"rId"+p}),c["!drawing"]=u),c["!comments"].length>0&&(p=Pe(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Ae.VML),n[n.length]=ie("legacyDrawing",null,{"r:id":"rId"+p}),c["!legacy"]=p),n.length>1&&(n[n.length]="</worksheet>",n[1]=n[1].replace("/>",">")),n.join("")}function Pm(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=a,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=n/20),r}function Lm(e,t,r){var a=z(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var i=320;n.hpx?i=ja(n.hpx)*20:n.hpt&&(i=n.hpt*20),a.write_shift(2,i),a.write_shift(1,0);var s=0;n.level&&(s|=n.level),n.hidden&&(s|=16),(n.hpx||n.hpt)&&(s|=32),a.write_shift(1,s),a.write_shift(1,0);var f=0,c=a.l;a.l+=4;for(var o={r:e,c:0},l=0;l<16;++l)if(!(t.s.c>l+1<<10||t.e.c<l<<10)){for(var u=-1,h=-1,d=l<<10;d<l+1<<10;++d){o.c=d;var p=Array.isArray(r)?(r[o.r]||[])[o.c]:r[_e(o)];p&&(u<0&&(u=d),h=d)}u<0||(++f,a.write_shift(4,u),a.write_shift(4,h))}var x=a.l;return a.l=c,a.write_shift(4,f),a.l=x,a.length>a.l?a.slice(0,a.l):a}function Bm(e,t,r,a){var n=Lm(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&Y(e,0,n)}var Mm=aa,Um=ka;function Wm(){}function Vm(e,t){var r={},a=e[e.l];return++e.l,r.above=!(a&64),r.left=!(a&128),e.l+=18,r.name=Xl(e),r}function Hm(e,t,r){r==null&&(r=z(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return y0({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),hf(e,r),r.slice(0,r.l)}function Gm(e){var t=Jr(e);return[t]}function Xm(e,t,r){return r==null&&(r=z(8)),ea(t,r)}function zm(e){var t=ra(e);return[t]}function $m(e,t,r){return r==null&&(r=z(4)),ta(t,r)}function Km(e){var t=Jr(e),r=e.read_shift(1);return[t,r,"b"]}function jm(e,t,r){return r==null&&(r=z(9)),ea(t,r),r.write_shift(1,e.v?1:0),r}function Ym(e){var t=ra(e),r=e.read_shift(1);return[t,r,"b"]}function Jm(e,t,r){return r==null&&(r=z(5)),ta(t,r),r.write_shift(1,e.v?1:0),r}function qm(e){var t=Jr(e),r=e.read_shift(1);return[t,r,"e"]}function Zm(e,t,r){return r==null&&(r=z(9)),ea(t,r),r.write_shift(1,e.v),r}function Qm(e){var t=ra(e),r=e.read_shift(1);return[t,r,"e"]}function ev(e,t,r){return r==null&&(r=z(8)),ta(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function rv(e){var t=Jr(e),r=e.read_shift(4);return[t,r,"s"]}function tv(e,t,r){return r==null&&(r=z(12)),ea(t,r),r.write_shift(4,t.v),r}function av(e){var t=ra(e),r=e.read_shift(4);return[t,r,"s"]}function nv(e,t,r){return r==null&&(r=z(8)),ta(t,r),r.write_shift(4,t.v),r}function iv(e){var t=Jr(e),r=kr(e);return[t,r,"n"]}function sv(e,t,r){return r==null&&(r=z(16)),ea(t,r),Yt(e.v,r),r}function vc(e){var t=ra(e),r=kr(e);return[t,r,"n"]}function fv(e,t,r){return r==null&&(r=z(12)),ta(t,r),Yt(e.v,r),r}function cv(e){var t=Jr(e),r=Bn(e);return[t,r,"n"]}function ov(e,t,r){return r==null&&(r=z(12)),ea(t,r),xf(e.v,r),r}function lv(e){var t=ra(e),r=Bn(e);return[t,r,"n"]}function uv(e,t,r){return r==null&&(r=z(8)),ta(t,r),xf(e.v,r),r}function hv(e){var t=Jr(e),r=Nn(e);return[t,r,"is"]}function xv(e){var t=Jr(e),r=Er(e);return[t,r,"str"]}function dv(e,t,r){return r==null&&(r=z(12+4*e.v.length)),ea(t,r),lr(e.v,r),r.length>r.l?r.slice(0,r.l):r}function pv(e){var t=ra(e),r=Er(e);return[t,r,"str"]}function mv(e,t,r){return r==null&&(r=z(8+4*e.v.length)),ta(t,r),lr(e.v,r),r.length>r.l?r.slice(0,r.l):r}function vv(e,t,r){var a=e.l+t,n=Jr(e);n.r=r["!row"];var i=e.read_shift(1),s=[n,i,"b"];if(r.cellFormula){e.l+=2;var f=U0(e,a-e.l,r);s[3]=_r(f,null,n,r.supbooks,r)}else e.l=a;return s}function gv(e,t,r){var a=e.l+t,n=Jr(e);n.r=r["!row"];var i=e.read_shift(1),s=[n,i,"e"];if(r.cellFormula){e.l+=2;var f=U0(e,a-e.l,r);s[3]=_r(f,null,n,r.supbooks,r)}else e.l=a;return s}function _v(e,t,r){var a=e.l+t,n=Jr(e);n.r=r["!row"];var i=kr(e),s=[n,i,"n"];if(r.cellFormula){e.l+=2;var f=U0(e,a-e.l,r);s[3]=_r(f,null,n,r.supbooks,r)}else e.l=a;return s}function wv(e,t,r){var a=e.l+t,n=Jr(e);n.r=r["!row"];var i=Er(e),s=[n,i,"str"];if(r.cellFormula){e.l+=2;var f=U0(e,a-e.l,r);s[3]=_r(f,null,n,r.supbooks,r)}else e.l=a;return s}var kv=aa,Ev=ka;function Tv(e,t){return t==null&&(t=z(4)),t.write_shift(4,e),t}function Sv(e,t){var r=e.l+t,a=aa(e),n=Pn(e),i=Er(e),s=Er(e),f=Er(e);e.l=r;var c={rfx:a,relId:n,loc:i,display:f};return s&&(c.Tooltip=s),c}function Fv(e,t){var r=z(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));ka({s:Ke(e[0]),e:Ke(e[0])},r),Ln("rId"+t,r);var a=e[1].Target.indexOf("#"),n=a==-1?"":e[1].Target.slice(a+1);return lr(n||"",r),lr(e[1].Tooltip||"",r),lr("",r),r.slice(0,r.l)}function yv(){}function Av(e,t,r){var a=e.l+t,n=df(e),i=e.read_shift(1),s=[n];if(s[2]=i,r.cellFormula){var f=qp(e,a-e.l,r);s[1]=f}else e.l=a;return s}function Cv(e,t,r){var a=e.l+t,n=aa(e),i=[n];if(r.cellFormula){var s=Qp(e,a-e.l,r);i[1]=s,e.l=a}else e.l=a;return i}function Dv(e,t,r){r==null&&(r=z(18));var a=W0(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(a.width||10)*256),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),typeof a.width=="number"&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}var gc=["left","right","top","bottom","header","footer"];function Iv(e){var t={};return gc.forEach(function(r){t[r]=kr(e)}),t}function Ov(e,t){return t==null&&(t=z(6*8)),$t(e),gc.forEach(function(r){Yt(e[r],t)}),t}function bv(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function Rv(e,t,r){r==null&&(r=z(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function Nv(e){var t=z(24);return t.write_shift(4,4),t.write_shift(4,1),ka(e,t),t}function Pv(e,t){return t==null&&(t=z(16*4+2)),t.write_shift(2,e.password?Gn(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function Lv(){}function Bv(){}function Mv(e,t,r,a,n,i,s){if(!e)return e;var f=t||{};a||(a={"!id":{}});var c=f.dense?[]:{},o,l={s:{r:2e6,c:2e6},e:{r:0,c:0}},u=!1,h=!1,d,p,x,m,_,T,g,I,L,D=[];f.biff=12,f["!row"]=0;var k=0,R=!1,O=[],X={},M=f.supbooks||n.supbooks||[[]];if(M.sharedf=X,M.arrayf=O,M.SheetNames=n.SheetNames||n.Sheets.map(function(Ie){return Ie.name}),!f.supbooks&&(f.supbooks=M,n.Names))for(var P=0;P<n.Names.length;++P)M[0][P+1]=n.Names[P];var J=[],le=[],Z=!1;Ya[16]={n:"BrtShortReal",f:vc};var ue;if(kt(e,function(V,pe,ve){if(!h)switch(ve){case 148:o=V;break;case 0:d=V,f.sheetRows&&f.sheetRows<=d.r&&(h=!0),I=er(m=d.r),f["!row"]=d.r,(V.hidden||V.hpt||V.level!=null)&&(V.hpt&&(V.hpx=va(V.hpt)),le[V.r]=V);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(p={t:V[2]},V[2]){case"n":p.v=V[1];break;case"s":g=Pa[V[1]],p.v=g.t,p.r=g.r;break;case"b":p.v=!!V[1];break;case"e":p.v=V[1],f.cellText!==!1&&(p.w=Et[p.v]);break;case"str":p.t="s",p.v=V[1];break;case"is":p.t="s",p.v=V[1].t;break}if((x=s.CellXf[V[0].iStyleRef])&&dc(p,x.numFmtId,null,f,i,s),_=V[0].c==-1?_+1:V[0].c,f.dense?(c[m]||(c[m]=[]),c[m][_]=p):c[$e(_)+I]=p,f.cellFormula){for(R=!1,k=0;k<O.length;++k){var C=O[k];d.r>=C[0].s.r&&d.r<=C[0].e.r&&_>=C[0].s.c&&_<=C[0].e.c&&(p.F=Ee(C[0]),R=!0)}!R&&V.length>3&&(p.f=V[3])}if(l.s.r>d.r&&(l.s.r=d.r),l.s.c>_&&(l.s.c=_),l.e.r<d.r&&(l.e.r=d.r),l.e.c<_&&(l.e.c=_),f.cellDates&&x&&p.t=="n"&&ga(ge[x.numFmtId])){var B=Ht(p.v);B&&(p.t="d",p.v=new Date(B.y,B.m-1,B.d,B.H,B.M,B.S,B.u))}ue&&(ue.type=="XLDAPR"&&(p.D=!0),ue=void 0);break;case 1:case 12:if(!f.sheetStubs||u)break;p={t:"z",v:void 0},_=V[0].c==-1?_+1:V[0].c,f.dense?(c[m]||(c[m]=[]),c[m][_]=p):c[$e(_)+I]=p,l.s.r>d.r&&(l.s.r=d.r),l.s.c>_&&(l.s.c=_),l.e.r<d.r&&(l.e.r=d.r),l.e.c<_&&(l.e.c=_),ue&&(ue.type=="XLDAPR"&&(p.D=!0),ue=void 0);break;case 176:D.push(V);break;case 49:ue=((f.xlmeta||{}).Cell||[])[V-1];break;case 494:var b=a["!id"][V.relId];for(b?(V.Target=b.Target,V.loc&&(V.Target+="#"+V.loc),V.Rel=b):V.relId==""&&(V.Target="#"+V.loc),m=V.rfx.s.r;m<=V.rfx.e.r;++m)for(_=V.rfx.s.c;_<=V.rfx.e.c;++_)f.dense?(c[m]||(c[m]=[]),c[m][_]||(c[m][_]={t:"z",v:void 0}),c[m][_].l=V):(T=_e({c:_,r:m}),c[T]||(c[T]={t:"z",v:void 0}),c[T].l=V);break;case 426:if(!f.cellFormula)break;O.push(V),L=f.dense?c[m][_]:c[$e(_)+I],L.f=_r(V[1],l,{r:d.r,c:_},M,f),L.F=Ee(V[0]);break;case 427:if(!f.cellFormula)break;X[_e(V[0].s)]=V[1],L=f.dense?c[m][_]:c[$e(_)+I],L.f=_r(V[1],l,{r:d.r,c:_},M,f);break;case 60:if(!f.cellStyles)break;for(;V.e>=V.s;)J[V.e--]={width:V.w/256,hidden:!!(V.flags&1),level:V.level},Z||(Z=!0,Xn(V.w/256)),Ot(J[V.e+1]);break;case 161:c["!autofilter"]={ref:Ee(V)};break;case 476:c["!margins"]=V;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),V.name&&(n.Sheets[r].CodeName=V.name),(V.above||V.left)&&(c["!outline"]={above:V.above,left:V.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),V.RTL&&(n.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:u=!0;break;case 36:u=!1;break;case 37:u=!0;break;case 38:u=!1;break;default:if(!pe.T){if(!u||f.WTF)throw new Error("Unexpected record 0x"+ve.toString(16))}}},f),delete f.supbooks,delete f["!row"],!c["!ref"]&&(l.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0))&&(c["!ref"]=Ee(o||l)),f.sheetRows&&c["!ref"]){var ce=be(c["!ref"]);f.sheetRows<=+ce.e.r&&(ce.e.r=f.sheetRows-1,ce.e.r>l.e.r&&(ce.e.r=l.e.r),ce.e.r<ce.s.r&&(ce.s.r=ce.e.r),ce.e.c>l.e.c&&(ce.e.c=l.e.c),ce.e.c<ce.s.c&&(ce.s.c=ce.e.c),c["!fullref"]=c["!ref"],c["!ref"]=Ee(ce))}return D.length>0&&(c["!merges"]=D),J.length>0&&(c["!cols"]=J),le.length>0&&(c["!rows"]=le),c}function Uv(e,t,r,a,n,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=He(t),t.z=t.z||ge[14],t.v=fr(Ve(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var c={r,c:a};switch(c.s=Lt(n.cellXfs,t,n),t.l&&i["!links"].push([_e(c),t.l]),t.c&&i["!comments"].push([_e(c),t.c]),t.t){case"s":case"str":return n.bookSST?(f=Yn(n.Strings,t.v,n.revStrings),c.t="s",c.v=f,s?Y(e,18,nv(t,c)):Y(e,7,tv(t,c))):(c.t="str",s?Y(e,17,mv(t,c)):Y(e,6,dv(t,c))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?Y(e,13,uv(t,c)):Y(e,2,ov(t,c)):s?Y(e,16,fv(t,c)):Y(e,5,sv(t,c)),!0;case"b":return c.t="b",s?Y(e,15,Jm(t,c)):Y(e,4,jm(t,c)),!0;case"e":return c.t="e",s?Y(e,14,ev(t,c)):Y(e,3,Zm(t,c)),!0}return s?Y(e,12,$m(t,c)):Y(e,1,Xm(t,c)),!0}function Wv(e,t,r,a){var n=be(t["!ref"]||"A1"),i,s="",f=[];Y(e,145);var c=Array.isArray(t),o=n.e.r;t["!rows"]&&(o=Math.max(n.e.r,t["!rows"].length-1));for(var l=n.s.r;l<=o;++l){s=er(l),Bm(e,t,n,l);var u=!1;if(l<=n.e.r)for(var h=n.s.c;h<=n.e.c;++h){l===n.s.r&&(f[h]=$e(h)),i=f[h]+s;var d=c?(t[l]||[])[h]:t[i];if(!d){u=!1;continue}u=Uv(e,d,l,h,a,t,u)}}Y(e,146)}function Vv(e,t){!t||!t["!merges"]||(Y(e,177,Tv(t["!merges"].length)),t["!merges"].forEach(function(r){Y(e,176,Ev(r))}),Y(e,178))}function Hv(e,t){!t||!t["!cols"]||(Y(e,390),t["!cols"].forEach(function(r,a){r&&Y(e,60,Dv(a,r))}),Y(e,391))}function Gv(e,t){!t||!t["!ref"]||(Y(e,648),Y(e,649,Nv(be(t["!ref"]))),Y(e,650))}function Xv(e,t,r){t["!links"].forEach(function(a){if(a[1].Target){var n=Pe(r,-1,a[1].Target.replace(/#.*$/,""),Ae.HLINK);Y(e,494,Fv(a,n))}}),delete t["!links"]}function zv(e,t,r,a){if(t["!comments"].length>0){var n=Pe(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",Ae.VML);Y(e,551,Ln("rId"+n)),t["!legacy"]=n}}function $v(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],i=typeof n.ref=="string"?n.ref:Ee(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=Rr(i);f.s.r==f.e.r&&(f.e.r=Rr(t["!ref"]).e.r,i=Ee(f));for(var c=0;c<s.length;++c){var o=s[c];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==a){o.Ref="'"+r.SheetNames[a]+"'!"+i;break}}c==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+i}),Y(e,161,ka(be(i))),Y(e,162)}}function Kv(e,t,r){Y(e,133),Y(e,137,Rv(t,r)),Y(e,138),Y(e,134)}function jv(e,t){t["!protect"]&&Y(e,535,Pv(t["!protect"]))}function Yv(e,t,r,a){var n=br(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var c=be(s["!ref"]||"A1");if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],Y(n,129),(r.vbaraw||s["!outline"])&&Y(n,147,Hm(f,s["!outline"])),Y(n,148,Um(c)),Kv(n,s,r.Workbook),Hv(n,s),Wv(n,s,e,t),jv(n,s),$v(n,s,r,e),Vv(n,s),Xv(n,s,a),s["!margins"]&&Y(n,476,Ov(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&Gv(n,s),zv(n,s,e,a),Y(n,130),n.end()}function Jv(e){var t=[],r=e.match(/^<c:numCache>/),a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(i){var s=i.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);s&&(t[+s[1]]=r?+s[2]:s[2])});var n=Oe((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(i){a=i.replace(/<.*?>/g,"")}),[t,n,a]}function qv(e,t,r,a,n,i){var s=i||{"!type":"chart"};if(!e)return i;var f=0,c=0,o="A",l={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(u){var h=Jv(u);l.s.r=l.s.c=0,l.e.c=f,o=$e(f),h[0].forEach(function(d,p){s[o+er(p)]={t:"n",v:d,z:h[1]},c=p}),l.e.r<c&&(l.e.r=c),++f}),f>0&&(s["!ref"]=Ee(l)),s}function Zv(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var i={"!type":"chart","!drawel":null,"!rel":""},s,f=e.match(pc);return f&&Jn(f[0],i,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}function Qv(e,t){e.l+=10;var r=Er(e);return{name:r}}function e2(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var i={"!type":"chart","!drawel":null,"!rel":""},s=!1;return kt(e,function(c,o,l){switch(l){case 550:i["!rel"]=c;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),c.name&&(n.Sheets[r].CodeName=c.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!(o.T>0)){if(!(o.T<0)){if(!s||t.WTF)throw new Error("Unexpected record 0x"+l.toString(16))}}}},t),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}var qn=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],r2=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],t2=[],a2=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function fs(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var i=t[n];if(a[i[0]]==null)a[i[0]]=i[1];else switch(i[2]){case"bool":typeof a[i[0]]=="string"&&(a[i[0]]=We(a[i[0]]));break;case"int":typeof a[i[0]]=="string"&&(a[i[0]]=parseInt(a[i[0]],10));break}}}function cs(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":typeof e[a[0]]=="string"&&(e[a[0]]=We(e[a[0]]));break;case"int":typeof e[a[0]]=="string"&&(e[a[0]]=parseInt(e[a[0]],10));break}}}function _c(e){cs(e.WBProps,qn),cs(e.CalcPr,a2),fs(e.WBView,r2),fs(e.Sheets,t2),da.date1904=We(e.WBProps.date1904)}function n2(e){return!e.Workbook||!e.Workbook.WBProps?"false":We(e.Workbook.WBProps.date1904)?"true":"false"}var i2="][*?/\\".split("");function wc(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return i2.forEach(function(a){if(e.indexOf(a)!=-1){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function s2(e,t,r){e.forEach(function(a,n){wc(a);for(var i=0;i<n;++i)if(a==e[i])throw new Error("Duplicate Sheet Name: "+a);if(r){var s=t&&t[n]&&t[n].CodeName||a;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function f2(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];s2(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)nm(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}var c2=/<\w+:workbook/;function o2(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",i={},s=0;if(e.replace(Sr,function(c,o){var l=we(c);switch(ot(l[0])){case"<?xml":break;case"<workbook":c.match(c2)&&(n="xmlns"+c.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"</workbook>":break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":qn.forEach(function(u){if(l[u[0]]!=null)switch(u[2]){case"bool":r.WBProps[u[0]]=We(l[u[0]]);break;case"int":r.WBProps[u[0]]=parseInt(l[u[0]],10);break;default:r.WBProps[u[0]]=l[u[0]]}}),l.codeName&&(r.WBProps.CodeName=Be(l.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=Oe(Be(l.name)),delete l[0],r.Sheets.push(l);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":a=!0;break;case"</definedNames>":a=!1;break;case"<definedName":i={},i.Name=Be(l.name),l.comment&&(i.Comment=l.comment),l.localSheetId&&(i.Sheet=+l.localSheetId),We(l.hidden||"0")&&(i.Hidden=!0),s=o+c.length;break;case"</definedName>":i.Ref=Oe(Be(e.slice(s,o))),r.Names.push(i);break;case"<definedName/>":break;case"<calcPr":delete l[0],r.CalcPr=l;break;case"<calcPr/>":delete l[0],r.CalcPr=l;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</AlternateContent>":a=!1;break;case"<revisionPtr":break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return c}),Qt.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return _c(r),r}function kc(e){var t=[rr];t[t.length]=ie("workbook",null,{xmlns:Qt[0],"xmlns:r":sr.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(qn.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(a[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=ie("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],i=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!n[i]||!n[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:Le(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),n[i])switch(n[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=ie("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var c={name:f.Name};f.Comment&&(c.comment=f.Comment),f.Sheet!=null&&(c.localSheetId=""+f.Sheet),f.Hidden&&(c.hidden="1"),f.Ref&&(t[t.length]=ie("definedName",Le(f.Ref),c))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function l2(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=nn(e),r.name=Er(e),r}function u2(e,t){return t||(t=z(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),Ln(e.strRelID,t),lr(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function h2(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?Er(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(a&65536),r.backupFile=!!(a&64),r.checkCompatibility=!!(a&4096),r.date1904=!!(a&1),r.filterPrivacy=!!(a&8),r.hidePivotFieldList=!!(a&1024),r.promptedSolutions=!!(a&16),r.publishItems=!!(a&2048),r.refreshAllConnections=!!(a&262144),r.saveExternalLinkValues=!!(a&128),r.showBorderUnselectedTables=!!(a&4),r.showInkAnnotation=!!(a&32),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(a&32768),r.updateLinks=["userSet","never","always"][a>>8&3],r}function x2(e,t){t||(t=z(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),hf(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function d2(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function p2(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),i=zl(e),s=Zp(e,0,r),f=Pn(e);e.l=a;var c={Name:i,Ptg:s};return n<268435455&&(c.Sheet=n),f&&(c.Comment=f),c}function m2(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var i=[],s=[[]];return s.SheetNames=[],s.XTI=[],Ya[16]={n:"BrtFRTArchID$",f:d2},kt(e,function(c,o,l){switch(l){case 156:s.SheetNames.push(c.name),r.Sheets.push(c);break;case 153:r.WBProps=c;break;case 39:c.Sheet!=null&&(t.SID=c.Sheet),c.Ref=_r(c.Ptg,null,null,s,t),delete t.SID,delete c.Ptg,i.push(c);break;case 1036:break;case 357:case 358:case 355:case 667:s[0].length?s.push([l,c]):s[0]=[l,c],s[s.length-1].XTI=[];break;case 362:s.length===0&&(s[0]=[],s[0].XTI=[]),s[s.length-1].XTI=s[s.length-1].XTI.concat(c),s.XTI=s.XTI.concat(c);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:a.push(l),n=!0;break;case 36:a.pop(),n=!1;break;case 37:a.push(l),n=!0;break;case 38:a.pop(),n=!1;break;case 16:break;default:if(!o.T){if(!n||t.WTF&&a[a.length-1]!=37&&a[a.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}},t),_c(r),r.Names=i,r.supbooks=s,r}function v2(e,t){Y(e,143);for(var r=0;r!=t.SheetNames.length;++r){var a=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,n={Hidden:a,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};Y(e,156,u2(n))}Y(e,144)}function g2(e,t){t||(t=z(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return lr("SheetJS",t),lr(_0.version,t),lr(_0.version,t),lr("7262",t),t.length>t.l?t.slice(0,t.l):t}function _2(e,t){t||(t=z(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function w2(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,a=0,n=-1,i=-1;a<r.length;++a)!r[a]||!r[a].Hidden&&n==-1?n=a:r[a].Hidden==1&&i==-1&&(i=a);i>n||(Y(e,135),Y(e,158,_2(n)),Y(e,136))}}function k2(e,t){var r=br();return Y(r,131),Y(r,128,g2()),Y(r,153,x2(e.Workbook&&e.Workbook.WBProps||null)),w2(r,e),v2(r,e),Y(r,132),r.end()}function E2(e,t,r){return t.slice(-4)===".bin"?m2(e,r):o2(e,r)}function T2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?Mv(e,a,r,n,i,s,f):pm(e,a,r,n,i,s,f)}function S2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?e2(e,a,r,n,i):Zv(e,a,r,n,i)}function F2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?Ad():Cd()}function y2(e,t,r,a,n,i,s,f){return t.slice(-4)===".bin"?Fd():yd()}function A2(e,t,r,a){return t.slice(-4)===".bin"?Ex(e,r,a):lx(e,r,a)}function C2(e,t,r){return rc(e,r)}function D2(e,t,r){return t.slice(-4)===".bin"?D1(e,r):y1(e,r)}function I2(e,t,r){return t.slice(-4)===".bin"?wd(e,r):ud(e,r)}function O2(e,t,r){return t.slice(-4)===".bin"?cd(e):sd(e)}function b2(e,t,r,a){return r.slice(-4)===".bin"?od(e,t,r,a):void 0}function R2(e,t,r){return t.slice(-4)===".bin"?ad(e,t,r):id(e,t,r)}function N2(e,t,r){return(t.slice(-4)===".bin"?k2:kc)(e)}function P2(e,t,r,a,n){return(t.slice(-4)===".bin"?Yv:mc)(e,r,a,n)}function L2(e,t,r){return(t.slice(-4)===".bin"?bx:Qf)(e,r)}function B2(e,t,r){return(t.slice(-4)===".bin"?b1:zf)(e,r)}function M2(e,t,r){return(t.slice(-4)===".bin"?kd:nc)(e)}function U2(e){return(e.slice(-4)===".bin"?nd:tc)()}var Ec=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,Tc=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function qr(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),r.length===1)return a;var n=e.match(Ec),i,s,f,c;if(n)for(c=0;c!=n.length;++c)i=n[c].match(Tc),(s=i[1].indexOf(":"))===-1?a[i[1]]=i[2].slice(1,i[2].length-1):(i[1].slice(0,6)==="xmlns:"?f="xmlns"+i[1].slice(6):f=i[1].slice(s+1),a[f]=i[2].slice(1,i[2].length-1));return a}function W2(e){var t=e.split(/\s+/),r={};if(t.length===1)return r;var a=e.match(Ec),n,i,s,f;if(a)for(f=0;f!=a.length;++f)n=a[f].match(Tc),(i=n[1].indexOf(":"))===-1?r[n[1]]=n[2].slice(1,n[2].length-1):(n[1].slice(0,6)==="xmlns:"?s="xmlns"+n[1].slice(6):s=n[1].slice(i+1),r[s]=n[2].slice(1,n[2].length-1));return r}var Ba;function V2(e,t){var r=Ba[e]||Oe(e);return r==="General"?Kt(t):Yr(r,t)}function H2(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=We(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=Ve(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Oe(t)]=n}function G2(e,t,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||Et[e.v]:t==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Wa(e.v):e.w=Kt(e.v):e.w=V2(t||"General",e.v)}catch(i){if(r.WTF)throw i}try{var a=Ba[t]||t||"General";if(r.cellNF&&(e.z=a),r.cellDates&&e.t=="n"&&ga(a)){var n=Ht(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(i){if(r.WTF)throw i}}}function X2(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=tx[a.Pattern]||a.Pattern)}e[t.ID]=t}function z2(e,t,r,a,n,i,s,f,c,o){var l="General",u=a.StyleID,h={};o=o||{};var d=[],p=0;for(u===void 0&&f&&(u=f.StyleID),u===void 0&&s&&(u=s.StyleID);i[u]!==void 0&&(i[u].nf&&(l=i[u].nf),i[u].Interior&&d.push(i[u].Interior),!!i[u].Parent);)u=i[u].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=We(e);break;case"String":a.t="s",a.r=Fi(Oe(e)),a.v=e.indexOf("<")>-1?Oe(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),a.v=(Ve(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3),a.v!==a.v?a.v=Oe(e):a.v<60&&(a.v=a.v-1),(!l||l=="General")&&(l="yyyy-mm-dd");case"Number":a.v===void 0&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=vf[e],o.cellText!==!1&&(a.w=e);break;default:e==""&&t==""?a.t="z":(a.t="s",a.v=Fi(t||e));break}if(G2(a,l,o),o.cellFormula!==!1)if(a.Formula){var x=Oe(a.Formula);x.charCodeAt(0)==61&&(x=x.slice(1)),a.f=xa(x,n),delete a.Formula,a.ArrayRange=="RC"?a.F=xa("RC:RC",n):a.ArrayRange&&(a.F=xa(a.ArrayRange,n),c.push([be(a.F),a.F]))}else for(p=0;p<c.length;++p)n.r>=c[p][0].s.r&&n.r<=c[p][0].e.r&&n.c>=c[p][0].s.c&&n.c<=c[p][0].e.c&&(a.F=c[p][1]);o.cellStyles&&(d.forEach(function(m){!h.patternType&&m.patternType&&(h.patternType=m.patternType)}),a.s=h),a.StyleID!==void 0&&(a.ixfe=a.StyleID)}function $2(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function Z0(e,t){var r=t||{};_a();var a=Ca(Dn(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(a=Be(a));var n=a.slice(0,1024).toLowerCase(),i=!1;if(n=n.replace(/".*?"/g,""),(n.indexOf(">")&1023)>Math.min(n.indexOf(",")&1023,n.indexOf(";")&1023)){var s=He(r);return s.type="string",ma.to_workbook(a,s)}if(n.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(qe){n.indexOf("<"+qe)>=0&&(i=!0)}),i)return Og(a,r);Ba={"General Number":"General","General Date":ge[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":ge[15],"Short Date":ge[14],"Long Time":ge[19],"Medium Time":ge[18],"Short Time":ge[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:ge[2],Standard:ge[4],Percent:ge[10],Scientific:ge[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var f,c=[],o,l={},u=[],h=r.dense?[]:{},d="",p={},x={},m=qr('<Data ss:Type="String">'),_=0,T=0,g=0,I={s:{r:2e6,c:2e6},e:{r:0,c:0}},L={},D={},k="",R=0,O=[],X={},M={},P=0,J=[],le=[],Z={},ue=[],ce,Ie=!1,V=[],pe=[],ve={},C=0,B=0,b={Sheets:[],WBProps:{date1904:!1}},N={};Ga.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"");for(var j="";f=Ga.exec(a);)switch(f[3]=(j=f[3]).toLowerCase()){case"data":if(j=="data"){if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break}if(c[c.length-1][1])break;f[1]==="/"?z2(a.slice(_,f.index),k,m,c[c.length-1][0]=="comment"?Z:p,{c:T,r:g},L,ue[T],x,V,r):(k="",m=qr(f[0]),_=f.index+f[0].length);break;case"cell":if(f[1]==="/")if(le.length>0&&(p.c=le),(!r.sheetRows||r.sheetRows>g)&&p.v!==void 0&&(r.dense?(h[g]||(h[g]=[]),h[g][T]=p):h[$e(T)+er(g)]=p),p.HRef&&(p.l={Target:Oe(p.HRef)},p.HRefScreenTip&&(p.l.Tooltip=p.HRefScreenTip),delete p.HRef,delete p.HRefScreenTip),(p.MergeAcross||p.MergeDown)&&(C=T+(parseInt(p.MergeAcross,10)|0),B=g+(parseInt(p.MergeDown,10)|0),O.push({s:{c:T,r:g},e:{c:C,r:B}})),!r.sheetStubs)p.MergeAcross?T=C+1:++T;else if(p.MergeAcross||p.MergeDown){for(var ae=T;ae<=C;++ae)for(var ne=g;ne<=B;++ne)(ae>T||ne>g)&&(r.dense?(h[ne]||(h[ne]=[]),h[ne][ae]={t:"z"}):h[$e(ae)+er(ne)]={t:"z"});T=C+1}else++T;else p=W2(f[0]),p.Index&&(T=+p.Index-1),T<I.s.c&&(I.s.c=T),T>I.e.c&&(I.e.c=T),f[0].slice(-2)==="/>"&&++T,le=[];break;case"row":f[1]==="/"||f[0].slice(-2)==="/>"?(g<I.s.r&&(I.s.r=g),g>I.e.r&&(I.e.r=g),f[0].slice(-2)==="/>"&&(x=qr(f[0]),x.Index&&(g=+x.Index-1)),T=0,++g):(x=qr(f[0]),x.Index&&(g=+x.Index-1),ve={},(x.AutoFitHeight=="0"||x.Height)&&(ve.hpx=parseInt(x.Height,10),ve.hpt=ja(ve.hpx),pe[g]=ve),x.Hidden=="1"&&(ve.hidden=!0,pe[g]=ve));break;case"worksheet":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));u.push(d),I.s.r<=I.e.r&&I.s.c<=I.e.c&&(h["!ref"]=Ee(I),r.sheetRows&&r.sheetRows<=I.e.r&&(h["!fullref"]=h["!ref"],I.e.r=r.sheetRows-1,h["!ref"]=Ee(I))),O.length&&(h["!merges"]=O),ue.length>0&&(h["!cols"]=ue),pe.length>0&&(h["!rows"]=pe),l[d]=h}else I={s:{r:2e6,c:2e6},e:{r:0,c:0}},g=T=0,c.push([f[3],!1]),o=qr(f[0]),d=Oe(o.Name),h=r.dense?[]:{},O=[],V=[],pe=[],N={name:d,Hidden:0},b.Sheets.push(N);break;case"table":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else{if(f[0].slice(-2)=="/>")break;c.push([f[3],!1]),ue=[],Ie=!1}break;case"style":f[1]==="/"?X2(L,D,r):D=qr(f[0]);break;case"numberformat":D.nf=Oe(qr(f[0]).Format||"General"),Ba[D.nf]&&(D.nf=Ba[D.nf]);for(var re=0;re!=392&&ge[re]!=D.nf;++re);if(re==392){for(re=57;re!=392;++re)if(ge[re]==null){vt(D.nf,re);break}}break;case"column":if(c[c.length-1][0]!=="table")break;if(ce=qr(f[0]),ce.Hidden&&(ce.hidden=!0,delete ce.Hidden),ce.Width&&(ce.wpx=parseInt(ce.Width,10)),!Ie&&ce.wpx>10){Ie=!0,wr=qf;for(var Q=0;Q<ue.length;++Q)ue[Q]&&Ot(ue[Q])}Ie&&Ot(ce),ue[ce.Index-1||ue.length]=ce;for(var Se=0;Se<+ce.Span;++Se)ue[ue.length]=He(ce);break;case"namedrange":if(f[1]==="/")break;b.Names||(b.Names=[]);var A=we(f[0]),Ue={Name:A.Name,Ref:xa(A.RefersTo.slice(1),{r:0,c:0})};b.Sheets.length>0&&(Ue.Sheet=b.Sheets.length-1),b.Names.push(Ue);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(f[0].slice(-2)==="/>")break;f[1]==="/"?k+=a.slice(R,f.index):R=f.index+f[0].length;break;case"interior":if(!r.cellStyles)break;D.Interior=qr(f[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(f[0].slice(-2)==="/>")break;f[1]==="/"?mu(X,j,a.slice(P,f.index)):P=f.index+f[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else c.push([f[3],!1]);break;case"comment":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"));$2(Z),le.push(Z)}else c.push([f[3],!1]),o=qr(f[0]),Z={a:o.Author};break;case"autofilter":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else if(f[0].charAt(f[0].length-2)!=="/"){var De=qr(f[0]);h["!autofilter"]={ref:xa(De.Range).replace(/\$/g,"")},c.push([f[3],!0])}break;case"name":break;case"datavalidation":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(f[1]==="/"){if((o=c.pop())[0]!==f[3])throw new Error("Bad state: "+o.join("|"))}else f[0].charAt(f[0].length-2)!=="/"&&c.push([f[3],!0]);break;case"null":break;default:if(c.length==0&&f[3]=="document"||c.length==0&&f[3]=="uof")return xs(a,r);var Me=!0;switch(c[c.length-1][0]){case"officedocumentsettings":switch(f[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:Me=!1}break;case"componentoptions":switch(f[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:Me=!1}break;case"excelworkbook":switch(f[3]){case"date1904":b.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:Me=!1}break;case"workbookoptions":switch(f[3]){case"owcversion":break;case"height":break;case"width":break;default:Me=!1}break;case"worksheetoptions":switch(f[3]){case"visible":if(f[0].slice(-2)!=="/>")if(f[1]==="/")switch(a.slice(P,f.index)){case"SheetHidden":N.Hidden=1;break;case"SheetVeryHidden":N.Hidden=2;break}else P=f.index+f[0].length;break;case"header":h["!margins"]||$t(h["!margins"]={},"xlml"),isNaN(+we(f[0]).Margin)||(h["!margins"].header=+we(f[0]).Margin);break;case"footer":h["!margins"]||$t(h["!margins"]={},"xlml"),isNaN(+we(f[0]).Margin)||(h["!margins"].footer=+we(f[0]).Margin);break;case"pagemargins":var Ce=we(f[0]);h["!margins"]||$t(h["!margins"]={},"xlml"),isNaN(+Ce.Top)||(h["!margins"].top=+Ce.Top),isNaN(+Ce.Left)||(h["!margins"].left=+Ce.Left),isNaN(+Ce.Right)||(h["!margins"].right=+Ce.Right),isNaN(+Ce.Bottom)||(h["!margins"].bottom=+Ce.Bottom);break;case"displayrighttoleft":b.Views||(b.Views=[]),b.Views[0]||(b.Views[0]={}),b.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":h["!outline"]||(h["!outline"]={}),h["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":h["!outline"]||(h["!outline"]={}),h["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:Me=!1}break;case"pivottable":case"pivotcache":switch(f[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:Me=!1}break;case"pagebreaks":switch(f[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:Me=!1}break;case"autofilter":switch(f[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:Me=!1}break;case"querytable":switch(f[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:Me=!1}break;case"datavalidation":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:Me=!1}break;case"sorting":case"conditionalformatting":switch(f[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:Me=!1}break;case"mapinfo":case"schema":case"data":switch(f[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:Me=!1}break;case"smarttags":break;default:Me=!1;break}if(Me||f[3].match(/!\[CDATA/))break;if(!c[c.length-1][1])throw"Unrecognized tag: "+f[3]+"|"+c.join("|");if(c[c.length-1][0]==="customdocumentproperties"){if(f[0].slice(-2)==="/>")break;f[1]==="/"?H2(M,j,J,a.slice(P,f.index)):(J=f,P=f.index+f[0].length);break}if(r.WTF)throw"Unrecognized tag: "+f[3]+"|"+c.join("|")}var oe={};return!r.bookSheets&&!r.bookProps&&(oe.Sheets=l),oe.SheetNames=u,oe.Workbook=b,oe.SSF=He(ge),oe.Props=X,oe.Custprops=M,oe}function hn(e,t){switch(Qn(t=t||{}),t.type||"base64"){case"base64":return Z0(Ur(e),t);case"binary":case"buffer":case"file":return Z0(e,t);case"array":return Z0(Nt(e),t)}}function K2(e,t){var r=[];return e.Props&&r.push(vu(e.Props,t)),e.Custprops&&r.push(gu(e.Props,e.Custprops)),r.join("")}function j2(){return""}function Y2(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(a,n){var i=[];i.push(ie("NumberFormat",null,{"ss:Format":Le(ge[a.numFmtId])}));var s={"ss:ID":"s"+(21+n)};r.push(ie("Style",i.join(""),s))}),ie("Styles",r.join(""))}function Sc(e){return ie("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+Kn(e.Ref,{r:0,c:0})})}function J2(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];n.Sheet==null&&(n.Name.match(/^_xlfn\./)||r.push(Sc(n)))}return ie("Names",r.join(""))}function q2(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,i=[],s=0;s<n.length;++s){var f=n[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(Sc(f)))}return i.join("")}function Z2(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(ie("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(ie("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(ie("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(ie("Visible",a.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(a.Workbook.Sheets[i]&&!a.Workbook.Sheets[i].Hidden);++i);i==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(dr("ProtectContents","True")),e["!protect"].objects&&n.push(dr("ProtectObjects","True")),e["!protect"].scenarios&&n.push(dr("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?n.push(dr("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&n.push(dr("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&n.push("<"+s[1]+"/>")})),n.length==0?"":ie("WorksheetOptions",n.join(""),{xmlns:Pr.x})}function Q2(e){return e.map(function(t){var r=wl(t.t||""),a=ie("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return ie("Comment",a,{"ss:Author":t.a})}).join("")}function eg(e,t,r,a,n,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+Le(Kn(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var c=Ke(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(c.r==s.r?"":"["+(c.r-s.r)+"]")+"C"+(c.c==s.c?"":"["+(c.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=Le(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=Le(e.l.Tooltip))),r["!merges"])for(var o=r["!merges"],l=0;l!=o.length;++l)o[l].s.c!=s.c||o[l].s.r!=s.r||(o[l].e.c>o[l].s.c&&(f["ss:MergeAcross"]=o[l].e.c-o[l].s.c),o[l].e.r>o[l].s.r&&(f["ss:MergeDown"]=o[l].e.r-o[l].s.r));var u="",h="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":u="Number",h=String(e.v);break;case"b":u="Boolean",h=e.v?"1":"0";break;case"e":u="Error",h=Et[e.v];break;case"d":u="DateTime",h=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||ge[14]);break;case"s":u="String",h=_l(e.v||"");break}var d=Lt(a.cellXfs,e,a);f["ss:StyleID"]="s"+(21+d),f["ss:Index"]=s.c+1;var p=e.v!=null?h:"",x=e.t=="z"?"":'<Data ss:Type="'+u+'">'+p+"</Data>";return(e.c||[]).length>0&&(x+=Q2(e.c)),ie("Cell",x,f)}function rg(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=va(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function tg(e,t,r,a){if(!e["!ref"])return"";var n=be(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(m,_){Ot(m);var T=!!m.width,g=W0(_,m),I={"ss:Index":_+1};T&&(I["ss:Width"]=$a(g.width)),m.hidden&&(I["ss:Hidden"]="1"),f.push(ie("Column",null,I))});for(var c=Array.isArray(e),o=n.s.r;o<=n.e.r;++o){for(var l=[rg(o,(e["!rows"]||[])[o])],u=n.s.c;u<=n.e.c;++u){var h=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>u)&&!(i[s].s.r>o)&&!(i[s].e.c<u)&&!(i[s].e.r<o)){(i[s].s.c!=u||i[s].s.r!=o)&&(h=!0);break}if(!h){var d={r:o,c:u},p=_e(d),x=c?(e[o]||[])[u]:e[p];l.push(eg(x,p,e,t,r,a,d))}}l.push("</Row>"),l.length>2&&f.push(l.join(""))}return f.join("")}function ag(e,t,r){var a=[],n=r.SheetNames[e],i=r.Sheets[n],s=i?q2(i,t,e,r):"";return s.length>0&&a.push("<Names>"+s+"</Names>"),s=i?tg(i,t,e,r):"",s.length>0&&a.push("<Table>"+s+"</Table>"),a.push(Z2(i,t,e,r)),a.join("")}function ng(e,t){t||(t={}),e.SSF||(e.SSF=He(ge)),e.SSF&&(_a(),R0(e.SSF),t.revssf=P0(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Lt(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(K2(e,t)),r.push(j2()),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push(ie("Worksheet",ag(a,t,e),{"ss:Name":Le(e.SheetNames[a])}));return r[2]=Y2(e,t),r[3]=J2(e),rr+ie("Workbook",r.join(""),{xmlns:Pr.ss,"xmlns:o":Pr.o,"xmlns:x":Pr.x,"xmlns:ss":Pr.ss,"xmlns:dt":Pr.dt,"xmlns:html":Pr.html})}function ig(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=Jl(r),r.length-r.l<=4)return t;var a=r.read_shift(4);if(a==0||a>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(a=r.read_shift(4),a!==1907505652)||(t.UnicodeClipboardFormat=ql(r),a=r.read_shift(4),a==0||a>40))return t;r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}var sg=[60,1084,2066,2165,2175];function fg(e,t,r,a,n){var i=a,s=[],f=r.slice(r.l,r.l+i);if(n&&n.enc&&n.enc.insitu&&f.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:n.enc.insitu(f)}s.push(f),r.l+=i;for(var c=pt(r,r.l),o=xn[c],l=0;o!=null&&sg.indexOf(c)>-1;)i=pt(r,r.l+2),l=r.l+4,c==2066?l+=4:(c==2165||c==2175)&&(l+=12),f=r.slice(l,r.l+4+i),s.push(f),r.l+=4+i,o=xn[c=pt(r,r.l)];var u=or(s);xr(u,0);var h=0;u.lens=[];for(var d=0;d<s.length;++d)u.lens.push(h),h+=s[d].length;if(u.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+u.length+" < "+a;return t.f(u,u.length,n)}function nt(e,t,r){if(e.t!=="z"&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=ge[a])}catch(i){if(t.WTF)throw i}if(!t||t.cellText!==!1)try{e.t==="e"?e.w=e.w||Et[e.v]:a===0||a=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=Wa(e.v):e.w=Kt(e.v):e.w=Yr(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(i){if(t.WTF)throw i}if(t.cellDates&&a&&e.t=="n"&&ga(ge[a]||String(a))){var n=Ht(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function m0(e,t,r){return{v:e,ixfe:t,t:r}}function cg(e,t){var r={opts:{}},a={},n=t.dense?[]:{},i={},s={},f=null,c=[],o="",l={},u,h="",d,p,x,m,_={},T=[],g,I,L=[],D=[],k={Sheets:[],WBProps:{date1904:!1},Views:[{}]},R={},O=function(ye){return ye<8?Gt[ye]:ye<64&&D[ye-8]||Gt[ye]},X=function(ye,Ze,Vr){var ir=Ze.XF.data;if(!(!ir||!ir.patternType||!Vr||!Vr.cellStyles)){Ze.s={},Ze.s.patternType=ir.patternType;var Bt;(Bt=za(O(ir.icvFore)))&&(Ze.s.fgColor={rgb:Bt}),(Bt=za(O(ir.icvBack)))&&(Ze.s.bgColor={rgb:Bt})}},M=function(ye,Ze,Vr){if(!(ve>1)&&!(Vr.sheetRows&&ye.r>=Vr.sheetRows)){if(Vr.cellStyles&&Ze.XF&&Ze.XF.data&&X(ye,Ze,Vr),delete Ze.ixfe,delete Ze.XF,u=ye,h=_e(ye),(!s||!s.s||!s.e)&&(s={s:{r:0,c:0},e:{r:0,c:0}}),ye.r<s.s.r&&(s.s.r=ye.r),ye.c<s.s.c&&(s.s.c=ye.c),ye.r+1>s.e.r&&(s.e.r=ye.r+1),ye.c+1>s.e.c&&(s.e.c=ye.c+1),Vr.cellFormula&&Ze.f){for(var ir=0;ir<T.length;++ir)if(!(T[ir][0].s.c>ye.c||T[ir][0].s.r>ye.r)&&!(T[ir][0].e.c<ye.c||T[ir][0].e.r<ye.r)){Ze.F=Ee(T[ir][0]),(T[ir][0].s.c!=ye.c||T[ir][0].s.r!=ye.r)&&delete Ze.f,Ze.f&&(Ze.f=""+_r(T[ir][1],s,ye,V,P));break}}Vr.dense?(n[ye.r]||(n[ye.r]=[]),n[ye.r][ye.c]=Ze):n[h]=Ze}},P={enc:!1,sbcch:0,snames:[],sharedf:_,arrayf:T,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(P.password=t.password);var J,le=[],Z=[],ue=[],ce=[],Ie=!1,V=[];V.SheetNames=P.snames,V.sharedf=P.sharedf,V.arrayf=P.arrayf,V.names=[],V.XTI=[];var pe=0,ve=0,C=0,B=[],b=[],N;P.codepage=1200,Zr(1200);for(var j=!1;e.l<e.length-1;){var ae=e.l,ne=e.read_shift(2);if(ne===0&&pe===10)break;var re=e.l===e.length?0:e.read_shift(2),Q=xn[ne];if(Q&&Q.f){if(t.bookSheets&&pe===133&&ne!==133)break;if(pe=ne,Q.r===2||Q.r==12){var Se=e.read_shift(2);if(re-=2,!P.enc&&Se!==ne&&((Se&255)<<8|Se>>8)!==ne)throw new Error("rt mismatch: "+Se+"!="+ne);Q.r==12&&(e.l+=10,re-=10)}var A={};if(ne===10?A=Q.f(e,re,P):A=fg(ne,Q,e,re,P),ve==0&&[9,521,1033,2057].indexOf(pe)===-1)continue;switch(ne){case 34:r.opts.Date1904=k.WBProps.date1904=A;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(P.enc||(e.l=0),P.enc=A,!t.password)throw new Error("File is password-protected");if(A.valid==null)throw new Error("Encryption scheme unsupported");if(!A.valid)throw new Error("Password is incorrect");break;case 92:P.lastuser=A;break;case 66:var Ue=Number(A);switch(Ue){case 21010:Ue=1200;break;case 32768:Ue=1e4;break;case 32769:Ue=1252;break}Zr(P.codepage=Ue),j=!0;break;case 317:P.rrtabid=A;break;case 25:P.winlocked=A;break;case 439:r.opts.RefreshAll=A;break;case 12:r.opts.CalcCount=A;break;case 16:r.opts.CalcDelta=A;break;case 17:r.opts.CalcIter=A;break;case 13:r.opts.CalcMode=A;break;case 14:r.opts.CalcPrecision=A;break;case 95:r.opts.CalcSaveRecalc=A;break;case 15:P.CalcRefMode=A;break;case 2211:r.opts.FullCalc=A;break;case 129:A.fDialog&&(n["!type"]="dialog"),A.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),A.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:L.push(A);break;case 430:V.push([A]),V[V.length-1].XTI=[];break;case 35:case 547:V[V.length-1].push(A);break;case 24:case 536:N={Name:A.Name,Ref:_r(A.rgce,s,null,V,P)},A.itab>0&&(N.Sheet=A.itab-1),V.names.push(N),V[0]||(V[0]=[],V[0].XTI=[]),V[V.length-1].push(A),A.Name=="_xlnm._FilterDatabase"&&A.itab>0&&A.rgce&&A.rgce[0]&&A.rgce[0][0]&&A.rgce[0][0][0]=="PtgArea3d"&&(b[A.itab-1]={ref:Ee(A.rgce[0][0][1][2])});break;case 22:P.ExternCount=A;break;case 23:V.length==0&&(V[0]=[],V[0].XTI=[]),V[V.length-1].XTI=V[V.length-1].XTI.concat(A),V.XTI=V.XTI.concat(A);break;case 2196:if(P.biff<8)break;N!=null&&(N.Comment=A[1]);break;case 18:n["!protect"]=A;break;case 19:A!==0&&P.WTF&&console.error("Password verifier: "+A);break;case 133:i[A.pos]=A,P.snames.push(A.name);break;case 10:{if(--ve)break;if(s.e){if(s.e.r>0&&s.e.c>0){if(s.e.r--,s.e.c--,n["!ref"]=Ee(s),t.sheetRows&&t.sheetRows<=s.e.r){var De=s.e.r;s.e.r=t.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=Ee(s),s.e.r=De}s.e.r++,s.e.c++}le.length>0&&(n["!merges"]=le),Z.length>0&&(n["!objects"]=Z),ue.length>0&&(n["!cols"]=ue),ce.length>0&&(n["!rows"]=ce),k.Sheets.push(R)}o===""?l=n:a[o]=n,n=t.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(P.biff===8&&(P.biff={9:2,521:3,1033:4}[ne]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[A.BIFFVer]||8),P.biffguess=A.BIFFVer==0,A.BIFFVer==0&&A.dt==4096&&(P.biff=5,j=!0,Zr(P.codepage=28591)),P.biff==8&&A.BIFFVer==0&&A.dt==16&&(P.biff=2),ve++)break;if(n=t.dense?[]:{},P.biff<8&&!j&&(j=!0,Zr(P.codepage=t.codepage||1252)),P.biff<5||A.BIFFVer==0&&A.dt==4096){o===""&&(o="Sheet1"),s={s:{r:0,c:0},e:{r:0,c:0}};var Me={pos:e.l-re,name:o};i[Me.pos]=Me,P.snames.push(o)}else o=(i[ae]||{name:""}).name;A.dt==32&&(n["!type"]="chart"),A.dt==64&&(n["!type"]="macro"),le=[],Z=[],P.arrayf=T=[],ue=[],ce=[],Ie=!1,R={Hidden:(i[ae]||{hs:0}).hs,name:o}}break;case 515:case 3:case 2:n["!type"]=="chart"&&(t.dense?(n[A.r]||[])[A.c]:n[_e({c:A.c,r:A.r})])&&++A.c,g={ixfe:A.ixfe,XF:L[A.ixfe]||{},v:A.val,t:"n"},C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:A.c,r:A.r},g,t);break;case 5:case 517:g={ixfe:A.ixfe,XF:L[A.ixfe],v:A.val,t:A.t},C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:A.c,r:A.r},g,t);break;case 638:g={ixfe:A.ixfe,XF:L[A.ixfe],v:A.rknum,t:"n"},C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:A.c,r:A.r},g,t);break;case 189:for(var Ce=A.c;Ce<=A.C;++Ce){var oe=A.rkrec[Ce-A.c][0];g={ixfe:oe,XF:L[oe],v:A.rkrec[Ce-A.c][1],t:"n"},C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:Ce,r:A.r},g,t)}break;case 6:case 518:case 1030:{if(A.val=="String"){f=A;break}if(g=m0(A.val,A.cell.ixfe,A.tt),g.XF=L[g.ixfe],t.cellFormula){var qe=A.formula;if(qe&&qe[0]&&qe[0][0]&&qe[0][0][0]=="PtgExp"){var Wr=qe[0][0][1][0],tt=qe[0][0][1][1],ut=_e({r:Wr,c:tt});_[ut]?g.f=""+_r(A.formula,s,A.cell,V,P):g.F=((t.dense?(n[Wr]||[])[tt]:n[ut])||{}).F}else g.f=""+_r(A.formula,s,A.cell,V,P)}C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M(A.cell,g,t),f=A}break;case 7:case 519:if(f)f.val=A,g=m0(A,f.cell.ixfe,"s"),g.XF=L[g.ixfe],t.cellFormula&&(g.f=""+_r(f.formula,s,f.cell,V,P)),C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M(f.cell,g,t),f=null;else throw new Error("String record expects Formula");break;case 33:case 545:{T.push(A);var Ea=_e(A[0].s);if(d=t.dense?(n[A[0].s.r]||[])[A[0].s.c]:n[Ea],t.cellFormula&&d){if(!f||!Ea||!d)break;d.f=""+_r(A[1],s,A[0],V,P),d.F=Ee(A[0])}}break;case 1212:{if(!t.cellFormula)break;if(h){if(!f)break;_[_e(f.cell)]=A[0],d=t.dense?(n[f.cell.r]||[])[f.cell.c]:n[_e(f.cell)],(d||{}).f=""+_r(A[0],s,u,V,P)}}break;case 253:g=m0(c[A.isst].t,A.ixfe,"s"),c[A.isst].h&&(g.h=c[A.isst].h),g.XF=L[g.ixfe],C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:A.c,r:A.r},g,t);break;case 513:t.sheetStubs&&(g={ixfe:A.ixfe,XF:L[A.ixfe],t:"z"},C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:A.c,r:A.r},g,t));break;case 190:if(t.sheetStubs)for(var Tt=A.c;Tt<=A.C;++Tt){var Nr=A.ixfe[Tt-A.c];g={ixfe:Nr,XF:L[Nr],t:"z"},C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:Tt,r:A.r},g,t)}break;case 214:case 516:case 4:g=m0(A.val,A.ixfe,"s"),g.XF=L[g.ixfe],C>0&&(g.z=B[g.ixfe>>8&63]),nt(g,t,r.opts.Date1904),M({c:A.c,r:A.r},g,t);break;case 0:case 512:ve===1&&(s=A);break;case 252:c=A;break;case 1054:if(P.biff==4){B[C++]=A[1];for(var ht=0;ht<C+163&&ge[ht]!=A[1];++ht);ht>=163&&vt(A[1],C+163)}else vt(A[1],A[0]);break;case 30:{B[C++]=A;for(var St=0;St<C+163&&ge[St]!=A;++St);St>=163&&vt(A,C+163)}break;case 229:le=le.concat(A);break;case 93:Z[A.cmo[0]]=P.lastobj=A;break;case 438:P.lastobj.TxO=A;break;case 127:P.lastobj.ImData=A;break;case 440:for(m=A[0].s.r;m<=A[0].e.r;++m)for(x=A[0].s.c;x<=A[0].e.c;++x)d=t.dense?(n[m]||[])[x]:n[_e({c:x,r:m})],d&&(d.l=A[1]);break;case 2048:for(m=A[0].s.r;m<=A[0].e.r;++m)for(x=A[0].s.c;x<=A[0].e.c;++x)d=t.dense?(n[m]||[])[x]:n[_e({c:x,r:m})],d&&d.l&&(d.l.Tooltip=A[1]);break;case 28:{if(P.biff<=5&&P.biff>=2)break;d=t.dense?(n[A[0].r]||[])[A[0].c]:n[_e(A[0])];var Ta=Z[A[2]];d||(t.dense?(n[A[0].r]||(n[A[0].r]=[]),d=n[A[0].r][A[0].c]={t:"z"}):d=n[_e(A[0])]={t:"z"},s.e.r=Math.max(s.e.r,A[0].r),s.s.r=Math.min(s.s.r,A[0].r),s.e.c=Math.max(s.e.c,A[0].c),s.s.c=Math.min(s.s.c,A[0].c)),d.c||(d.c=[]),p={a:A[1],t:Ta.TxO.t},d.c.push(p)}break;case 2173:Yx(L[A.ixfe],A.ext);break;case 125:{if(!P.cellStyles)break;for(;A.e>=A.s;)ue[A.e--]={width:A.w/256,level:A.level||0,hidden:!!(A.flags&1)},Ie||(Ie=!0,Xn(A.w/256)),Ot(ue[A.e+1])}break;case 520:{var Fr={};A.level!=null&&(ce[A.r]=Fr,Fr.level=A.level),A.hidden&&(ce[A.r]=Fr,Fr.hidden=!0),A.hpt&&(ce[A.r]=Fr,Fr.hpt=A.hpt,Fr.hpx=va(A.hpt))}break;case 38:case 39:case 40:case 41:n["!margins"]||$t(n["!margins"]={}),n["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[ne]]=A;break;case 161:n["!margins"]||$t(n["!margins"]={}),n["!margins"].header=A.header,n["!margins"].footer=A.footer;break;case 574:A.RTL&&(k.Views[0].RTL=!0);break;case 146:D=A;break;case 2198:J=A;break;case 140:I=A;break;case 442:o?R.CodeName=A||R.name:k.WBProps.CodeName=A||"ThisWorkbook";break}}else Q||console.error("Missing Info for XLS Record 0x"+ne.toString(16)),e.l+=re}return r.SheetNames=Je(i).sort(function(at,ye){return Number(at)-Number(ye)}).map(function(at){return i[at].name}),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&l["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=l)):r.Preamble=l,r.Sheets&&b.forEach(function(at,ye){r.Sheets[r.SheetNames[ye]]["!autofilter"]=at}),r.Strings=c,r.SSF=He(ge),P.enc&&(r.Encryption=P.enc),J&&(r.Themes=J),r.Metadata={},I!==void 0&&(r.Metadata.Country=I),V.names.length>0&&(k.Names=V.names),r.Workbook=k,r}var Ma={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function og(e,t,r){var a=me.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Gi(a,sn,Ma.DSI);for(var i in n)t[i]=n[i]}catch(o){if(r.WTF)throw o}var s=me.find(e,"/!SummaryInformation");if(s&&s.size>0)try{var f=Gi(s,fn,Ma.SI);for(var c in f)t[c]==null&&(t[c]=f[c])}catch(o){if(r.WTF)throw o}t.HeadingPairs&&t.TitlesOfParts&&(Tf(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}function lg(e,t){var r=[],a=[],n=[],i=0,s,f=_i(sn,"n"),c=_i(fn,"n");if(e.Props)for(s=Je(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(c,s[i])?a:n).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=Je(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(c,s[i])?a:n).push([s[i],e.Custprops[s[i]]]);var o=[];for(i=0;i<n.length;++i)If.indexOf(n[i][0])>-1||Ef.indexOf(n[i][0])>-1||n[i][1]!=null&&o.push(n[i]);a.length&&me.utils.cfb_add(t,"/SummaryInformation",Xi(a,Ma.SI,c,fn)),(r.length||o.length)&&me.utils.cfb_add(t,"/DocumentSummaryInformation",Xi(r,Ma.DSI,f,sn,o.length?o:null,Ma.UDI))}function Fc(e,t){t||(t={}),Qn(t),wn(),t.codepage&&_n(t.codepage);var r,a;if(e.FullPaths){if(me.find(e,"/encryption"))throw new Error("File is password-protected");r=me.find(e,"!CompObj"),a=me.find(e,"/Workbook")||me.find(e,"/Book")}else{switch(t.type){case"base64":e=Br(Ur(e));break;case"binary":e=Br(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}xr(e,0),a={content:e}}var n,i;if(r&&ig(r),t.bookProps&&!t.bookSheets)n={};else{var s=Fe?"buffer":"array";if(a&&a.content)n=cg(a.content,t);else if((i=me.find(e,"PerfectOffice_MAIN"))&&i.content)n=zt.to_workbook(i.content,(t.type=s,t));else if((i=me.find(e,"NativeContent_MAIN"))&&i.content)n=zt.to_workbook(i.content,(t.type=s,t));else throw(i=me.find(e,"MN0"))&&i.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&me.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=Td(e))}var f={};return e.FullPaths&&og(e,f,t),n.Props=n.Custprops=f,t.bookFiles&&(n.cfb=e),n}function ug(e,t){var r=t||{},a=me.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return me.utils.cfb_add(a,n,yc(e,r)),r.biff==8&&(e.Props||e.Custprops)&&lg(e,a),r.biff==8&&e.vbaraw&&Sd(a,me.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),a}var Ya={0:{f:Pm},1:{f:Gm},2:{f:cv},3:{f:qm},4:{f:Km},5:{f:iv},6:{f:xv},7:{f:rv},8:{f:wv},9:{f:_v},10:{f:vv},11:{f:gv},12:{f:zm},13:{f:lv},14:{f:Qm},15:{f:Ym},16:{f:vc},17:{f:pv},18:{f:av},19:{f:Nn},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:p2},40:{},42:{},43:{f:xx},44:{f:ux},45:{f:mx},46:{f:gx},47:{f:vx},48:{},49:{f:Ml},50:{},51:{f:Zx},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Vf},62:{f:hv},63:{f:fd},64:{f:Lv},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Tr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:bv},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Vm},148:{f:Mm,p:16},151:{f:yv},152:{},153:{f:h2},154:{},155:{},156:{f:l2},157:{},158:{},159:{T:1,f:C1},160:{T:-1},161:{T:1,f:aa},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:kv},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:Jx},336:{T:-1},337:{f:rd,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:nn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Wf},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Av},427:{f:Cv},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Iv},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:Wm},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Sv},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:nn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:gd},633:{T:1},634:{T:-1},635:{T:1,f:md},636:{T:-1},637:{f:Hl},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Qv},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Bv},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},xn={6:{f:J0},10:{f:Ft},12:{f:nr},13:{f:nr},14:{f:Qe},15:{f:Qe},16:{f:kr},17:{f:Qe},18:{f:Qe},19:{f:nr},20:{f:Ji},21:{f:Ji},23:{f:Wf},24:{f:Zi},25:{f:Qe},26:{},27:{},28:{f:Bh},29:{},34:{f:Qe},35:{f:qi},38:{f:kr},39:{f:kr},40:{f:kr},41:{f:kr},42:{f:Qe},43:{f:Qe},47:{f:Y1},49:{f:lh},51:{f:nr},60:{},61:{f:ih},64:{f:Qe},65:{f:oh},66:{f:nr},77:{},80:{},81:{},82:{},85:{f:nr},89:{},90:{},91:{},92:{f:ju},93:{f:Wh},94:{},95:{f:Qe},96:{},97:{},99:{f:Qe},125:{f:Vf},128:{f:Fh},129:{f:Ju},130:{f:nr},131:{f:Qe},132:{f:Qe},133:{f:qu},134:{},140:{f:jh},141:{f:nr},144:{},146:{f:qh},151:{},152:{},153:{},154:{},155:{},156:{f:nr},157:{},158:{},160:{f:n1},161:{f:e1},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:kh},190:{f:Eh},193:{f:Ft},197:{},198:{},199:{},200:{},201:{},202:{f:Qe},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:nr},220:{},221:{f:Qe},222:{},224:{f:Sh},225:{f:Ku},226:{f:Ft},227:{},229:{f:Mh},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:Qu},253:{f:hh},255:{f:rh},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:Of},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Qe},353:{f:Ft},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:Ih},431:{f:Qe},432:{},433:{},434:{},437:{},438:{f:Gh},439:{f:Qe},440:{f:Xh},441:{},442:{f:r0},443:{},444:{f:nr},445:{},446:{},448:{f:Ft},449:{f:nh,r:2},450:{f:Ft},512:{f:Ki},513:{f:a1},515:{f:Ch},516:{f:dh},517:{f:Yi},519:{f:i1},520:{f:th},523:{},545:{f:Qi},549:{f:$i},566:{},574:{f:fh},638:{f:wh},659:{},1048:{},1054:{f:mh},1084:{},1212:{f:Nh},2048:{f:$h},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:x0},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Ft},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:Zh,r:12},2173:{f:jx,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Qe,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:Rh,r:12},2197:{},2198:{f:Hx,r:12},2199:{},2200:{},2201:{},2202:{f:Ph,r:12},2203:{f:Ft},2204:{},2205:{},2206:{},2207:{},2211:{f:ah},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:nr},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:r1},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:Jh},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Ki},1:{},2:{f:l1},3:{f:c1},4:{f:f1},5:{f:Yi},7:{f:h1},8:{},9:{f:x0},11:{},22:{f:nr},30:{f:gh},31:{},32:{},33:{f:Qi},36:{},37:{f:$i},50:{f:x1},62:{},52:{},67:{},68:{f:nr},69:{},86:{},126:{},127:{f:s1},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:d1},223:{},234:{},354:{},421:{},518:{f:J0},521:{f:x0},536:{f:Zi},547:{f:qi},561:{},579:{},1030:{f:J0},1033:{f:x0},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function se(e,t,r,a){var n=t;if(!isNaN(n)){var i=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,n),s.write_shift(2,i),i>0&&On(r)&&e.push(r)}}function hg(e,t,r,a){var n=a||(r||[]).length||0;if(n<=8224)return se(e,t,r,n);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,c=0,o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;var l=e.next(4);for(l.write_shift(2,i),l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o;c<n;){for(l=e.next(4),l.write_shift(2,60),o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;l.write_shift(2,o),e.push(r.slice(c,c+o)),c+=o}}}function i0(e,t,r){return e||(e=z(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function xg(e,t,r,a){var n=z(9);return i0(n,e,t),bf(r,a||"b",n),n}function dg(e,t,r){var a=z(8+2*r.length);return i0(a,e,t),a.write_shift(1,r.length),a.write_shift(r.length,r,"sbcs"),a.l<a.length?a.slice(0,a.l):a}function pg(e,t,r,a){if(t.v!=null)switch(t.t){case"d":case"n":var n=t.t=="d"?fr(Ve(t.v)):t.v;n==(n|0)&&n>=0&&n<65536?se(e,2,u1(r,a,n)):se(e,3,o1(r,a,n));return;case"b":case"e":se(e,5,xg(r,a,t.v,t.t));return;case"s":case"str":se(e,4,dg(r,a,(t.v||"").slice(0,255)));return}se(e,1,i0(null,r,a))}function mg(e,t,r,a){var n=Array.isArray(t),i=be(t["!ref"]||"A1"),s,f="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=Ee(i)}for(var o=i.s.r;o<=i.e.r;++o){f=er(o);for(var l=i.s.c;l<=i.e.c;++l){o===i.s.r&&(c[l]=$e(l)),s=c[l]+f;var u=n?(t[o]||[])[l]:t[s];u&&pg(e,u,o,l)}}}function vg(e,t){for(var r=t||{},a=br(),n=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(n=i);if(n==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return se(a,r.biff==4?1033:r.biff==3?521:9,Vn(e,16,r)),mg(a,e.Sheets[e.SheetNames[n]],n,r),se(a,10),a.end()}function gg(e,t,r){se(e,49,uh({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function _g(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&se(e,1054,vh(n,t[n],r))})}function wg(e,t){var r=z(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),se(e,2151,r),r=z(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),Bf(be(t["!ref"]||"A1"),r),r.write_shift(4,4),se(e,2152,r)}function kg(e,t){for(var r=0;r<16;++r)se(e,224,ji({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(a){se(e,224,ji(a,0,t))})}function Eg(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];se(e,440,zh(a)),a[1].Tooltip&&se(e,2048,Kh(a))}delete t["!links"]}function Tg(e,t){if(t){var r=0;t.forEach(function(a,n){++r<=256&&a&&se(e,125,Qh(W0(n,a),n))})}}function Sg(e,t,r,a,n){var i=16+Lt(n.cellXfs,t,n);if(t.v==null&&!t.bf){se(e,513,qt(r,a,i));return}if(t.bf)se(e,6,Jp(t,r,a,n,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?fr(Ve(t.v)):t.v;se(e,515,Dh(r,a,s,i));break;case"b":case"e":se(e,517,Ah(r,a,t.v,i,n,t.t));break;case"s":case"str":if(n.bookSST){var f=Yn(n.Strings,t.v,n.revStrings);se(e,253,xh(r,a,f,i))}else se(e,516,ph(r,a,(t.v||"").slice(0,255),i,n));break;default:se(e,513,qt(r,a,i))}}function Fg(e,t,r){var a=br(),n=r.SheetNames[e],i=r.Sheets[n]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},c=Array.isArray(i),o=t.biff==8,l,u="",h=[],d=be(i["!ref"]||"A1"),p=o?65536:16384;if(d.e.c>255||d.e.r>=p){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,p-1)}se(a,2057,Vn(r,16,t)),se(a,13,Kr(1)),se(a,12,Kr(100)),se(a,15,Cr(!0)),se(a,17,Cr(!1)),se(a,16,Yt(.001)),se(a,95,Cr(!0)),se(a,42,Cr(!1)),se(a,43,Cr(!1)),se(a,130,Kr(1)),se(a,128,yh([0,0])),se(a,131,Cr(!1)),se(a,132,Cr(!1)),o&&Tg(a,i["!cols"]),se(a,512,_h(d,t)),o&&(i["!links"]=[]);for(var x=d.s.r;x<=d.e.r;++x){u=er(x);for(var m=d.s.c;m<=d.e.c;++m){x===d.s.r&&(h[m]=$e(m)),l=h[m]+u;var _=c?(i[x]||[])[m]:i[l];_&&(Sg(a,_,x,m,t),o&&_.l&&i["!links"].push([l,_.l]))}}var T=f.CodeName||f.name||n;return o&&se(a,574,ch((s.Views||[])[0])),o&&(i["!merges"]||[]).length&&se(a,229,Uh(i["!merges"])),o&&Eg(a,i),se(a,442,Rf(T)),o&&wg(a,i),se(a,10),a.end()}function yg(e,t,r){var a=br(),n=(e||{}).Workbook||{},i=n.Sheets||[],s=n.WBProps||{},f=r.biff==8,c=r.biff==5;if(se(a,2057,Vn(e,5,r)),r.bookType=="xla"&&se(a,135),se(a,225,f?Kr(1200):null),se(a,193,Au(2)),c&&se(a,191),c&&se(a,192),se(a,226),se(a,92,Yu("SheetJS",r)),se(a,66,Kr(f?1200:1252)),f&&se(a,353,Kr(0)),f&&se(a,448),se(a,317,t1(e.SheetNames.length)),f&&e.vbaraw&&se(a,211),f&&e.vbaraw){var o=s.CodeName||"ThisWorkbook";se(a,442,Rf(o))}se(a,156,Kr(17)),se(a,25,Cr(!1)),se(a,18,Cr(!1)),se(a,19,Kr(0)),f&&se(a,431,Cr(!1)),f&&se(a,444,Kr(0)),se(a,61,sh()),se(a,64,Cr(!1)),se(a,141,Kr(0)),se(a,34,Cr(n2(e)=="true")),se(a,14,Cr(!0)),f&&se(a,439,Cr(!1)),se(a,218,Kr(0)),gg(a,e,r),_g(a,e.SSF,r),kg(a,r),f&&se(a,352,Cr(!1));var l=a.end(),u=br();f&&se(u,140,Yh()),f&&r.Strings&&hg(u,252,eh(r.Strings)),se(u,10);var h=u.end(),d=br(),p=0,x=0;for(x=0;x<e.SheetNames.length;++x)p+=(f?12:11)+(f?2:1)*e.SheetNames[x].length;var m=l.length+p+h.length;for(x=0;x<e.SheetNames.length;++x){var _=i[x]||{};se(d,133,Zu({pos:m,hs:_.Hidden||0,dt:0,name:e.SheetNames[x]},r)),m+=t[x].length}var T=d.end();if(p!=T.length)throw new Error("BS8 "+p+" != "+T.length);var g=[];return l.length&&g.push(l),T.length&&g.push(T),h.length&&g.push(h),or(g)}function Ag(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=He(ge)),e&&e.SSF&&(_a(),R0(e.SSF),r.revssf=P0(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,ei(r),r.cellXfs=[],Lt(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=Fg(n,r,e);return a.unshift(yg(e,a,r)),or(a)}function yc(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];if(!(!a||!a["!ref"])){var n=Rr(a["!ref"]);n.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Ag(e,t);case 4:case 3:case 2:return vg(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function os(e,t){var r=t||{},a=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i),s=n.index,f=i&&i.index||e.length,c=hl(e.slice(s,f),/(:?<tr[^>]*>)/i,"<tr>"),o=-1,l=0,u=0,h=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(s=0;s<c.length;++s){var x=c[s].trim(),m=x.slice(0,3).toLowerCase();if(m=="<tr"){if(++o,r.sheetRows&&r.sheetRows<=o){--o;break}l=0;continue}if(!(m!="<td"&&m!="<th")){var _=x.split(/<\/t[dh]>/i);for(f=0;f<_.length;++f){var T=_[f].trim();if(T.match(/<t[dh]/i)){for(var g=T,I=0;g.charAt(0)=="<"&&(I=g.indexOf(">"))>-1;)g=g.slice(I+1);for(var L=0;L<p.length;++L){var D=p[L];D.s.c==l&&D.s.r<o&&o<=D.e.r&&(l=D.e.c+1,L=-1)}var k=we(T.slice(0,T.indexOf(">")));h=k.colspan?+k.colspan:1,((u=+k.rowspan)>1||h>1)&&p.push({s:{r:o,c:l},e:{r:o+(u||1)-1,c:l+h-1}});var R=k.t||k["data-t"]||"";if(!g.length){l+=h;continue}if(g=Ys(g),d.s.r>o&&(d.s.r=o),d.e.r<o&&(d.e.r=o),d.s.c>l&&(d.s.c=l),d.e.c<l&&(d.e.c=l),!g.length){l+=h;continue}var O={t:"s",v:g};r.raw||!g.trim().length||R=="s"||(g==="TRUE"?O={t:"b",v:!0}:g==="FALSE"?O={t:"b",v:!1}:isNaN(et(g))?isNaN(pa(g).getDate())||(O={t:"d",v:Ve(g)},r.cellDates||(O={t:"n",v:fr(O.v)}),O.z=r.dateNF||ge[14]):O={t:"n",v:et(g)}),r.dense?(a[o]||(a[o]=[]),a[o][l]=O):a[_e({r:o,c:l})]=O,l+=h}}}}return a["!ref"]=Ee(d),p.length&&(a["!merges"]=p),a}function Cg(e,t,r,a){for(var n=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,c=0,o=0;o<n.length;++o)if(!(n[o].s.r>r||n[o].s.c>s)&&!(n[o].e.r<r||n[o].e.c<s)){if(n[o].s.r<r||n[o].s.c<s){f=-1;break}f=n[o].e.r-n[o].s.r+1,c=n[o].e.c-n[o].s.c+1;break}if(!(f<0)){var l=_e({r,c:s}),u=a.dense?(e[r]||[])[s]:e[l],h=u&&u.v!=null&&(u.h||Cn(u.w||(_t(u),u.w)||""))||"",d={};f>1&&(d.rowspan=f),c>1&&(d.colspan=c),a.editable?h='<span contenteditable="true">'+h+"</span>":u&&(d["data-t"]=u&&u.t||"z",u.v!=null&&(d["data-v"]=u.v),u.z!=null&&(d["data-z"]=u.z),u.l&&(u.l.Target||"#").charAt(0)!="#"&&(h='<a href="'+u.l.Target+'">'+h+"</a>")),d.id=(a.id||"sjs")+"-"+l,i.push(ie("td",h,d))}}var p="<tr>";return p+i.join("")+"</tr>"}var Dg='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Ig="</body></html>";function Og(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return Pt(os(r[0],t),t);var a=ai();return r.forEach(function(n,i){ni(a,os(n,t),"Sheet"+(i+1))}),a}function bg(e,t,r){var a=[];return a.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Ac(e,t){var r=t||{},a=r.header!=null?r.header:Dg,n=r.footer!=null?r.footer:Ig,i=[a],s=Rr(e["!ref"]);r.dense=Array.isArray(e),i.push(bg(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(Cg(e,s,f,r));return i.push("</table>"+n),i.join("")}function Cc(e,t,r){var a=r||{},n=0,i=0;if(a.origin!=null)if(typeof a.origin=="number")n=a.origin;else{var s=typeof a.origin=="string"?Ke(a.origin):a.origin;n=s.r,i=s.c}var f=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,f.length),o={s:{r:0,c:0},e:{r:n,c:i}};if(e["!ref"]){var l=Rr(e["!ref"]);o.s.r=Math.min(o.s.r,l.s.r),o.s.c=Math.min(o.s.c,l.s.c),o.e.r=Math.max(o.e.r,l.e.r),o.e.c=Math.max(o.e.c,l.e.c),n==-1&&(o.e.r=n=l.e.r+1)}var u=[],h=0,d=e["!rows"]||(e["!rows"]=[]),p=0,x=0,m=0,_=0,T=0,g=0;for(e["!cols"]||(e["!cols"]=[]);p<f.length&&x<c;++p){var I=f[p];if(ls(I)){if(a.display)continue;d[x]={hidden:!0}}var L=I.children;for(m=_=0;m<L.length;++m){var D=L[m];if(!(a.display&&ls(D))){var k=D.hasAttribute("data-v")?D.getAttribute("data-v"):D.hasAttribute("v")?D.getAttribute("v"):Ys(D.innerHTML),R=D.getAttribute("data-z")||D.getAttribute("z");for(h=0;h<u.length;++h){var O=u[h];O.s.c==_+i&&O.s.r<x+n&&x+n<=O.e.r&&(_=O.e.c+1-i,h=-1)}g=+D.getAttribute("colspan")||1,((T=+D.getAttribute("rowspan")||1)>1||g>1)&&u.push({s:{r:x+n,c:_+i},e:{r:x+n+(T||1)-1,c:_+i+(g||1)-1}});var X={t:"s",v:k},M=D.getAttribute("data-t")||D.getAttribute("t")||"";k!=null&&(k.length==0?X.t=M||"z":a.raw||k.trim().length==0||M=="s"||(k==="TRUE"?X={t:"b",v:!0}:k==="FALSE"?X={t:"b",v:!1}:isNaN(et(k))?isNaN(pa(k).getDate())||(X={t:"d",v:Ve(k)},a.cellDates||(X={t:"n",v:fr(X.v)}),X.z=a.dateNF||ge[14]):X={t:"n",v:et(k)})),X.z===void 0&&R!=null&&(X.z=R);var P="",J=D.getElementsByTagName("A");if(J&&J.length)for(var le=0;le<J.length&&!(J[le].hasAttribute("href")&&(P=J[le].getAttribute("href"),P.charAt(0)!="#"));++le);P&&P.charAt(0)!="#"&&(X.l={Target:P}),a.dense?(e[x+n]||(e[x+n]=[]),e[x+n][_+i]=X):e[_e({c:_+i,r:x+n})]=X,o.e.c<_+i&&(o.e.c=_+i),_+=g}}++x}return u.length&&(e["!merges"]=(e["!merges"]||[]).concat(u)),o.e.r=Math.max(o.e.r,x-1+n),e["!ref"]=Ee(o),x>=c&&(e["!fullref"]=Ee((o.e.r=f.length-p+x-1+n,o))),e}function Dc(e,t){var r=t||{},a=r.dense?[]:{};return Cc(a,e,t)}function Rg(e,t){return Pt(Dc(e,t),t)}function ls(e){var t="",r=Ng(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Ng(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function Pg(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,n){return Array(parseInt(n,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=Oe(t.replace(/<[^>]*>/g,""));return[r]}var us={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Ic(e,t){var r=t||{},a=Dn(e),n=[],i,s,f={name:""},c="",o=0,l,u,h={},d=[],p=r.dense?[]:{},x,m,_={value:""},T="",g=0,I=[],L=-1,D=-1,k={s:{r:1e6,c:1e7},e:{r:0,c:0}},R=0,O={},X=[],M={},P=0,J=0,le=[],Z=1,ue=1,ce=[],Ie={Names:[]},V={},pe=["",""],ve=[],C={},B="",b=0,N=!1,j=!1,ae=0;for(Ga.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");x=Ga.exec(a);)switch(x[3]=x[3].replace(/_.*$/,"")){case"table":case"工作表":x[1]==="/"?(k.e.c>=k.s.c&&k.e.r>=k.s.r?p["!ref"]=Ee(k):p["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=k.e.r&&(p["!fullref"]=p["!ref"],k.e.r=r.sheetRows-1,p["!ref"]=Ee(k)),X.length&&(p["!merges"]=X),le.length&&(p["!rows"]=le),l.name=l.名称||l.name,typeof JSON<"u"&&JSON.stringify(l),d.push(l.name),h[l.name]=p,j=!1):x[0].charAt(x[0].length-2)!=="/"&&(l=we(x[0],!1),L=D=-1,k.s.r=k.s.c=1e7,k.e.r=k.e.c=0,p=r.dense?[]:{},X=[],le=[],j=!0);break;case"table-row-group":x[1]==="/"?--R:++R;break;case"table-row":case"行":if(x[1]==="/"){L+=Z,Z=1;break}if(u=we(x[0],!1),u.行号?L=u.行号-1:L==-1&&(L=0),Z=+u["number-rows-repeated"]||1,Z<10)for(ae=0;ae<Z;++ae)R>0&&(le[L+ae]={level:R});D=-1;break;case"covered-table-cell":x[1]!=="/"&&++D,r.sheetStubs&&(r.dense?(p[L]||(p[L]=[]),p[L][D]={t:"z"}):p[_e({r:L,c:D})]={t:"z"}),T="",I=[];break;case"table-cell":case"数据":if(x[0].charAt(x[0].length-2)==="/")++D,_=we(x[0],!1),ue=parseInt(_["number-columns-repeated"]||"1",10),m={t:"z",v:null},_.formula&&r.cellFormula!=!1&&(m.f=ss(Oe(_.formula))),(_.数据类型||_["value-type"])=="string"&&(m.t="s",m.v=Oe(_["string-value"]||""),r.dense?(p[L]||(p[L]=[]),p[L][D]=m):p[_e({r:L,c:D})]=m),D+=ue-1;else if(x[1]!=="/"){++D,T="",g=0,I=[],ue=1;var ne=Z?L+Z-1:L;if(D>k.e.c&&(k.e.c=D),D<k.s.c&&(k.s.c=D),L<k.s.r&&(k.s.r=L),ne>k.e.r&&(k.e.r=ne),_=we(x[0],!1),ve=[],C={},m={t:_.数据类型||_["value-type"],v:null},r.cellFormula)if(_.formula&&(_.formula=Oe(_.formula)),_["number-matrix-columns-spanned"]&&_["number-matrix-rows-spanned"]&&(P=parseInt(_["number-matrix-rows-spanned"],10)||0,J=parseInt(_["number-matrix-columns-spanned"],10)||0,M={s:{r:L,c:D},e:{r:L+P-1,c:D+J-1}},m.F=Ee(M),ce.push([M,m.F])),_.formula)m.f=ss(_.formula);else for(ae=0;ae<ce.length;++ae)L>=ce[ae][0].s.r&&L<=ce[ae][0].e.r&&D>=ce[ae][0].s.c&&D<=ce[ae][0].e.c&&(m.F=ce[ae][1]);switch((_["number-columns-spanned"]||_["number-rows-spanned"])&&(P=parseInt(_["number-rows-spanned"],10)||0,J=parseInt(_["number-columns-spanned"],10)||0,M={s:{r:L,c:D},e:{r:L+P-1,c:D+J-1}},X.push(M)),_["number-columns-repeated"]&&(ue=parseInt(_["number-columns-repeated"],10)),m.t){case"boolean":m.t="b",m.v=We(_["boolean-value"]);break;case"float":m.t="n",m.v=parseFloat(_.value);break;case"percentage":m.t="n",m.v=parseFloat(_.value);break;case"currency":m.t="n",m.v=parseFloat(_.value);break;case"date":m.t="d",m.v=Ve(_["date-value"]),r.cellDates||(m.t="n",m.v=fr(m.v)),m.z="m/d/yy";break;case"time":m.t="n",m.v=ol(_["time-value"])/86400,r.cellDates&&(m.t="d",m.v=L0(m.v)),m.z="HH:MM:SS";break;case"number":m.t="n",m.v=parseFloat(_.数据数值);break;default:if(m.t==="string"||m.t==="text"||!m.t)m.t="s",_["string-value"]!=null&&(T=Oe(_["string-value"]),I=[]);else throw new Error("Unsupported value type "+m.t)}}else{if(N=!1,m.t==="s"&&(m.v=T||"",I.length&&(m.R=I),N=g==0),V.Target&&(m.l=V),ve.length>0&&(m.c=ve,ve=[]),T&&r.cellText!==!1&&(m.w=T),N&&(m.t="z",delete m.v),(!N||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=L))for(var re=0;re<Z;++re){if(ue=parseInt(_["number-columns-repeated"]||"1",10),r.dense)for(p[L+re]||(p[L+re]=[]),p[L+re][D]=re==0?m:He(m);--ue>0;)p[L+re][D+ue]=He(m);else for(p[_e({r:L+re,c:D})]=m;--ue>0;)p[_e({r:L+re,c:D+ue})]=He(m);k.e.c<=D&&(k.e.c=D)}ue=parseInt(_["number-columns-repeated"]||"1",10),D+=ue-1,ue=0,m={},T="",I=[]}V={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(x[1]==="/"){if((i=n.pop())[0]!==x[3])throw"Bad state: "+i}else x[0].charAt(x[0].length-2)!=="/"&&n.push([x[3],!0]);break;case"annotation":if(x[1]==="/"){if((i=n.pop())[0]!==x[3])throw"Bad state: "+i;C.t=T,I.length&&(C.R=I),C.a=B,ve.push(C)}else x[0].charAt(x[0].length-2)!=="/"&&n.push([x[3],!1]);B="",b=0,T="",g=0,I=[];break;case"creator":x[1]==="/"?B=a.slice(b,x.index):b=x.index+x[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(x[1]==="/"){if((i=n.pop())[0]!==x[3])throw"Bad state: "+i}else x[0].charAt(x[0].length-2)!=="/"&&n.push([x[3],!1]);T="",g=0,I=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(x[1]==="/"){if(O[f.name]=c,(i=n.pop())[0]!==x[3])throw"Bad state: "+i}else x[0].charAt(x[0].length-2)!=="/"&&(c="",f=we(x[0],!1),n.push([x[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(n[n.length-1][0]){case"time-style":case"date-style":s=we(x[0],!1),c+=us[x[3]][s.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(n[n.length-1][0]){case"time-style":case"date-style":s=we(x[0],!1),c+=us[x[3]][s.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(x[0].slice(-2)==="/>")break;if(x[1]==="/")switch(n[n.length-1][0]){case"number-style":case"date-style":case"time-style":c+=a.slice(o,x.index);break}else o=x.index+x[0].length;break;case"named-range":s=we(x[0],!1),pe=q0(s["cell-range-address"]);var Q={Name:s.name,Ref:pe[0]+"!"+pe[1]};j&&(Q.Sheet=d.length),Ie.Names.push(Q);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(n[n.length-1][0])>-1)break;if(x[1]==="/"&&(!_||!_["string-value"])){var Se=Pg(a.slice(g,x.index));T=(T.length>0?T+`
`:"")+Se[0]}else we(x[0],!1),g=x.index+x[0].length;break;case"s":break;case"database-range":if(x[1]==="/")break;try{pe=q0(we(x[0])["target-range-address"]),h[pe[0]]["!autofilter"]={ref:pe[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(x[1]!=="/"){if(V=we(x[0],!1),!V.href)break;V.Target=Oe(V.href),delete V.href,V.Target.charAt(0)=="#"&&V.Target.indexOf(".")>-1?(pe=q0(V.Target.slice(1)),V.Target="#"+pe[0]+"!"+pe[1]):V.Target.match(/^\.\.[\\\/]/)&&(V.Target=V.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(x[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(x)}}var A={Sheets:h,SheetNames:d,Workbook:Ie};return r.bookSheets&&delete A.Sheets,A}function hs(e,t){t=t||{},$r(e,"META-INF/manifest.xml")&&cu(ar(e,"META-INF/manifest.xml"),t);var r=Mr(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=Ic(Be(r),t);return $r(e,"meta.xml")&&(a.Props=wf(ar(e,"meta.xml"))),a}function xs(e,t){return Ic(e,t)}var Lg=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Ha({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return rr+t}}(),ds=function(){var e=function(i){return Le(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,a=function(i,s,f){var c=[];c.push('      <table:table table:name="'+Le(s.SheetNames[f])+`" table:style-name="ta1">
`);var o=0,l=0,u=Rr(i["!ref"]||"A1"),h=i["!merges"]||[],d=0,p=Array.isArray(i);if(i["!cols"])for(l=0;l<=u.e.c;++l)c.push("        <table:table-column"+(i["!cols"][l]?' table:style-name="co'+i["!cols"][l].ods+'"':"")+`></table:table-column>
`);var x="",m=i["!rows"]||[];for(o=0;o<u.s.r;++o)x=m[o]?' table:style-name="ro'+m[o].ods+'"':"",c.push("        <table:table-row"+x+`></table:table-row>
`);for(;o<=u.e.r;++o){for(x=m[o]?' table:style-name="ro'+m[o].ods+'"':"",c.push("        <table:table-row"+x+`>
`),l=0;l<u.s.c;++l)c.push(t);for(;l<=u.e.c;++l){var _=!1,T={},g="";for(d=0;d!=h.length;++d)if(!(h[d].s.c>l)&&!(h[d].s.r>o)&&!(h[d].e.c<l)&&!(h[d].e.r<o)){(h[d].s.c!=l||h[d].s.r!=o)&&(_=!0),T["table:number-columns-spanned"]=h[d].e.c-h[d].s.c+1,T["table:number-rows-spanned"]=h[d].e.r-h[d].s.r+1;break}if(_){c.push(r);continue}var I=_e({r:o,c:l}),L=p?(i[o]||[])[l]:i[I];if(L&&L.f&&(T["table:formula"]=Le(tm(L.f)),L.F&&L.F.slice(0,I.length)==I)){var D=Rr(L.F);T["table:number-matrix-columns-spanned"]=D.e.c-D.s.c+1,T["table:number-matrix-rows-spanned"]=D.e.r-D.s.r+1}if(!L){c.push(t);continue}switch(L.t){case"b":g=L.v?"TRUE":"FALSE",T["office:value-type"]="boolean",T["office:boolean-value"]=L.v?"true":"false";break;case"n":g=L.w||String(L.v||0),T["office:value-type"]="float",T["office:value"]=L.v||0;break;case"s":case"str":g=L.v==null?"":L.v,T["office:value-type"]="string";break;case"d":g=L.w||Ve(L.v).toISOString(),T["office:value-type"]="date",T["office:date-value"]=Ve(L.v).toISOString(),T["table:style-name"]="ce1";break;default:c.push(t);continue}var k=e(g);if(L.l&&L.l.Target){var R=L.l.Target;R=R.charAt(0)=="#"?"#"+am(R.slice(1)):R,R.charAt(0)!="#"&&!R.match(/^\w+:/)&&(R="../"+R),k=ie("text:a",k,{"xlink:href":R.replace(/&/g,"&amp;")})}c.push("          "+ie("table:table-cell",ie("text:p",k,{}),T)+`
`)}c.push(`        </table:table-row>
`)}return c.push(`      </table:table>
`),c.join("")},n=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!cols"]){for(var l=0;l<o["!cols"].length;++l)if(o["!cols"][l]){var u=o["!cols"][l];if(u.width==null&&u.wpx==null&&u.wch==null)continue;Ot(u),u.ods=f;var h=o["!cols"][l].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+h+`"/>
`),i.push(`  </style:style>
`),++f}}});var c=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!rows"]){for(var l=0;l<o["!rows"].length;++l)if(o["!rows"][l]){o["!rows"][l].ods=c;var u=o["!rows"][l].hpx+"px";i.push('  <style:style style:name="ro'+c+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+u+`"/>
`),i.push(`  </style:style>
`),++c}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var c=[rr],o=Ha({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),l=Ha({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(c.push("<office:document"+o+l+`>
`),c.push(_f().replace(/office:document-meta/g,"office:meta"))):c.push("<office:document-content"+o+`>
`),n(c,s),c.push(`  <office:body>
`),c.push(`    <office:spreadsheet>
`);for(var u=0;u!=s.SheetNames.length;++u)c.push(a(s.Sheets[s.SheetNames[u]],s,u));return c.push(`    </office:spreadsheet>
`),c.push(`  </office:body>
`),f.bookType=="fods"?c.push("</office:document>"):c.push("</office:document-content>"),c.join("")}}();function Oc(e,t){if(t.bookType=="fods")return ds(e,t);var r=Fn(),a="",n=[],i=[];return a="mimetype",Te(r,a,"application/vnd.oasis.opendocument.spreadsheet"),a="content.xml",Te(r,a,ds(e,t)),n.push([a,"text/xml"]),i.push([a,"ContentFile"]),a="styles.xml",Te(r,a,Lg(e,t)),n.push([a,"text/xml"]),i.push([a,"StylesFile"]),a="meta.xml",Te(r,a,rr+_f()),n.push([a,"text/xml"]),i.push([a,"MetadataFile"]),a="manifest.rdf",Te(r,a,uu(i)),n.push([a,"application/rdf+xml"]),a="META-INF/manifest.xml",Te(r,a,ou(n)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Zt(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function dn(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):Be(Nt(e))}function Bg(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):Br(ct(e))}function Mg(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}function Rt(e){var t=e.reduce(function(n,i){return n+i.length},0),r=new Uint8Array(t),a=0;return e.forEach(function(n){r.set(n,a),a+=n.length}),r}function ps(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function Ug(e,t){for(var r=(e[t+15]&127)<<7|e[t+14]>>1,a=e[t+14]&1,n=t+13;n>=t;--n)a=a*256+e[n];return(e[t+15]&128?-a:a)*Math.pow(10,r-6176)}function Wg(e,t,r){var a=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(a&127)<<1;for(var i=0;n>=1;++i,n/=256)e[t+i]=n&255;e[t+15]|=r>=0?0:128}function Ja(e,t){var r=t?t[0]:0,a=e[r]&127;e:if(e[r++]>=128&&(a|=(e[r]&127)<<7,e[r++]<128||(a|=(e[r]&127)<<14,e[r++]<128)||(a|=(e[r]&127)<<21,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),a}function Ne(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Ye(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Re(e){for(var t=[],r=[0];r[0]<e.length;){var a=r[0],n=Ja(e,r),i=n&7;n=Math.floor(n/8);var s=0,f;if(n==0)break;switch(i){case 0:{for(var c=r[0];e[r[0]++]>=128;);f=e.slice(c,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=Ja(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(n," at offset ").concat(a))}var o={data:f,type:i};t[n]==null?t[n]=[o]:t[n].push(o)}return t}function ur(e){var t=[];return e.forEach(function(r,a){r.forEach(function(n){n.data&&(t.push(Ne(a*8+n.type)),n.type==2&&t.push(Ne(n.data.length)),t.push(n.data))})}),Rt(t)}function Zn(e,t){return(e==null?void 0:e.map(function(r){return t(r.data)}))||[]}function Xr(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=Ja(e,a),i=Re(e.slice(a[0],a[0]+n));a[0]+=n;var s={id:Ye(i[1][0].data),messages:[]};i[2].forEach(function(f){var c=Re(f.data),o=Ye(c[3][0].data);s.messages.push({meta:c,data:e.slice(a[0],a[0]+o)}),a[0]+=o}),(t=i[3])!=null&&t[0]&&(s.merge=Ye(i[3][0].data)>>>0>0),r.push(s)}return r}function sa(e){var t=[];return e.forEach(function(r){var a=[];a[1]=[{data:Ne(r.id),type:0}],a[2]=[],r.merge!=null&&(a[3]=[{data:Ne(+!!r.merge),type:0}]);var n=[];r.messages.forEach(function(s){n.push(s.data),s.meta[3]=[{type:0,data:Ne(s.data.length)}],a[2].push({data:ur(s.meta),type:2})});var i=ur(a);t.push(Ne(i.length)),t.push(i),n.forEach(function(s){return t.push(s)})}),Rt(t)}function Vg(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=Ja(t,r),n=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}n.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var c=0,o=0;if(i==1?(o=(t[r[0]]>>2&7)+4,c=(t[r[0]++]&224)<<3,c|=t[r[0]++]):(o=(t[r[0]++]>>2)+1,i==2?(c=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(c=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[Rt(n)],c==0)throw new Error("Invalid offset 0");if(c>n[0].length)throw new Error("Invalid offset beyond length");if(o>=c)for(n.push(n[0].slice(-c)),o-=c;o>=n[n.length-1].length;)n.push(n[n.length-1]),o-=n[n.length-1].length;n.push(n[0].slice(-c,-c+o))}}var l=Rt(n);if(l.length!=a)throw new Error("Unexpected length: ".concat(l.length," != ").concat(a));return l}function zr(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Vg(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Rt(t)}function fa(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var i=Ne(a),s=i.length;t.push(i),a<=60?(s++,t.push(new Uint8Array([a-1<<2]))):a<=256?(s+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(s+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(s+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(s+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),s+=a,n[0]=0,n[1]=s&255,n[2]=s>>8&255,n[3]=s>>16&255,r+=a}return Rt(t)}function Hg(e,t,r,a){var n=Zt(e),i=n.getUint32(4,!0),s=(a>1?12:8)+ps(i&(a>1?3470:398))*4,f=-1,c=-1,o=NaN,l=new Date(2001,0,1);i&512&&(f=n.getUint32(s,!0),s+=4),s+=ps(i&(a>1?12288:4096))*4,i&16&&(c=n.getUint32(s,!0),s+=4),i&32&&(o=n.getFloat64(s,!0),s+=8),i&64&&(l.setTime(l.getTime()+n.getFloat64(s,!0)*1e3),s+=8);var u;switch(e[2]){case 0:break;case 2:u={t:"n",v:o};break;case 3:u={t:"s",v:t[c]};break;case 5:u={t:"d",v:l};break;case 6:u={t:"b",v:o>0};break;case 7:u={t:"n",v:o/86400};break;case 8:u={t:"e",v:0};break;case 9:if(f>-1)u={t:"s",v:r[f]};else if(c>-1)u={t:"s",v:t[c]};else if(!isNaN(o))u={t:"n",v:o};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return u}function Gg(e,t,r){var a=Zt(e),n=a.getUint32(8,!0),i=12,s=-1,f=-1,c=NaN,o=NaN,l=new Date(2001,0,1);n&1&&(c=Ug(e,i),i+=16),n&2&&(o=a.getFloat64(i,!0),i+=8),n&4&&(l.setTime(l.getTime()+a.getFloat64(i,!0)*1e3),i+=8),n&8&&(f=a.getUint32(i,!0),i+=4),n&16&&(s=a.getUint32(i,!0),i+=4);var u;switch(e[1]){case 0:break;case 2:u={t:"n",v:c};break;case 3:u={t:"s",v:t[f]};break;case 5:u={t:"d",v:l};break;case 6:u={t:"b",v:o>0};break;case 7:u={t:"n",v:o/86400};break;case 8:u={t:"e",v:0};break;case 9:if(s>-1)u={t:"s",v:r[s]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)));break;case 10:u={t:"n",v:c};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)))}return u}function Q0(e,t){var r=new Uint8Array(32),a=Zt(r),n=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,Wg(r,n,e.v),i|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),i|=2,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),i|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,i,!0),r.slice(0,n)}function en(e,t){var r=new Uint8Array(32),a=Zt(r),n=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),i|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,e.v?1:0,!0),i|=32,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),i|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,i,!0),r.slice(0,n)}function Xg(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return Hg(e,t,r,e[0]);case 5:return Gg(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function gr(e){var t=Re(e);return Ja(t[1][0].data)}function ms(e,t){var r=Re(t.data),a=Ye(r[1][0].data),n=r[3],i=[];return(n||[]).forEach(function(s){var f=Re(s.data),c=Ye(f[1][0].data)>>>0;switch(a){case 1:i[c]=dn(f[3][0].data);break;case 8:{var o=e[gr(f[9][0].data)][0],l=Re(o.data),u=e[gr(l[1][0].data)][0],h=Ye(u.meta[1][0].data);if(h!=2001)throw new Error("2000 unexpected reference to ".concat(h));var d=Re(u.data);i[c]=d[3].map(function(p){return dn(p.data)}).join("")}break}}),i}function zg(e,t){var r,a,n,i,s,f,c,o,l,u,h,d,p,x,m=Re(e),_=Ye(m[1][0].data)>>>0,T=Ye(m[2][0].data)>>>0,g=((a=(r=m[8])==null?void 0:r[0])==null?void 0:a.data)&&Ye(m[8][0].data)>0||!1,I,L;if((i=(n=m[7])==null?void 0:n[0])!=null&&i.data&&t!=0)I=(f=(s=m[7])==null?void 0:s[0])==null?void 0:f.data,L=(o=(c=m[6])==null?void 0:c[0])==null?void 0:o.data;else if((u=(l=m[4])==null?void 0:l[0])!=null&&u.data&&t!=1)I=(d=(h=m[4])==null?void 0:h[0])==null?void 0:d.data,L=(x=(p=m[3])==null?void 0:p[0])==null?void 0:x.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var D=g?4:1,k=Zt(I),R=[],O=0;O<I.length/2;++O){var X=k.getUint16(O*2,!0);X<65535&&R.push([O,X])}if(R.length!=T)throw"Expected ".concat(T," cells, found ").concat(R.length);var M=[];for(O=0;O<R.length-1;++O)M[R[O][0]]=L.subarray(R[O][1]*D,R[O+1][1]*D);return R.length>=1&&(M[R[R.length-1][0]]=L.subarray(R[R.length-1][1]*D)),{R:_,cells:M}}function $g(e,t){var r,a=Re(t.data),n=(r=a==null?void 0:a[7])!=null&&r[0]?Ye(a[7][0].data)>>>0>0?1:0:-1,i=Zn(a[5],function(s){return zg(s,n)});return{nrows:Ye(a[4][0].data)>>>0,data:i.reduce(function(s,f){return s[f.R]||(s[f.R]=[]),f.cells.forEach(function(c,o){if(s[f.R][o])throw new Error("Duplicate cell r=".concat(f.R," c=").concat(o));s[f.R][o]=c}),s},[])}}function Kg(e,t,r){var a,n=Re(t.data),i={s:{r:0,c:0},e:{r:0,c:0}};if(i.e.r=(Ye(n[6][0].data)>>>0)-1,i.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(i.e.c=(Ye(n[7][0].data)>>>0)-1,i.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=Ee(i);var s=Re(n[4][0].data),f=ms(e,e[gr(s[4][0].data)][0]),c=(a=s[17])!=null&&a[0]?ms(e,e[gr(s[17][0].data)][0]):[],o=Re(s[3][0].data),l=0;o[1].forEach(function(u){var h=Re(u.data),d=e[gr(h[2][0].data)][0],p=Ye(d.meta[1][0].data);if(p!=6002)throw new Error("6001 unexpected reference to ".concat(p));var x=$g(e,d);x.data.forEach(function(m,_){m.forEach(function(T,g){var I=_e({r:l+_,c:g}),L=Xg(T,f,c);L&&(r[I]=L)})}),l+=x.nrows})}function jg(e,t){var r=Re(t.data),a={"!ref":"A1"},n=e[gr(r[2][0].data)],i=Ye(n[0].meta[1][0].data);if(i!=6001)throw new Error("6000 unexpected reference to ".concat(i));return Kg(e,n[0],a),a}function Yg(e,t){var r,a=Re(t.data),n={name:(r=a[1])!=null&&r[0]?dn(a[1][0].data):"",sheets:[]},i=Zn(a[2],gr);return i.forEach(function(s){e[s].forEach(function(f){var c=Ye(f.meta[1][0].data);c==6e3&&n.sheets.push(jg(e,f))})}),n}function Jg(e,t){var r=ai(),a=Re(t.data),n=Zn(a[1],gr);if(n.forEach(function(i){e[i].forEach(function(s){var f=Ye(s.meta[1][0].data);if(f==2){var c=Yg(e,s);c.sheets.forEach(function(o,l){ni(r,o,l==0?c.name:c.name+"_"+l,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function rn(e){var t,r,a,n,i={},s=[];if(e.FullPaths.forEach(function(c){if(c.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(c){if(c.name.match(/\.iwa$/)){var o;try{o=zr(c.content)}catch(u){return console.log("?? "+c.content.length+" "+(u.message||u))}var l;try{l=Xr(o)}catch(u){return console.log("## "+(u.message||u))}l.forEach(function(u){i[u.id]=u.messages,s.push(u.id)})}}),!s.length)throw new Error("File has no messages");var f=((n=(a=(r=(t=i==null?void 0:i[1])==null?void 0:t[0])==null?void 0:r.meta)==null?void 0:a[1])==null?void 0:n[0].data)&&Ye(i[1][0].meta[1][0].data)==1&&i[1][0];if(f||s.forEach(function(c){i[c].forEach(function(o){var l=Ye(o.meta[1][0].data)>>>0;if(l==1)if(!f)f=o;else throw new Error("Document has multiple roots")})}),!f)throw new Error("Cannot find Document root");return Jg(i,f)}function qg(e,t,r){var a,n,i,s;if(!((a=e[6])!=null&&a[0])||!((n=e[7])!=null&&n[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&Ye(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var c=0,o=Zt(e[7][0].data),l=0,u=[],h=Zt(e[4][0].data),d=0,p=[],x=0;x<t.length;++x){if(t[x]==null){o.setUint16(x*2,65535,!0),h.setUint16(x*2,65535);continue}o.setUint16(x*2,l,!0),h.setUint16(x*2,d,!0);var m,_;switch(typeof t[x]){case"string":m=Q0({t:"s",v:t[x]},r),_=en({t:"s",v:t[x]},r);break;case"number":m=Q0({t:"n",v:t[x]},r),_=en({t:"n",v:t[x]},r);break;case"boolean":m=Q0({t:"b",v:t[x]},r),_=en({t:"b",v:t[x]},r);break;default:throw new Error("Unsupported value "+t[x])}u.push(m),l+=m.length,p.push(_),d+=_.length,++c}for(e[2][0].data=Ne(c);x<e[7][0].data.length/2;++x)o.setUint16(x*2,65535,!0),h.setUint16(x*2,65535,!0);return e[6][0].data=Rt(u),e[3][0].data=Rt(p),c}function Zg(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=Rr(r["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(Ee(a)));var i=O0(r,{range:a,header:1}),s=["~Sh33tJ5~"];i.forEach(function(B){return B.forEach(function(b){typeof b=="string"&&s.push(b)})});var f={},c=[],o=me.read(t.numbers,{type:"base64"});o.FileIndex.map(function(B,b){return[B,o.FullPaths[b]]}).forEach(function(B){var b=B[0],N=B[1];if(b.type==2&&b.name.match(/\.iwa/)){var j=b.content,ae=zr(j),ne=Xr(ae);ne.forEach(function(re){c.push(re.id),f[re.id]={deps:[],location:N,type:Ye(re.messages[0].meta[1][0].data)}})}}),c.sort(function(B,b){return B-b});var l=c.filter(function(B){return B>1}).map(function(B){return[B,Ne(B)]});o.FileIndex.map(function(B,b){return[B,o.FullPaths[b]]}).forEach(function(B){var b=B[0];if(B[1],!!b.name.match(/\.iwa/)){var N=Xr(zr(b.content));N.forEach(function(j){j.messages.forEach(function(ae){l.forEach(function(ne){j.messages.some(function(re){return Ye(re.meta[1][0].data)!=11006&&Mg(re.data,ne[1])})&&f[ne[0]].deps.push(j.id)})})})}});for(var u=me.find(o,f[1].location),h=Xr(zr(u.content)),d,p=0;p<h.length;++p){var x=h[p];x.id==1&&(d=x)}var m=gr(Re(d.messages[0].data)[1][0].data);for(u=me.find(o,f[m].location),h=Xr(zr(u.content)),p=0;p<h.length;++p)x=h[p],x.id==m&&(d=x);for(m=gr(Re(d.messages[0].data)[2][0].data),u=me.find(o,f[m].location),h=Xr(zr(u.content)),p=0;p<h.length;++p)x=h[p],x.id==m&&(d=x);for(m=gr(Re(d.messages[0].data)[2][0].data),u=me.find(o,f[m].location),h=Xr(zr(u.content)),p=0;p<h.length;++p)x=h[p],x.id==m&&(d=x);var _=Re(d.messages[0].data);{_[6][0].data=Ne(a.e.r+1),_[7][0].data=Ne(a.e.c+1);var T=gr(_[46][0].data),g=me.find(o,f[T].location),I=Xr(zr(g.content));{for(var L=0;L<I.length&&I[L].id!=T;++L);if(I[L].id!=T)throw"Bad ColumnRowUIDMapArchive";var D=Re(I[L].messages[0].data);D[1]=[],D[2]=[],D[3]=[];for(var k=0;k<=a.e.c;++k){var R=[];R[1]=R[2]=[{type:0,data:Ne(k+420690)}],D[1].push({type:2,data:ur(R)}),D[2].push({type:0,data:Ne(k)}),D[3].push({type:0,data:Ne(k)})}D[4]=[],D[5]=[],D[6]=[];for(var O=0;O<=a.e.r;++O)R=[],R[1]=R[2]=[{type:0,data:Ne(O+726270)}],D[4].push({type:2,data:ur(R)}),D[5].push({type:0,data:Ne(O)}),D[6].push({type:0,data:Ne(O)});I[L].messages[0].data=ur(D)}g.content=fa(sa(I)),g.size=g.content.length,delete _[46];var X=Re(_[4][0].data);{X[7][0].data=Ne(a.e.r+1);var M=Re(X[1][0].data),P=gr(M[2][0].data);g=me.find(o,f[P].location),I=Xr(zr(g.content));{if(I[0].id!=P)throw"Bad HeaderStorageBucket";var J=Re(I[0].messages[0].data);for(O=0;O<i.length;++O){var le=Re(J[2][0].data);le[1][0].data=Ne(O),le[4][0].data=Ne(i[O].length),J[2][O]={type:J[2][0].type,data:ur(le)}}I[0].messages[0].data=ur(J)}g.content=fa(sa(I)),g.size=g.content.length;var Z=gr(X[2][0].data);g=me.find(o,f[Z].location),I=Xr(zr(g.content));{if(I[0].id!=Z)throw"Bad HeaderStorageBucket";for(J=Re(I[0].messages[0].data),k=0;k<=a.e.c;++k)le=Re(J[2][0].data),le[1][0].data=Ne(k),le[4][0].data=Ne(a.e.r+1),J[2][k]={type:J[2][0].type,data:ur(le)};I[0].messages[0].data=ur(J)}g.content=fa(sa(I)),g.size=g.content.length;var ue=gr(X[4][0].data);(function(){for(var B=me.find(o,f[ue].location),b=Xr(zr(B.content)),N,j=0;j<b.length;++j){var ae=b[j];ae.id==ue&&(N=ae)}var ne=Re(N.messages[0].data);{ne[3]=[];var re=[];s.forEach(function(A,Ue){re[1]=[{type:0,data:Ne(Ue)}],re[2]=[{type:0,data:Ne(1)}],re[3]=[{type:2,data:Bg(A)}],ne[3].push({type:2,data:ur(re)})})}N.messages[0].data=ur(ne);var Q=sa(b),Se=fa(Q);B.content=Se,B.size=B.content.length})();var ce=Re(X[3][0].data);{var Ie=ce[1][0];delete ce[2];var V=Re(Ie.data);{var pe=gr(V[2][0].data);(function(){for(var B=me.find(o,f[pe].location),b=Xr(zr(B.content)),N,j=0;j<b.length;++j){var ae=b[j];ae.id==pe&&(N=ae)}var ne=Re(N.messages[0].data);{delete ne[6],delete ce[7];var re=new Uint8Array(ne[5][0].data);ne[5]=[];for(var Q=0,Se=0;Se<=a.e.r;++Se){var A=Re(re);Q+=qg(A,i[Se],s),A[1][0].data=Ne(Se),ne[5].push({data:ur(A),type:2})}ne[1]=[{type:0,data:Ne(a.e.c+1)}],ne[2]=[{type:0,data:Ne(a.e.r+1)}],ne[3]=[{type:0,data:Ne(Q)}],ne[4]=[{type:0,data:Ne(a.e.r+1)}]}N.messages[0].data=ur(ne);var Ue=sa(b),De=fa(Ue);B.content=De,B.size=B.content.length})()}Ie.data=ur(V)}X[3][0].data=ur(ce)}_[4][0].data=ur(X)}d.messages[0].data=ur(_);var ve=sa(h),C=fa(ve);return u.content=C,u.size=u.content.length,o}function bc(e){return function(r){for(var a=0;a!=e.length;++a){var n=e[a];r[n[0]]===void 0&&(r[n[0]]=n[1]),n[2]==="n"&&(r[n[0]]=Number(r[n[0]]))}}}function Qn(e){bc([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function ei(e){bc([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Qg(e){return Ae.WS.indexOf(e)>-1?"sheet":e==Ae.CS?"chart":e==Ae.DS?"dialog":e==Ae.MS?"macro":e&&e.length?e:"sheet"}function e_(e,t){if(!e)return 0;try{e=t.map(function(a){return a.id||(a.id=a.strRelID),[a.name,e["!id"][a.id].Target,Qg(e["!id"][a.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function r_(e,t,r,a,n,i,s,f,c,o,l,u){try{i[a]=Na(Mr(e,r,!0),t);var h=ar(e,t),d;switch(f){case"sheet":d=T2(h,t,n,c,i[a],o,l,u);break;case"chart":if(d=S2(h,t,n,c,i[a],o,l,u),!d||!d["!drawel"])break;var p=Ia(d["!drawel"].Target,t),x=Xa(p),m=ld(Mr(e,p,!0),Na(Mr(e,x,!0),p)),_=Ia(m,p),T=Xa(_);d=qv(Mr(e,_,!0),_,c,Na(Mr(e,T,!0),_),o,d);break;case"macro":d=F2(h,t,n,c,i[a],o,l,u);break;case"dialog":d=y2(h,t,n,c,i[a],o,l,u);break;default:throw new Error("Unrecognized sheet type "+f)}s[a]=d;var g=[];i&&i[a]&&Je(i[a]).forEach(function(I){var L="";if(i[a][I].Type==Ae.CMNT){L=Ia(i[a][I].Target,t);var D=I2(ar(e,L,!0),L,c);if(!D||!D.length)return;rs(d,D,!1)}i[a][I].Type==Ae.TCMNT&&(L=Ia(i[a][I].Target,t),g=g.concat(hd(ar(e,L,!0),c)))}),g&&g.length&&rs(d,g,!0,c.people||[])}catch(I){if(c.WTF)throw I}}function Hr(e){return e.charAt(0)=="/"?e.slice(1):e}function t_(e,t){if(_a(),t=t||{},Qn(t),$r(e,"META-INF/manifest.xml")||$r(e,"objectdata.xml"))return hs(e,t);if($r(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof rn<"u"){if(e.FileIndex)return rn(e);var r=me.utils.cfb_new();return Ei(e).forEach(function(le){Te(r,le,zs(e,le))}),rn(r)}throw new Error("Unsupported NUMBERS file")}if(!$r(e,"[Content_Types].xml"))throw $r(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):$r(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var a=Ei(e),n=su(Mr(e,"[Content_Types].xml")),i=!1,s,f;if(n.workbooks.length===0&&(f="xl/workbook.xml",ar(e,f,!0)&&n.workbooks.push(f)),n.workbooks.length===0){if(f="xl/workbook.bin",!ar(e,f,!0))throw new Error("Could not find workbook");n.workbooks.push(f),i=!0}n.workbooks[0].slice(-3)=="bin"&&(i=!0);var c={},o={};if(!t.bookSheets&&!t.bookProps){if(Pa=[],n.sst)try{Pa=D2(ar(e,Hr(n.sst)),n.sst,t)}catch(le){if(t.WTF)throw le}t.cellStyles&&n.themes.length&&(c=C2(Mr(e,n.themes[0].replace(/^\//,""),!0)||"",n.themes[0],t)),n.style&&(o=A2(ar(e,Hr(n.style)),n.style,c,t))}n.links.map(function(le){try{var Z=Na(Mr(e,Xa(Hr(le))),le);return b2(ar(e,Hr(le)),Z,le,t)}catch{}});var l=E2(ar(e,Hr(n.workbooks[0])),n.workbooks[0],t),u={},h="";n.coreprops.length&&(h=ar(e,Hr(n.coreprops[0]),!0),h&&(u=wf(h)),n.extprops.length!==0&&(h=ar(e,Hr(n.extprops[0]),!0),h&&xu(h,u,t)));var d={};(!t.bookSheets||t.bookProps)&&n.custprops.length!==0&&(h=Mr(e,Hr(n.custprops[0]),!0),h&&(d=pu(h,t)));var p={};if((t.bookSheets||t.bookProps)&&(l.Sheets?s=l.Sheets.map(function(Z){return Z.name}):u.Worksheets&&u.SheetNames.length>0&&(s=u.SheetNames),t.bookProps&&(p.Props=u,p.Custprops=d),t.bookSheets&&typeof s<"u"&&(p.SheetNames=s),t.bookSheets?p.SheetNames:t.bookProps))return p;s={};var x={};t.bookDeps&&n.calcchain&&(x=O2(ar(e,Hr(n.calcchain)),n.calcchain));var m=0,_={},T,g;{var I=l.Sheets;u.Worksheets=I.length,u.SheetNames=[];for(var L=0;L!=I.length;++L)u.SheetNames[L]=I[L].name}var D=i?"bin":"xml",k=n.workbooks[0].lastIndexOf("/"),R=(n.workbooks[0].slice(0,k+1)+"_rels/"+n.workbooks[0].slice(k+1)+".rels").replace(/^\//,"");$r(e,R)||(R="xl/_rels/workbook."+D+".rels");var O=Na(Mr(e,R,!0),R.replace(/_rels.*/,"s5s"));(n.metadata||[]).length>=1&&(t.xlmeta=R2(ar(e,Hr(n.metadata[0])),n.metadata[0],t)),(n.people||[]).length>=1&&(t.people=dd(ar(e,Hr(n.people[0])),t)),O&&(O=e_(O,l.Sheets));var X=ar(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(m=0;m!=u.Worksheets;++m){var M="sheet";if(O&&O[m]?(T="xl/"+O[m][1].replace(/[\/]?xl\//,""),$r(e,T)||(T=O[m][1]),$r(e,T)||(T=R.replace(/_rels\/.*$/,"")+O[m][1]),M=O[m][2]):(T="xl/worksheets/sheet"+(m+1-X)+"."+D,T=T.replace(/sheet0\./,"sheet.")),g=T.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&t.sheets!=null)switch(typeof t.sheets){case"number":if(m!=t.sheets)continue e;break;case"string":if(u.SheetNames[m].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var P=!1,J=0;J!=t.sheets.length;++J)typeof t.sheets[J]=="number"&&t.sheets[J]==m&&(P=1),typeof t.sheets[J]=="string"&&t.sheets[J].toLowerCase()==u.SheetNames[m].toLowerCase()&&(P=1);if(!P)continue e}}r_(e,T,g,u.SheetNames[m],m,_,s,M,t,l,c,o)}return p={Directory:n,Workbook:l,Props:u,Custprops:d,Deps:x,Sheets:s,SheetNames:u.SheetNames,Strings:Pa,Styles:o,Themes:c,SSF:He(ge)},t&&t.bookFiles&&(e.files?(p.keys=a,p.files=e.files):(p.keys=[],p.files={},e.FullPaths.forEach(function(le,Z){le=le.replace(/^Root Entry[\/]/,""),p.keys.push(le),p.files[le]=e.FileIndex[Z]}))),t&&t.bookVBA&&(n.vba.length>0?p.vbaraw=ar(e,Hr(n.vba[0]),!0):n.defaults&&n.defaults.bin===Ed&&(p.vbaraw=ar(e,"xl/vbaProject.bin",!0))),p}function a_(e,t){var r=t||{},a="Workbook",n=me.find(e,a);try{if(a="/!DataSpaces/Version",n=me.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(R1(n.content),a="/!DataSpaces/DataSpaceMap",n=me.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=P1(n.content);if(i.length!==1||i[0].comps.length!==1||i[0].comps[0].t!==0||i[0].name!=="StrongEncryptionDataSpace"||i[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",n=me.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=L1(n.content);if(s.length!=1||s[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",n=me.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);M1(n.content)}catch{}if(a="/EncryptionInfo",n=me.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var f=U1(n.content);if(a="/EncryptedPackage",n=me.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(f[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(f[1],n.content,r.password||"",r);if(f[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(f[1],n.content,r.password||"",r);throw new Error("File is password-protected")}function n_(e,t){return t.bookType=="ods"?Oc(e,t):t.bookType=="numbers"?Zg(e,t):t.bookType=="xlsb"?i_(e,t):s_(e,t)}function i_(e,t){oa=1024,e&&!e.SSF&&(e.SSF=He(ge)),e&&e.SSF&&(_a(),R0(e.SSF),t.revssf=P0(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,La?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",a=ic.indexOf(t.bookType)>-1,n=Un();ei(t=t||{});var i=Fn(),s="",f=0;if(t.cellXfs=[],Lt(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Te(i,s,kf(e.Props,t)),n.coreprops.push(s),Pe(t.rels,2,s,Ae.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&c.push(e.SheetNames[o]);e.Props.SheetNames=c}for(e.Props.Worksheets=e.Props.SheetNames.length,Te(i,s,Sf(e.Props)),n.extprops.push(s),Pe(t.rels,3,s,Ae.EXT_PROPS),e.Custprops!==e.Props&&Je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Te(i,s,Ff(e.Custprops)),n.custprops.push(s),Pe(t.rels,4,s,Ae.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var l={"!id":{}},u=e.Sheets[e.SheetNames[f-1]],h=(u||{})["!type"]||"sheet";switch(h){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Te(i,s,P2(f-1,s,t,e,l)),n.sheets.push(s),Pe(t.wbrels,-1,"worksheets/sheet"+f+"."+r,Ae.WS[0])}if(u){var d=u["!comments"],p=!1,x="";d&&d.length>0&&(x="xl/comments"+f+"."+r,Te(i,x,M2(d,x)),n.comments.push(x),Pe(l,-1,"../comments"+f+"."+r,Ae.CMNT),p=!0),u["!legacy"]&&p&&Te(i,"xl/drawings/vmlDrawing"+f+".vml",ac(f,u["!comments"])),delete u["!comments"],delete u["!legacy"]}l["!id"].rId1&&Te(i,Xa(s),ha(l))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Te(i,s,B2(t.Strings,s,t)),n.strs.push(s),Pe(t.wbrels,-1,"sharedStrings."+r,Ae.SST)),s="xl/workbook."+r,Te(i,s,N2(e,s)),n.workbooks.push(s),Pe(t.rels,1,s,Ae.WB),s="xl/theme/theme1.xml",Te(i,s,zn(e.Themes,t)),n.themes.push(s),Pe(t.wbrels,-1,"theme/theme1.xml",Ae.THEME),s="xl/styles."+r,Te(i,s,L2(e,s,t)),n.styles.push(s),Pe(t.wbrels,-1,"styles."+r,Ae.STY),e.vbaraw&&a&&(s="xl/vbaProject.bin",Te(i,s,e.vbaraw),n.vba.push(s),Pe(t.wbrels,-1,"vbaProject.bin",Ae.VBA)),s="xl/metadata."+r,Te(i,s,U2(s)),n.metadata.push(s),Pe(t.wbrels,-1,"metadata."+r,Ae.XLMETA),Te(i,"[Content_Types].xml",gf(n,t)),Te(i,"_rels/.rels",ha(t.rels)),Te(i,"xl/_rels/workbook."+r+".rels",ha(t.wbrels)),delete t.revssf,delete t.ssf,i}function s_(e,t){oa=1024,e&&!e.SSF&&(e.SSF=He(ge)),e&&e.SSF&&(_a(),R0(e.SSF),t.revssf=P0(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,La?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=ic.indexOf(t.bookType)>-1,n=Un();ei(t=t||{});var i=Fn(),s="",f=0;if(t.cellXfs=[],Lt(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Te(i,s,kf(e.Props,t)),n.coreprops.push(s),Pe(t.rels,2,s,Ae.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&c.push(e.SheetNames[o]);e.Props.SheetNames=c}e.Props.Worksheets=e.Props.SheetNames.length,Te(i,s,Sf(e.Props)),n.extprops.push(s),Pe(t.rels,3,s,Ae.EXT_PROPS),e.Custprops!==e.Props&&Je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Te(i,s,Ff(e.Custprops)),n.custprops.push(s),Pe(t.rels,4,s,Ae.CUST_PROPS));var l=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var u={"!id":{}},h=e.Sheets[e.SheetNames[f-1]],d=(h||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,Te(i,s,mc(f-1,t,e,u)),n.sheets.push(s),Pe(t.wbrels,-1,"worksheets/sheet"+f+"."+r,Ae.WS[0])}if(h){var p=h["!comments"],x=!1,m="";if(p&&p.length>0){var _=!1;p.forEach(function(T){T[1].forEach(function(g){g.T==!0&&(_=!0)})}),_&&(m="xl/threadedComments/threadedComment"+f+"."+r,Te(i,m,xd(p,l,t)),n.threadedcomments.push(m),Pe(u,-1,"../threadedComments/threadedComment"+f+"."+r,Ae.TCMNT)),m="xl/comments"+f+"."+r,Te(i,m,nc(p)),n.comments.push(m),Pe(u,-1,"../comments"+f+"."+r,Ae.CMNT),x=!0}h["!legacy"]&&x&&Te(i,"xl/drawings/vmlDrawing"+f+".vml",ac(f,h["!comments"])),delete h["!comments"],delete h["!legacy"]}u["!id"].rId1&&Te(i,Xa(s),ha(u))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Te(i,s,zf(t.Strings,t)),n.strs.push(s),Pe(t.wbrels,-1,"sharedStrings."+r,Ae.SST)),s="xl/workbook."+r,Te(i,s,kc(e)),n.workbooks.push(s),Pe(t.rels,1,s,Ae.WB),s="xl/theme/theme1.xml",Te(i,s,zn(e.Themes,t)),n.themes.push(s),Pe(t.wbrels,-1,"theme/theme1.xml",Ae.THEME),s="xl/styles."+r,Te(i,s,Qf(e,t)),n.styles.push(s),Pe(t.wbrels,-1,"styles."+r,Ae.STY),e.vbaraw&&a&&(s="xl/vbaProject.bin",Te(i,s,e.vbaraw),n.vba.push(s),Pe(t.wbrels,-1,"vbaProject.bin",Ae.VBA)),s="xl/metadata."+r,Te(i,s,tc()),n.metadata.push(s),Pe(t.wbrels,-1,"metadata."+r,Ae.XLMETA),l.length>1&&(s="xl/persons/person.xml",Te(i,s,pd(l)),n.people.push(s),Pe(t.wbrels,-1,"persons/person.xml",Ae.PEOPLE)),Te(i,"[Content_Types].xml",gf(n,t)),Te(i,"_rels/.rels",ha(t.rels)),Te(i,"xl/_rels/workbook."+r+".rels",ha(t.wbrels)),delete t.revssf,delete t.ssf,i}function ri(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Ur(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function f_(e,t){return me.find(e,"EncryptedPackage")?a_(e,t):Fc(e,t)}function c_(e,t){var r,a=e,n=t||{};return n.type||(n.type=Fe&&Buffer.isBuffer(e)?"buffer":"base64"),r=$s(a,n),t_(r,n)}function Rc(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return hn(e.slice(r),t);default:break e}return ma.to_workbook(e,t)}function o_(e,t){var r="",a=ri(e,t);switch(t.type){case"base64":r=Ur(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=jt(e);break;default:throw new Error("Unrecognized type "+t.type)}return a[0]==239&&a[1]==187&&a[2]==191&&(r=Be(r)),t.type="binary",Rc(r,t)}function l_(e,t){var r=e;return t.type=="base64"&&(r=Ur(r)),r=yt.utils.decode(1200,r.slice(2),"str"),t.type="binary",Rc(r,t)}function u_(e){return e.match(/[^\x00-\x7F]/)?ct(e):e}function tn(e,t,r,a){return a?(r.type="string",ma.to_workbook(e,r)):ma.to_workbook(t,r)}function pn(e,t){wn();var r=t||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return pn(new Uint8Array(e),(r=He(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var a=e,n=[0,0,0,0],i=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),da={},r.dateNF&&(da.dateNF=r.dateNF),r.type||(r.type=Fe&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=Fe?"buffer":"binary",a=sl(e),typeof Uint8Array<"u"&&!Fe&&(r.type="array")),r.type=="string"&&(i=!0,r.type="binary",r.codepage=65001,a=u_(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var s=new ArrayBuffer(3),f=new Uint8Array(s);if(f.foo="bar",!f.foo)return r=He(r),r.type="array",pn(kn(a),r)}switch((n=ri(a,r))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return f_(me.read(a,r),r);break;case 9:if(n[1]<=8)return Fc(a,r);break;case 60:return hn(a,r);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return m1(a,r);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return Gf.to_workbook(a,r);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?c_(a,r):tn(e,a,r,i);case 239:return n[3]===60?hn(a,r):tn(e,a,r,i);case 255:if(n[1]===254)return l_(a,r);if(n[1]===0&&n[2]===2&&n[3]===0)return zt.to_workbook(a,r);break;case 0:if(n[1]===0&&(n[2]>=2&&n[3]===0||n[2]===0&&(n[3]===8||n[3]===9)))return zt.to_workbook(a,r);break;case 3:case 131:case 139:case 140:return un.to_workbook(a,r);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return Jf.to_workbook(a,r);break;case 10:case 13:case 32:return o_(a,r);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return p1.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?un.to_workbook(a,r):tn(e,a,r,i)}function Nc(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Za(t.file,me.write(e,{type:Fe?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return me.write(e,t)}function h_(e,t){var r=He(t||{}),a=n_(e,r);return x_(a,r)}function x_(e,t){var r={},a=Fe?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?me.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof n=="string"){if(t.type=="binary"||t.type=="base64")return n;n=new Uint8Array(b0(n))}return t.password&&typeof encrypt_agile<"u"?Nc(encrypt_agile(n,t.password),t):t.type==="file"?Za(t.file,n):t.type=="string"?Be(n):n}function d_(e,t){var r=t||{},a=ug(e,r);return Nc(a,r)}function ft(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return Ua(ct(a));case"binary":return ct(a);case"string":return e;case"file":return Za(t.file,a,"utf8");case"buffer":return Fe?wt(a,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(a):ft(a,{type:"binary"}).split("").map(function(n){return n.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function p_(e,t){switch(t.type){case"base64":return Ua(e);case"binary":return e;case"string":return e;case"file":return Za(t.file,e,"binary");case"buffer":return Fe?wt(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function v0(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return t.type=="base64"?Ua(r):t.type=="string"?Be(r):r;case"file":return Za(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Pc(e,t){wn(),f2(e);var r=He(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var a=Pc(e,r);return r.type="array",b0(a)}var n=0;if(r.sheet&&(typeof r.sheet=="number"?n=r.sheet:n=e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return ft(ng(e,r),r);case"slk":case"sylk":return ft(Hf.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"htm":case"html":return ft(Ac(e.Sheets[e.SheetNames[n]],r),r);case"txt":return p_(Lc(e.Sheets[e.SheetNames[n]],r),r);case"csv":return ft(ti(e.Sheets[e.SheetNames[n]],r),r,"\uFEFF");case"dif":return ft(Gf.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return v0(un.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return ft(ma.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return ft(Jf.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"eth":return ft(Xf.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return ft(Oc(e,r),r);case"wk1":return v0(zt.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return v0(zt.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),v0(yc(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),d_(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return h_(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function m_(e){if(!e.bookType){var t={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"},r=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();r.match(/^\.[a-z]+$/)&&(e.bookType=r.slice(1)),e.bookType=t[e.bookType]||e.bookType}}function v_(e,t,r){var a=r||{};return a.type="file",a.file=t,m_(a),Pc(e,a)}function g_(e,t,r,a,n,i,s,f){var c=er(r),o=f.defval,l=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),u=!0,h=n===1?[]:{};if(n!==1)if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:r,enumerable:!1})}catch{h.__rowNum__=r}else h.__rowNum__=r;if(!s||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=s?e[r][d]:e[a[d]+c];if(p===void 0||p.t===void 0){if(o===void 0)continue;i[d]!=null&&(h[i[d]]=o);continue}var x=p.v;switch(p.t){case"z":if(x==null)break;continue;case"e":x=x==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(i[d]!=null){if(x==null)if(p.t=="e"&&x===null)h[i[d]]=null;else if(o!==void 0)h[i[d]]=o;else if(l&&x===null)h[i[d]]=null;else continue;else h[i[d]]=l&&(p.t!=="n"||p.t==="n"&&f.rawNumbers!==!1)?x:_t(p,x,f);x!=null&&(u=!1)}}return{row:h,isempty:u}}function O0(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},a=0,n=1,i=[],s=0,f="",c={s:{r:0,c:0},e:{r:0,c:0}},o=t||{},l=o.range!=null?o.range:e["!ref"];switch(o.header===1?a=1:o.header==="A"?a=2:Array.isArray(o.header)?a=3:o.header==null&&(a=0),typeof l){case"string":c=be(l);break;case"number":c=be(e["!ref"]),c.s.r=l;break;default:c=l}a>0&&(n=0);var u=er(c.s.r),h=[],d=[],p=0,x=0,m=Array.isArray(e),_=c.s.r,T=0,g={};m&&!e[_]&&(e[_]=[]);var I=o.skipHidden&&e["!cols"]||[],L=o.skipHidden&&e["!rows"]||[];for(T=c.s.c;T<=c.e.c;++T)if(!(I[T]||{}).hidden)switch(h[T]=$e(T),r=m?e[_][T]:e[h[T]+u],a){case 1:i[T]=T-c.s.c;break;case 2:i[T]=h[T];break;case 3:i[T]=o.header[T-c.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=_t(r,null,o),x=g[s]||0,!x)g[s]=1;else{do f=s+"_"+x++;while(g[f]);g[s]=x,g[f]=1}i[T]=f}for(_=c.s.r+n;_<=c.e.r;++_)if(!(L[_]||{}).hidden){var D=g_(e,c,_,h,a,i,m,o);(D.isempty===!1||(a===1?o.blankrows!==!1:o.blankrows))&&(d[p++]=D.row)}return d.length=p,d}var vs=/"/g;function __(e,t,r,a,n,i,s,f){for(var c=!0,o=[],l="",u=er(r),h=t.s.c;h<=t.e.c;++h)if(a[h]){var d=f.dense?(e[r]||[])[h]:e[a[h]+u];if(d==null)l="";else if(d.v!=null){c=!1,l=""+(f.rawNumbers&&d.t=="n"?d.v:_t(d,null,f));for(var p=0,x=0;p!==l.length;++p)if((x=l.charCodeAt(p))===n||x===i||x===34||f.forceQuotes){l='"'+l.replace(vs,'""')+'"';break}l=="ID"&&(l='"ID"')}else d.f!=null&&!d.F?(c=!1,l="="+d.f,l.indexOf(",")>=0&&(l='"'+l.replace(vs,'""')+'"')):l="";o.push(l)}return f.blankrows===!1&&c?null:o.join(s)}function ti(e,t){var r=[],a=t??{};if(e==null||e["!ref"]==null)return"";var n=be(e["!ref"]),i=a.FS!==void 0?a.FS:",",s=i.charCodeAt(0),f=a.RS!==void 0?a.RS:`
`,c=f.charCodeAt(0),o=new RegExp((i=="|"?"\\|":i)+"+$"),l="",u=[];a.dense=Array.isArray(e);for(var h=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(h[p]||{}).hidden||(u[p]=$e(p));for(var x=0,m=n.s.r;m<=n.e.r;++m)(d[m]||{}).hidden||(l=__(e,n,m,u,s,c,i,a),l!=null&&(a.strip&&(l=l.replace(o,"")),(l||a.blankrows!==!1)&&r.push((x++?f:"")+l)));return delete a.dense,r.join("")}function Lc(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=ti(e,t);return r}function w_(e){var t="",r,a="";if(e==null||e["!ref"]==null)return[];var n=be(e["!ref"]),i="",s=[],f,c=[],o=Array.isArray(e);for(f=n.s.c;f<=n.e.c;++f)s[f]=$e(f);for(var l=n.s.r;l<=n.e.r;++l)for(i=er(l),f=n.s.c;f<=n.e.c;++f)if(t=s[f]+i,r=o?(e[l]||[])[f]:e[t],a="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;a=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)a=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)a=""+r.v;else if(r.t=="b")a=r.v?"TRUE":"FALSE";else if(r.w!==void 0)a="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?a="'"+r.v:a=""+r.v}}c[c.length]=t+"="+a}return c}function Bc(e,t,r){var a=r||{},n=+!a.skipHeader,i=e||{},s=0,f=0;if(i&&a.origin!=null)if(typeof a.origin=="number")s=a.origin;else{var c=typeof a.origin=="string"?Ke(a.origin):a.origin;s=c.r,f=c.c}var o,l={s:{c:0,r:0},e:{c:f,r:s+t.length-1+n}};if(i["!ref"]){var u=be(i["!ref"]);l.e.c=Math.max(l.e.c,u.e.c),l.e.r=Math.max(l.e.r,u.e.r),s==-1&&(s=u.e.r+1,l.e.r=s+t.length-1+n)}else s==-1&&(s=0,l.e.r=t.length-1+n);var h=a.header||[],d=0;t.forEach(function(x,m){Je(x).forEach(function(_){(d=h.indexOf(_))==-1&&(h[d=h.length]=_);var T=x[_],g="z",I="",L=_e({c:f+d,r:s+m+n});o=qa(i,L),T&&typeof T=="object"&&!(T instanceof Date)?i[L]=T:(typeof T=="number"?g="n":typeof T=="boolean"?g="b":typeof T=="string"?g="s":T instanceof Date?(g="d",a.cellDates||(g="n",T=fr(T)),I=a.dateNF||ge[14]):T===null&&a.nullError&&(g="e",T=0),o?(o.t=g,o.v=T,delete o.w,delete o.R,I&&(o.z=I)):i[L]=o={t:g,v:T},I&&(o.z=I))})}),l.e.c=Math.max(l.e.c,f+h.length-1);var p=er(s);if(n)for(d=0;d<h.length;++d)i[$e(d+f)+p]={t:"s",v:h[d]};return i["!ref"]=Ee(l),i}function k_(e,t){return Bc(null,e,t)}function qa(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var a=Ke(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?qa(e,_e(t)):qa(e,_e({r:t,c:r||0}))}function E_(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function ai(){return{SheetNames:[],Sheets:{}}}function ni(e,t,r,a){var n=1;if(!r)for(;n<=65535&&e.SheetNames.indexOf(r="Sheet"+n)!=-1;++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);n=i&&+i[2]||0;var s=i&&i[1]||r;for(++n;n<=65535&&e.SheetNames.indexOf(r=s+n)!=-1;++n);}if(wc(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function T_(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=E_(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r}function S_(e,t){return e.z=t,e}function Mc(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function F_(e,t,r){return Mc(e,"#"+t,r)}function y_(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function A_(e,t,r,a){for(var n=typeof t!="string"?t:be(t),i=typeof t=="string"?t:Ee(t),s=n.s.r;s<=n.e.r;++s)for(var f=n.s.c;f<=n.e.c;++f){var c=qa(e,s,f);c.t="n",c.F=i,delete c.v,s==n.s.r&&f==n.s.c&&(c.f=r,a&&(c.D=!0))}return e}var g0={encode_col:$e,encode_row:er,encode_cell:_e,encode_range:Ee,decode_col:Rn,decode_row:bn,split_cell:Bl,decode_cell:Ke,decode_range:Rr,format_cell:_t,sheet_add_aoa:uf,sheet_add_json:Bc,sheet_add_dom:Cc,aoa_to_sheet:wa,json_to_sheet:k_,table_to_sheet:Dc,table_to_book:Rg,sheet_to_csv:ti,sheet_to_txt:Lc,sheet_to_json:O0,sheet_to_html:Ac,sheet_to_formulae:w_,sheet_to_row_object_array:O0,sheet_get_cell:qa,book_new:ai,book_append_sheet:ni,book_set_sheet_visibility:T_,cell_set_number_format:S_,cell_set_hyperlink:Mc,cell_set_internal_link:F_,cell_add_comment:y_,sheet_set_array_formula:A_,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};const C_={class:"import-container"},D_={key:0,class:"step-content"},I_={class:"upload-section"},O_={class:"format-info"},b_={class:"format-header"},R_={class:"field-list"},N_={class:"note"},P_={key:1,class:"step-content"},L_={class:"preview-info"},B_={key:0,class:"preview-table"},M_={key:0,class:"preview-note"},U_={key:2,class:"step-content"},W_={class:"result-summary"},V_={class:"result-stats"},H_={key:0,class:"failure-details"},G_={class:"dialog-footer"},X_={__name:"BatchImportDialog",props:{visible:Boolean},emits:["update:visible","success"],setup(e,{emit:t}){const r=t,a=hr(0),n=hr(null),i=hr([]),s=hr(!1),f=hr(!1),c=hr([]),o=ws({success:!1,message:"",successCount:0,failureCount:0,failures:[]}),l=["姓名","用户名","密码","职位","部门ID","公司电话","公司名称","地址","手机","传真","邮编","邮箱"],u={姓名:"name",用户名:"username",密码:"password",职位:"position",部门ID:"departmentId",公司电话:"phone",公司名称:"workLocation",地址:"address",手机:"avatar",传真:"employeeId",邮编:"manager",邮箱:"email"},h=D=>{const k=D.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||D.type==="application/vnd.ms-excel",R=D.size/1024/1024<10;return k?(R||Ir.error("文件大小不能超过10MB!"),!1):(Ir.error("只能上传Excel文件!"),!1)},d=D=>{n.value=D.raw,i.value=[D]},p=async()=>{if(!n.value){Ir.error("请先选择文件");return}s.value=!0;try{const D=await x(n.value);D&&D.length>0?(c.value=D,a.value=1,Ir.success(`成功解析 ${D.length} 条数据`)):Ir.error("文件中没有找到有效数据")}catch(D){console.error("解析Excel文件失败:",D),Ir.error("解析文件失败: "+D.message)}finally{s.value=!1}},x=D=>new Promise((k,R)=>{const O=new FileReader;O.onload=X=>{try{const M=new Uint8Array(X.target.result),P=pn(M,{type:"array"}),J=P.SheetNames[0],le=P.Sheets[J],Z=g0.sheet_to_json(le,{header:1});if(Z.length<2){R(new Error("Excel文件至少需要包含表头和一行数据"));return}const ue=Z[0];if(!m(ue,l)){R(new Error("Excel表头格式不正确，请检查字段名称和顺序"));return}const Ie=[];for(let V=1;V<Z.length;V++){const pe=Z[V];if(pe.some(ve=>ve!==void 0&&ve!=="")){const ve=_(pe,ue);ve&&Ie.push(ve)}}k(Ie)}catch(M){R(M)}},O.onerror=()=>R(new Error("读取文件失败")),O.readAsArrayBuffer(D)}),m=(D,k)=>{if(D.length<k.length)return!1;for(let R=0;R<k.length;R++)if(D[R]!==k[R])return!1;return!0},_=(D,k)=>{const R={};return k.forEach((O,X)=>{const M=u[O];if(M&&D[X]!==void 0){let P=D[X];M==="departmentId"?P=P?parseInt(P):null:M==="password"?P=P||"123456":P=P?String(P).trim():"",R[M]=P}}),!R.name||!R.username?null:R},T=async()=>{var D,k,R,O,X;if(c.value.length===0){Ir.error("没有可导入的数据");return}f.value=!0;try{const M=await Dt.post("/employees/batch-import",{employees:c.value});o.success=!0,o.message=M.message||"批量导入完成",o.successCount=((D=M.data)==null?void 0:D.successCount)||0,o.failureCount=((k=M.data)==null?void 0:k.failureCount)||0,o.failures=((R=M.data)==null?void 0:R.failures)||[],a.value=2,o.failureCount===0?Ir.success("所有数据导入成功!"):Ir.warning(`导入完成，成功${o.successCount}条，失败${o.failureCount}条`)}catch(M){console.error("批量导入失败:",M),o.success=!1,o.message=((X=(O=M.response)==null?void 0:O.data)==null?void 0:X.message)||"导入失败",o.successCount=0,o.failureCount=c.value.length,o.failures=[],a.value=2,Ir.error("批量导入失败: "+o.message)}finally{f.value=!1}},g=()=>{a.value===2&&o.success?r("success"):r("update:visible",!1),I()},I=()=>{a.value=0,n.value=null,i.value=[],c.value=[],Object.assign(o,{success:!1,message:"",successCount:0,failureCount:0,failures:[]})},L=()=>{const D=[l,["张三","zhangsan","123456","软件工程师","1","010-12345678","北京分公司","北京市朝阳区","13800138000","010-87654321","100000","<EMAIL>"],["李四","lisi","","产品经理","2","021-12345678","上海分公司","上海市浦东新区","13900139000","021-87654321","200000","<EMAIL>"]],k=g0.aoa_to_sheet(D),R=g0.book_new();g0.book_append_sheet(R,k,"员工导入模板");const O=[{wch:10},{wch:15},{wch:10},{wch:15},{wch:10},{wch:15},{wch:15},{wch:20},{wch:15},{wch:15},{wch:10},{wch:20}];k["!cols"]=O,v_(R,"员工批量导入模板.xlsx"),Ir.success("模板下载成功")};return(D,k)=>{const R=lo,O=uo,X=Fs,M=ho,P=vn,J=xo,le=po,Z=ys,ue=As,ce=mo,Ie=vo,V=Ss;return vr(),dt(V,{"model-value":e.visible,"onUpdate:modelValue":k[1]||(k[1]=pe=>D.$emit("update:visible",pe)),title:"批量导入员工",width:"800px","close-on-click-modal":!1},{footer:de(()=>[Ge("div",G_,[q(P,{onClick:g},{default:de(()=>[yr(Wt(a.value===2?"关闭":"取消"),1)]),_:1}),a.value===0?(vr(),dt(P,{key:0,type:"primary",disabled:!n.value,onClick:p,loading:s.value},{default:de(()=>[yr(Wt(s.value?"解析中...":"解析文件"),1)]),_:1},8,["disabled","loading"])):it("",!0),a.value===1?(vr(),dt(P,{key:1,onClick:k[0]||(k[0]=pe=>a.value=0)},{default:de(()=>k[9]||(k[9]=[yr(" 上一步 ")])),_:1,__:[9]})):it("",!0),a.value===1?(vr(),dt(P,{key:2,type:"primary",disabled:c.value.length===0,onClick:T,loading:f.value},{default:de(()=>[yr(Wt(f.value?"导入中...":"开始导入"),1)]),_:1},8,["disabled","loading"])):it("",!0)])]),default:de(()=>[Ge("div",C_,[q(O,{active:a.value,"finish-status":"success","align-center":""},{default:de(()=>[q(R,{title:"选择文件"}),q(R,{title:"数据预览"}),q(R,{title:"导入结果"})]),_:1},8,["active"]),a.value===0?(vr(),Ut("div",D_,[Ge("div",I_,[q(M,{ref:"uploadRef",class:"upload-demo",drag:"","auto-upload":!1,"on-change":d,"before-upload":h,accept:".xlsx,.xls",limit:1,"file-list":i.value},{tip:de(()=>k[2]||(k[2]=[Ge("div",{class:"el-upload__tip"}," 只能上传 .xlsx/.xls 文件，且不超过 10MB ",-1)])),default:de(()=>[q(X,{class:"el-icon--upload"},{default:de(()=>[q(la(io))]),_:1}),k[3]||(k[3]=Ge("div",{class:"el-upload__text"},[yr(" 将Excel文件拖到此处，或"),Ge("em",null,"点击上传")],-1))]),_:1,__:[3]},8,["file-list"])]),Ge("div",O_,[Ge("div",b_,[k[5]||(k[5]=Ge("h4",null,"Excel文件格式要求：",-1)),q(P,{type:"primary",size:"small",onClick:L},{default:de(()=>[q(X,null,{default:de(()=>[q(la(so))]),_:1}),k[4]||(k[4]=yr(" 下载模板 "))]),_:1,__:[4]})]),k[7]||(k[7]=Ge("p",null,"第一行为表头，包含以下字段（顺序必须一致）：",-1)),Ge("div",R_,[(vr(),Ut(fo,null,co(l,pe=>q(J,{key:pe,type:"info",class:"field-tag"},{default:de(()=>[yr(Wt(pe),1)]),_:2},1024)),64))]),Ge("p",N_,[q(X,null,{default:de(()=>[q(la(oo))]),_:1}),k[6]||(k[6]=yr(' 注意：密码字段可以为空，系统将自动设置为默认密码"123456" '))])])])):it("",!0),a.value===1?(vr(),Ut("div",P_,[Ge("div",L_,[q(le,{title:`共解析到 ${c.value.length} 条员工数据`,type:"info",closable:!1,"show-icon":""},null,8,["title"])]),c.value.length>0?(vr(),Ut("div",B_,[q(ue,{data:c.value.slice(0,10),style:{width:"100%"},"max-height":"400",border:""},{default:de(()=>[q(Z,{prop:"name",label:"姓名",width:"80"}),q(Z,{prop:"username",label:"用户名",width:"100"}),q(Z,{prop:"position",label:"职位",width:"100"}),q(Z,{prop:"departmentId",label:"部门ID",width:"80"}),q(Z,{prop:"phone",label:"公司电话",width:"120"}),q(Z,{prop:"avatar",label:"手机",width:"120"}),q(Z,{prop:"email",label:"邮箱",width:"150"})]),_:1},8,["data"]),c.value.length>10?(vr(),Ut("p",M_," 仅显示前10条数据预览，实际将导入 "+Wt(c.value.length)+" 条数据 ",1)):it("",!0)])):it("",!0)])):it("",!0),a.value===2?(vr(),Ut("div",U_,[Ge("div",W_,[q(Ie,{icon:o.success?"success":"error",title:o.success?"导入完成":"导入失败","sub-title":o.message},{extra:de(()=>[Ge("div",V_,[q(ce,{title:"成功导入",value:o.successCount,suffix:"条",class:"stat-item success"},null,8,["value"]),q(ce,{title:"导入失败",value:o.failureCount,suffix:"条",class:"stat-item error"},null,8,["value"])])]),_:1},8,["icon","title","sub-title"])]),o.failures&&o.failures.length>0?(vr(),Ut("div",H_,[k[8]||(k[8]=Ge("h4",null,"失败详情：",-1)),q(ue,{data:o.failures,style:{width:"100%"},"max-height":"300",border:""},{default:de(()=>[q(Z,{prop:"row",label:"行号",width:"80"}),q(Z,{prop:"name",label:"姓名",width:"100"}),q(Z,{prop:"username",label:"用户名",width:"120"}),q(Z,{prop:"error",label:"失败原因"})]),_:1},8,["data"])])):it("",!0)])):it("",!0)])]),_:1},8,["model-value"])}}},z_=mn(X_,[["__scopeId","data-v-015262db"]]);const $_={class:"employee-management"},K_={class:"content-card"},j_={class:"table-toolbar"},Y_={class:"toolbar-right"},J_={class:"filter-bar"},q_={__name:"EmployeeManagement",setup(e){const t=hr(!1),r=hr([]),a=hr(!1),n=hr(null),i=hr(!1),s=hr(!1),f=hr({keyword:""}),c=async()=>{t.value=!0;try{const _={};f.value.keyword&&(_.keyword=f.value.keyword);const g=(await Dt.get("/employees",{params:_})).data||[];for(const I of g)if(I.departmentId)try{const L=await Dt.get(`/departments/${I.departmentId}`);I.departmentPath=o(L.data)}catch(L){console.error("获取部门路径失败:",L),I.departmentPath=I.department}r.value=g}catch(_){console.error("加载员工列表失败:",_)}finally{t.value=!1}},o=_=>_?(_.path,_.name):"",l=()=>{c()},u=()=>{n.value=null,i.value=!1,a.value=!0},h=_=>{n.value={..._},i.value=!0,a.value=!0},d=()=>{a.value=!1,c()},p=()=>{s.value=!0},x=()=>{s.value=!1,c()},m=async _=>{try{await _o.confirm(`确定要删除员工 ${_.name} 吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await Dt.delete(`/employees/${_.id}`),Ir.success("删除成功"),c()}catch(T){T!=="cancel"&&console.error("删除员工失败:",T)}};return _s(()=>{c()}),(_,T)=>{const g=Fs,I=vn,L=ks,D=Es,k=Ts,R=ys,O=As,X=wo;return vr(),dt(eo,null,{default:de(()=>[Ge("div",$_,[Ge("div",K_,[Ge("div",j_,[T[5]||(T[5]=Ge("div",{class:"toolbar-left"},[Ge("h3",null,"员工管理")],-1)),Ge("div",Y_,[q(I,{type:"success",onClick:p},{default:de(()=>[q(g,null,{default:de(()=>[q(la(ko))]),_:1}),T[3]||(T[3]=yr(" 批量导入 "))]),_:1,__:[3]}),q(I,{type:"primary",onClick:u},{default:de(()=>[q(g,null,{default:de(()=>[q(la(Eo))]),_:1}),T[4]||(T[4]=yr(" 新增员工 "))]),_:1,__:[4]})])]),Ge("div",J_,[q(k,{gutter:20},{default:de(()=>[q(D,{span:6},{default:de(()=>[q(L,{modelValue:f.value.keyword,"onUpdate:modelValue":T[0]||(T[0]=M=>f.value.keyword=M),placeholder:"搜索姓名/用户名/传真",clearable:"",onInput:l},{prefix:de(()=>[q(g,null,{default:de(()=>[q(la(To))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),go((vr(),dt(O,{data:r.value,stripe:"",style:{width:"100%"}},{default:de(()=>[q(R,{prop:"employeeId",label:"传真",width:"120"}),q(R,{prop:"name",label:"姓名",width:"100"}),q(R,{prop:"departmentPath",label:"部门",width:"200"},{default:de(({row:M})=>[yr(Wt(M.departmentPath||M.department),1)]),_:1}),q(R,{prop:"position",label:"职位",width:"120"}),q(R,{prop:"email",label:"邮箱",width:"180"}),q(R,{prop:"phone",label:"电话",width:"130"}),q(R,{prop:"avatar",label:"移动电话",width:"130"}),q(R,{prop:"manager",label:"邮政编码",width:"100"}),q(R,{prop:"workLocation",label:"工作地址",width:"150"}),q(R,{prop:"address",label:"详细地址",width:"200"}),q(R,{label:"操作",width:"150",fixed:"right"},{default:de(({row:M})=>[q(I,{type:"primary",size:"small",onClick:P=>h(M)},{default:de(()=>T[6]||(T[6]=[yr(" 编辑 ")])),_:2,__:[6]},1032,["onClick"]),q(I,{type:"danger",size:"small",onClick:P=>m(M)},{default:de(()=>T[7]||(T[7]=[yr(" 删除 ")])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,t.value]])]),q(Ao,{visible:a.value,"onUpdate:visible":T[1]||(T[1]=M=>a.value=M),employee:n.value,"is-edit":i.value,onSuccess:d},null,8,["visible","employee","is-edit"]),q(z_,{visible:s.value,"onUpdate:visible":T[2]||(T[2]=M=>s.value=M),onSuccess:x},null,8,["visible"])])]),_:1})}}},sw=mn(q_,[["__scopeId","data-v-8242d999"]]);export{sw as default};
