import{_ as B}from"./_plugin-vue_export-helper-063e7853.js";/* empty css                   *//* empty css                     */import{L as q}from"./Layout-97877336.js";import{I as R}from"./ImageUpload-90a0755f.js";import{r as n,b as $,q as j,o as _,s as f,w as a,v as U,d as i,X as z,e,i as m,t as v,H as A,j as I,T as F,a5 as O,R as S,m as H,U as X,l as Z,E as G,I as J,Z as K}from"./index-fbf0bc73.js";/* empty css                    */const P={class:"long-image-management"},Q={class:"content-card"},W={class:"current-image-preview"},Y={class:"dialog-footer"},ee={__name:"LongImageManagement",setup(ae){const u=n(!1),c=n(!1),b=n([]),d=n(!1),x=n(!1),g=n(),s=$({imageUrl:""}),E={imageUrl:[{required:!0,message:"请上传图片文件",trigger:"change"}]},w=async()=>{u.value=!0;try{const t=await U.get("/long-images");b.value=t.data||[]}catch(t){console.error("加载长图列表失败:",t)}finally{u.value=!1}},V=t=>t?new Date(t).toLocaleString("zh-CN"):"",k=t=>{Object.assign(s,{imageUrl:t.imageUrl||""}),s.currentImageId=t.id,x.value=!0,d.value=!0},L=async()=>{if(g.value)try{await g.value.validate(),c.value=!0,s.currentImageId&&(await U.put(`/long-images/${s.currentImageId}`,{imageUrl:s.imageUrl}),I.success("图片更新成功")),d.value=!1,w()}catch(t){console.error("更新长图失败:",t),I.error("更新失败")}finally{c.value=!1}};return j(()=>{w()}),(t,o)=>{const r=F,y=O,C=S,p=H,D=X,h=Z,M=G,N=J,T=K;return _(),f(q,null,{default:a(()=>[i("div",P,[i("div",Q,[o[4]||(o[4]=i("div",{class:"table-toolbar"},[i("div",{class:"toolbar-left"},[i("h3",null,"长图管理")]),i("div",{class:"toolbar-right"})],-1)),z((_(),f(D,{data:b.value,stripe:"",style:{width:"100%"}},{default:a(()=>[e(r,{prop:"id",label:"ID",width:"80"}),e(r,{prop:"title",label:"标题",width:"200"}),e(r,{label:"图片预览",width:"120"},{default:a(({row:l})=>[e(y,{src:l.imageUrl,"preview-src-list":[l.imageUrl],class:"table-image",fit:"cover"},null,8,["src","preview-src-list"])]),_:1}),e(r,{prop:"category",label:"分类",width:"120"}),e(r,{prop:"description",label:"描述"}),e(r,{prop:"creator",label:"创建者",width:"100"}),e(r,{prop:"sortOrder",label:"排序",width:"80"}),e(r,{label:"状态",width:"80"},{default:a(({row:l})=>[e(C,{type:l.enabled?"success":"danger"},{default:a(()=>[m(v(l.enabled?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"createdAt",label:"创建时间",width:"180"},{default:a(({row:l})=>[m(v(V(l.createdAt)),1)]),_:1}),e(r,{label:"操作",width:"100",fixed:"right"},{default:a(({row:l})=>[e(p,{type:"primary",size:"small",onClick:le=>k(l)},{default:a(()=>o[3]||(o[3]=[m(" 修改 ")])),_:2,__:[3]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[T,u.value]])]),e(N,{modelValue:d.value,"onUpdate:modelValue":o[2]||(o[2]=l=>d.value=l),title:"修改长图",width:"600px","close-on-click-modal":!1},{footer:a(()=>[i("div",Y,[e(p,{onClick:o[1]||(o[1]=l=>d.value=!1)},{default:a(()=>o[5]||(o[5]=[m(" 取消 ")])),_:1,__:[5]}),e(p,{type:"primary",loading:c.value,onClick:L},{default:a(()=>[m(v(c.value?"保存中...":"保存"),1)]),_:1},8,["loading"])])]),default:a(()=>[e(M,{ref_key:"formRef",ref:g,model:s,rules:E,"label-width":"100px"},{default:a(()=>[s.imageUrl?(_(),f(h,{key:0,label:"当前图片"},{default:a(()=>[i("div",W,[e(y,{src:s.imageUrl,"preview-src-list":[s.imageUrl],style:{"max-width":"100%","max-height":"300px"},fit:"contain"},null,8,["src","preview-src-list"])])]),_:1})):A("",!0),e(h,{label:"替换图片",prop:"imageUrl"},{default:a(()=>[e(R,{modelValue:s.imageUrl,"onUpdate:modelValue":o[0]||(o[0]=l=>s.imageUrl=l),"upload-url":"/api/file-upload/long-image",limit:1,accept:"image/*"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])]),_:1})}}},me=B(ee,[["__scopeId","data-v-64d9b70a"]]);export{me as default};
