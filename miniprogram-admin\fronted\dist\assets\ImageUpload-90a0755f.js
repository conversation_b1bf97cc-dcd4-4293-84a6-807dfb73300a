import{_ as B}from"./_plugin-vue_export-helper-063e7853.js";/* empty css                   *//* empty css                    */import{a as N,r as v,D as C,F as M,o as u,c,e as t,w as s,d as i,g as n,$ as g,a6 as $,i as y,a7 as A,H as D,j as d,x as H,Q as j,m as q,I as F}from"./index-fbf0bc73.js";const L={class:"image-upload"},Q={key:0,class:"image-preview"},T=["src"],W={class:"image-overlay"},G={key:1,class:"upload-placeholder"},J={key:0,class:"image-actions"},K={class:"preview-container"},O=["src"],P={__name:"ImageUpload",props:{modelValue:{type:String,default:""},uploadUrl:{type:String,required:!0},accept:{type:String,default:"image/*"},limit:{type:Number,default:1},maxSize:{type:Number,default:5}},emits:["update:modelValue"],setup(r,{emit:V}){const m=r,_=V,h=N(),o=v(""),p=v(!1),w=C(()=>({Authorization:`Bearer ${h.token}`}));M(()=>m.modelValue,e=>{o.value=e||""},{immediate:!0});const x=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<m.maxSize;return a?l?!0:(d.error(`图片大小不能超过 ${m.maxSize}MB!`),!1):(d.error("只能上传图片文件!"),!1)},I=e=>{e.code===200?(o.value=e.data,_("update:modelValue",e.data),d.success("上传成功")):d.error(e.message||"上传失败")},S=e=>{console.error("上传失败:",e),d.error("上传失败，请重试")},U=()=>{p.value=!0},k=()=>{o.value="",_("update:modelValue",""),d.success("删除成功")};return(e,a)=>{const l=H,b=j,f=q,z=F;return u(),c("div",L,[t(b,{action:r.uploadUrl,headers:w.value,"show-file-list":!1,"before-upload":x,"on-success":I,"on-error":S,accept:r.accept,limit:r.limit,class:"upload-container"},{default:s(()=>[o.value?(u(),c("div",Q,[i("img",{src:o.value,class:"uploaded-image"},null,8,T),i("div",W,[t(l,{class:"upload-icon"},{default:s(()=>[t(n(g))]),_:1}),a[1]||(a[1]=i("div",{class:"upload-text"},"重新上传",-1))])])):(u(),c("div",G,[t(l,{class:"upload-icon"},{default:s(()=>[t(n(g))]),_:1}),a[2]||(a[2]=i("div",{class:"upload-text"},"点击上传图片",-1))]))]),_:1},8,["action","headers","accept","limit"]),o.value?(u(),c("div",J,[t(f,{size:"small",onClick:U},{default:s(()=>[t(l,null,{default:s(()=>[t(n($))]),_:1}),a[3]||(a[3]=y(" 预览 "))]),_:1,__:[3]}),t(f,{size:"small",type:"danger",onClick:k},{default:s(()=>[t(l,null,{default:s(()=>[t(n(A))]),_:1}),a[4]||(a[4]=y(" 删除 "))]),_:1,__:[4]})])):D("",!0),t(z,{modelValue:p.value,"onUpdate:modelValue":a[0]||(a[0]=E=>p.value=E),title:"图片预览",width:"60%","append-to-body":""},{default:s(()=>[i("div",K,[i("img",{src:o.value,class:"preview-image"},null,8,O)])]),_:1},8,["modelValue"])])}}},ee=B(P,[["__scopeId","data-v-767c1279"]]);export{ee as I};
