import{_ as y}from"./_plugin-vue_export-helper-063e7853.js";/* empty css                     *//* empty css                 */import{u as b,a as x,r as c,b as V,o as E,c as k,d as p,e,w as r,f as F,E as B,g as m,h as L,i as R,t as S,j as _,k as z,l as I,m as N,n as q,p as C}from"./index-fbf0bc73.js";const K={class:"login-container"},M={class:"login-form"},U={__name:"Login",setup(j){const g=b(),f=x(),l=c(),t=c(!1),o=V({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},n=async()=>{if(l.value)try{await l.value.validate(),t.value=!0,console.log("开始登录...");const a=await f.login(o);console.log("登录响应:",a),_.success("登录成功"),console.log("准备跳转到 /dashboard"),await g.push("/dashboard"),console.log("跳转完成")}catch(a){console.error("登录失败:",a),_.error(a.message||"登录失败，请重试")}finally{t.value=!1}};return(a,s)=>{const d=z,i=I,v=N,h=B;return E(),k("div",K,[p("div",M,[s[2]||(s[2]=p("h2",{class:"login-title"},"名片管理平台",-1)),e(h,{ref_key:"loginFormRef",ref:l,model:o,rules:w,onSubmit:F(n,["prevent"])},{default:r(()=>[e(i,{prop:"username"},{default:r(()=>[e(d,{modelValue:o.username,"onUpdate:modelValue":s[0]||(s[0]=u=>o.username=u),placeholder:"请输入用户名",size:"large","prefix-icon":m(q)},null,8,["modelValue","prefix-icon"])]),_:1}),e(i,{prop:"password"},{default:r(()=>[e(d,{modelValue:o.password,"onUpdate:modelValue":s[1]||(s[1]=u=>o.password=u),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":m(C),onKeyup:L(n,["enter"]),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),e(i,null,{default:r(()=>[e(v,{type:"primary",size:"large",loading:t.value,onClick:n,style:{width:"100%"}},{default:r(()=>[R(S(t.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}},H=y(U,[["__scopeId","data-v-78b73319"]]);export{H as default};
