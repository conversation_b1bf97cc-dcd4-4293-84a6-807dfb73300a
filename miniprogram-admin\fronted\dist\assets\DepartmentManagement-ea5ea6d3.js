import{_ as O}from"./_plugin-vue_export-helper-063e7853.js";/* empty css                   */import{L as q}from"./Layout-97877336.js";import{r as p,b as N,D as R,F as M,o as k,s as I,w as a,d as b,e as t,i,t as D,c as A,L as G,M as H,v as x,j as g,k as X,l as Y,a1 as Z,a2 as J,y as K,z as P,a3 as Q,E as W,m as U,I as ee,q as te,g as le,X as ae,a4 as ne,H as se,Y as oe,R as re,T as de,U as ie,Z as ue,$ as ce}from"./index-fbf0bc73.js";/* empty css                     *//* empty css               *//* empty css                 */const me={class:"dialog-footer"},pe={__name:"DepartmentForm",props:{visible:Boolean,department:Object,parentDepartment:Object,isEdit:<PERSON>olean},emits:["update:visible","success"],setup(y,{emit:V}){const u=y,v=V,_=p(),m=p(!1),f=p([]),n=N({name:"",parentId:null,level:1,enabled:!0}),$=R(()=>{if(n.level===1)return[];if(u.isEdit)return f.value.filter(s=>{var e;return s.id!==((e=u.department)==null?void 0:e.id)&&s.level===n.level-1});const l=n.level-1;return f.value.filter(s=>s.level===l)}),w={name:[{required:!0,message:"请输入部门名称",trigger:"blur"}],level:[{required:!0,message:"请选择部门级别",trigger:"change"}]},T=l=>{l===1&&(n.parentId=null)},B=l=>{},L=async()=>{try{const l=await x.get("/departments");f.value=l.data||[]}catch(l){console.error("加载部门列表失败:",l)}};M(()=>u.department,l=>{l?Object.assign(n,{name:l.name||"",parentId:l.parentId||null,level:l.level||1,enabled:l.enabled!==!1}):Object.assign(n,{name:"",parentId:null,level:1,enabled:!0})},{immediate:!0}),M(()=>u.parentDepartment,l=>{l&&(n.parentId=l.id,n.level=l.level+1)},{immediate:!0}),M(()=>u.visible,l=>{l&&L()});const S=async()=>{var l,s;if(_.value)try{await _.value.validate(),m.value=!0;const e={...n};u.isEdit?(await x.put(`/departments/${u.department.id}`,e),g.success("更新成功")):(await x.post("/departments",e),g.success("创建成功")),v("success")}catch(e){console.error("保存部门失败:",e),g.error("保存失败: "+(((s=(l=e.response)==null?void 0:l.data)==null?void 0:s.message)||e.message))}finally{m.value=!1}};return(l,s)=>{const e=X,o=Y,c=Z,C=J,h=K,F=P,z=Q,r=W,E=U,j=ee;return k(),I(j,{"model-value":y.visible,"onUpdate:modelValue":s[5]||(s[5]=d=>l.$emit("update:visible",d)),title:y.isEdit?"编辑部门":"新增部门",width:"600px","close-on-click-modal":!1},{footer:a(()=>[b("div",me,[t(E,{onClick:s[4]||(s[4]=d=>l.$emit("update:visible",!1))},{default:a(()=>s[6]||(s[6]=[i(" 取消 ")])),_:1,__:[6]}),t(E,{type:"primary",loading:m.value,onClick:S},{default:a(()=>[i(D(m.value?"保存中...":"保存"),1)]),_:1},8,["loading"])])]),default:a(()=>[t(r,{ref_key:"formRef",ref:_,model:n,rules:w,"label-width":"100px"},{default:a(()=>[t(o,{label:"部门名称",prop:"name"},{default:a(()=>[t(e,{modelValue:n.name,"onUpdate:modelValue":s[0]||(s[0]=d=>n.name=d),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1}),t(F,{gutter:20},{default:a(()=>[t(h,{span:12},{default:a(()=>[t(o,{label:"部门级别",prop:"level"},{default:a(()=>[t(C,{modelValue:n.level,"onUpdate:modelValue":s[1]||(s[1]=d=>n.level=d),placeholder:"请选择级别",style:{width:"100%"},disabled:y.isEdit||!!y.parentDepartment,onChange:T},{default:a(()=>[t(c,{label:"一级部门",value:1}),t(c,{label:"二级部门",value:2}),t(c,{label:"三级部门",value:3})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),t(h,{span:12},{default:a(()=>[t(o,{label:"上级部门",prop:"parentId"},{default:a(()=>[t(C,{modelValue:n.parentId,"onUpdate:modelValue":s[2]||(s[2]=d=>n.parentId=d),placeholder:"请选择上级部门",style:{width:"100%"},disabled:!!y.parentDepartment||n.level===1,clearable:"",onChange:B},{default:a(()=>[(k(!0),A(G,null,H($.value,d=>(k(),I(c,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),t(o,{label:"状态"},{default:a(()=>[t(z,{modelValue:n.enabled,"onUpdate:modelValue":s[3]||(s[3]=d=>n.enabled=d),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}},ve=O(pe,[["__scopeId","data-v-3dbdb4cb"]]);const _e={class:"department-management"},fe={class:"content-card"},be={class:"toolbar"},ge={class:"toolbar-right"},ye={__name:"DepartmentManagement",setup(y){const V=p(!1),u=p([]),v=p(!1),_=p(null),m=p(null),f=p(!1),n=async()=>{V.value=!0;try{const e=await x.get("/departments/enabled/tree");u.value=e.data||[]}catch(e){console.error("加载部门树失败:",e),g.error("加载部门树失败")}finally{V.value=!1}},$=()=>{_.value=null,m.value=null,f.value=!1,v.value=!0},w=e=>{_.value={...e},m.value=null,f.value=!0,v.value=!0},T=e=>{_.value=null,m.value=e,f.value=!1,v.value=!0},B=()=>{v.value=!1,n()},L=async e=>{try{await x.put(`/departments/${e.id}/toggle-status`),g.success("状态切换成功"),n()}catch(o){console.error("切换部门状态失败:",o),g.error("切换部门状态失败")}},S=async e=>{try{await oe.confirm(`确定要删除部门 ${e.name} 吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await x.delete(`/departments/${e.id}`),g.success("删除成功"),n()}catch(o){o!=="cancel"&&(console.error("删除部门失败:",o),g.error("删除部门失败"))}},l=e=>({1:"danger",2:"warning",3:"success"})[e]||"",s=e=>({1:"一级",2:"二级",3:"三级"})[e]||"";return te(()=>{n()}),(e,o)=>{const c=U,C=re,h=de,F=ie,z=ue;return k(),I(q,null,{default:a(()=>[b("div",_e,[b("div",fe,[b("div",be,[o[2]||(o[2]=b("div",{class:"toolbar-left"},[b("h3",null,"部门管理")],-1)),b("div",ge,[t(c,{type:"primary",icon:le(ce),onClick:$},{default:a(()=>o[1]||(o[1]=[i(" 新增部门 ")])),_:1,__:[1]},8,["icon"])])]),ae((k(),I(F,{data:u.value,"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},"default-expand-all":!1,style:{width:"100%"}},{default:a(()=>[t(h,{prop:"name",label:"部门名称"},{default:a(({row:r})=>[b("span",{style:ne({paddingLeft:(r.level-1)*20+"px"})},[t(C,{size:"small",type:"info",class:"department-id-tag"},{default:a(()=>[i(" ID: "+D(r.id),1)]),_:2},1024),i(" "+D(r.name),1)],4)]),_:1}),t(h,{prop:"level",label:"级别",width:"100"},{default:a(({row:r})=>[t(C,{type:l(r.level)},{default:a(()=>[i(D(s(r.level)),1)]),_:2},1032,["type"])]),_:1}),t(h,{label:"状态",width:"100"},{default:a(({row:r})=>[t(C,{type:r.enabled?"success":"danger"},{default:a(()=>[i(D(r.enabled?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(h,{label:"操作",width:"300",fixed:"right"},{default:a(({row:r})=>[t(c,{type:"primary",size:"small",onClick:E=>w(r)},{default:a(()=>o[3]||(o[3]=[i(" 编辑 ")])),_:2,__:[3]},1032,["onClick"]),r.level<3?(k(),I(c,{key:0,type:"success",size:"small",onClick:E=>T(r)},{default:a(()=>o[4]||(o[4]=[i(" 添加子部门 ")])),_:2,__:[4]},1032,["onClick"])):se("",!0),t(c,{type:r.enabled?"warning":"success",size:"small",onClick:E=>L(r)},{default:a(()=>[i(D(r.enabled?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),t(c,{type:"danger",size:"small",onClick:E=>S(r),disabled:r.employeeCount>0||r.children&&r.children.length>0},{default:a(()=>o[5]||(o[5]=[i(" 删除 ")])),_:2,__:[5]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[z,V.value]])]),t(ve,{visible:v.value,"onUpdate:visible":o[0]||(o[0]=r=>v.value=r),department:_.value,"parent-department":m.value,"is-edit":f.value,onSuccess:B},null,8,["visible","department","parent-department","is-edit"])])]),_:1})}}},Ie=O(ye,[["__scopeId","data-v-5d51a654"]]);export{Ie as default};
