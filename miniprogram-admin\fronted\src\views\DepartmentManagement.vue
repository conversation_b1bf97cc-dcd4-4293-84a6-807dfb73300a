<template>
  <Layout>
    <div class="department-management">
      <div class="content-card">
        <!-- 页面标题和操作栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <h3>部门管理</h3>
          </div>
          <div class="toolbar-right">
            <el-button
              type="primary"
              :icon="Plus"
              @click="showCreateDialog"
            >
              新增部门
            </el-button>
          </div>
        </div>



        <!-- 部门树形表格 -->
        <el-table
          :data="departmentTree"
          v-loading="loading"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :default-expand-all="false"
          style="width: 100%"
        >
          <el-table-column prop="name" label="部门名称">
            <template #default="{ row }">
              <span :style="{ paddingLeft: (row.level - 1) * 20 + 'px' }">
                <el-tag size="small" type="info" class="department-id-tag">
                  ID: {{ row.id }}
                </el-tag>
                {{ row.name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="级别" width="100">
            <template #default="{ row }">
              <el-tag :type="getLevelTagType(row.level)">
                {{ getLevelText(row.level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.enabled ? 'success' : 'danger'">
                {{ row.enabled ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sortOrder" label="排序" width="80" />
          <el-table-column label="操作" width="400" fixed="right">
            <template #default="{ row }">
              <el-button
                type="info"
                size="small"
                @click="moveDepartmentUp(row)"
                :disabled="!canMoveUp(row)"
              >
                上移
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="moveDepartmentDown(row)"
                :disabled="!canMoveDown(row)"
              >
                下移
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="row.level < 3"
                type="success"
                size="small"
                @click="showCreateChildDialog(row)"
              >
                添加子部门
              </el-button>
              <el-button
                :type="row.enabled ? 'warning' : 'success'"
                size="small"
                @click="toggleDepartmentStatus(row)"
              >
                {{ row.enabled ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteDepartment(row)"
                :disabled="row.employeeCount > 0 || (row.children && row.children.length > 0)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 部门表单对话框 -->
      <DepartmentForm
        v-model:visible="dialogVisible"
        :department="currentDepartment"
        :parent-department="parentDepartment"
        :is-edit="isEdit"
        @success="handleFormSuccess"
      />
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import DepartmentForm from '@/components/DepartmentForm.vue'
import request from '@/utils/request'

const loading = ref(false)
const departmentTree = ref([])
const dialogVisible = ref(false)
const currentDepartment = ref(null)
const parentDepartment = ref(null)
const isEdit = ref(false)



// 加载部门树
const loadDepartmentTree = async () => {
  loading.value = true
  try {
    const response = await request.get('/departments/enabled/tree')
    departmentTree.value = response.data || []
  } catch (error) {
    console.error('加载部门树失败:', error)
    ElMessage.error('加载部门树失败')
  } finally {
    loading.value = false
  }
}



// 显示新增对话框
const showCreateDialog = () => {
  currentDepartment.value = null
  parentDepartment.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (department) => {
  currentDepartment.value = { ...department }
  parentDepartment.value = null
  isEdit.value = true
  dialogVisible.value = true
}

// 显示新增子部门对话框
const showCreateChildDialog = (parentDept) => {
  currentDepartment.value = null
  parentDepartment.value = parentDept
  isEdit.value = false
  dialogVisible.value = true
}

// 表单成功回调
const handleFormSuccess = () => {
  dialogVisible.value = false
  loadDepartmentTree()
}

// 切换部门状态
const toggleDepartmentStatus = async (department) => {
  try {
    await request.put(`/departments/${department.id}/toggle-status`)
    ElMessage.success('状态切换成功')
    loadDepartmentTree()
  } catch (error) {
    console.error('切换部门状态失败:', error)
    ElMessage.error('切换部门状态失败')
  }
}

// 删除部门
const deleteDepartment = async (department) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门 ${department.name} 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await request.delete(`/departments/${department.id}`)
    ElMessage.success('删除成功')
    loadDepartmentTree()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除部门失败:', error)
      ElMessage.error('删除部门失败')
    }
  }
}

// 上移部门
const moveDepartmentUp = async (department) => {
  try {
    await request.put(`/departments/${department.id}/move-up`)
    ElMessage.success('上移成功')
    loadDepartmentTree()
  } catch (error) {
    console.error('上移部门失败:', error)
    ElMessage.error('上移部门失败')
  }
}

// 下移部门
const moveDepartmentDown = async (department) => {
  try {
    await request.put(`/departments/${department.id}/move-down`)
    ElMessage.success('下移成功')
    loadDepartmentTree()
  } catch (error) {
    console.error('下移部门失败:', error)
    ElMessage.error('下移部门失败')
  }
}

// 检查是否可以上移
const canMoveUp = (department) => {
  // 简化实现，实际可以根据同级部门的排序来判断
  return true
}

// 检查是否可以下移
const canMoveDown = (department) => {
  // 简化实现，实际可以根据同级部门的排序来判断
  return true
}

// 获取级别标签类型
const getLevelTagType = (level) => {
  const types = { 1: 'danger', 2: 'warning', 3: 'success' }
  return types[level] || ''
}

// 获取级别文本
const getLevelText = (level) => {
  const texts = { 1: '一级', 2: '二级', 3: '三级' }
  return texts[level] || ''
}

onMounted(() => {
  loadDepartmentTree()
})
</script>

<style scoped>
.department-management {
  max-width: 1400px;
  margin: 0 auto;
}



h3 {
  color: var(--text-primary);
  margin: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
}

.department-id-tag {
  margin-right: 8px;
  font-size: 11px;
  font-weight: bold;
}
</style>
