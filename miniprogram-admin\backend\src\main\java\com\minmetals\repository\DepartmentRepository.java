package com.minmetals.repository;

import com.minmetals.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 部门数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {

    /**
     * 根据父部门ID查询子部门列表
     */
    List<Department> findByParentIdOrderBySortOrderAsc(Long parentId);

    /**
     * 根据部门级别查询部门列表
     */
    List<Department> findByLevelOrderBySortOrderAsc(Integer level);

    /**
     * 根据部门名称查询部门
     */
    List<Department> findByNameContainingIgnoreCase(String name);

    /**
     * 查询启用状态的部门
     */
    List<Department> findByEnabledTrueOrderBySortOrderAsc();

    /**
     * 查询指定父部门下的启用状态子部门
     */
    List<Department> findByParentIdAndEnabledTrueOrderBySortOrderAsc(Long parentId);

    /**
     * 查询指定级别的启用状态部门
     */
    List<Department> findByLevelAndEnabledTrueOrderBySortOrderAsc(Integer level);

    /**
     * 统计指定部门下的子部门数量
     */
    long countByParentId(Long parentId);

    /**
     * 根据路径前缀查询所有子部门（包括子子部门）
     */
    @Query("SELECT d FROM Department d WHERE d.path LIKE CONCAT(:pathPrefix, '%') ORDER BY d.path, d.sortOrder")
    List<Department> findByPathStartingWith(@Param("pathPrefix") String pathPrefix);

    /**
     * 查询所有一级部门
     */
    @Query("SELECT d FROM Department d WHERE d.parentId IS NULL ORDER BY d.sortOrder")
    List<Department> findRootDepartments();

    /**
     * 查询启用状态的一级部门
     */
    @Query("SELECT d FROM Department d WHERE d.parentId IS NULL AND d.enabled = true ORDER BY d.sortOrder")
    List<Department> findEnabledRootDepartments();
}
