// pages/employee-list/employee-list.js
const { api } = require('../../utils/api')

Page({
  data: {
    userInfo: {},
    employees: [],
    filteredEmployees: [],
    departments: [], // 所有部门数据

    // 3级部门选择器数据
    level1DepartmentOptions: ['全部一级部门'],
    level2DepartmentOptions: ['全部二级部门'],
    level3DepartmentOptions: ['全部三级部门'],

    // 选中的部门
    selectedLevel1Department: '',
    selectedLevel2Department: '',
    selectedLevel3Department: '',
    selectedLevel1Index: 0,
    selectedLevel2Index: 0,
    selectedLevel3Index: 0,

    // 当前选中的部门ID列表
    selectedDepartmentIds: [],

    loading: true
  },

  onLoad() {
    this.checkLoginStatus()
    this.loadUserInfo()
    this.loadDepartments()
    // 不再自动加载员工列表，需要用户先选择部门
  },

  onShow() {
    // 每次显示页面时只刷新用户信息，不自动加载员工数据
    this.loadUserInfo()
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.redirectTo({
        url: '/pages/login/login'
      })
      return
    }
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const app = getApp()
    if (app.globalData.employeeInfo) {
      this.setData({
        userInfo: app.globalData.employeeInfo
      })
    }
  },

  /**
   * 加载部门数据
   */
  async loadDepartments() {
    try {
      const result = await api.getDepartmentList()

      if (result.code === 200 && result.data) {
        const departments = result.data
        this.setData({
          departments
        })

        // 构建部门选择器选项
        this.buildDepartmentOptions(departments)
      } else {
        console.error('获取部门列表失败:', result.message)
      }
    } catch (error) {
      console.error('加载部门数据失败:', error)
    }
  },

  /**
   * 构建部门选择器选项
   */
  buildDepartmentOptions(departments) {
    // 按级别分组部门
    const level1Departments = departments.filter(dept => dept.level === 1)
    const level2Departments = departments.filter(dept => dept.level === 2)
    const level3Departments = departments.filter(dept => dept.level === 3)

    // 构建一级部门选项
    const level1Options = ['全部一级部门', ...level1Departments.map(dept => dept.name)]

    this.setData({
      level1DepartmentOptions: level1Options,
      level2DepartmentOptions: ['全部二级部门'],
      level3DepartmentOptions: ['全部三级部门']
    })
  },

  /**
   * 加载员工列表
   */
  async loadEmployeeList() {
    try {
      const { selectedDepartmentIds } = this.data

      // 如果没有选择部门，则不加载数据
      if (!selectedDepartmentIds || selectedDepartmentIds.length === 0) {
        this.setData({
          employees: [],
          filteredEmployees: [],
          loading: false
        })
        return
      }

      this.setData({
        loading: true
      })

      wx.showLoading({
        title: '加载中...'
      })

      let result

      if (selectedDepartmentIds && selectedDepartmentIds.length > 0) {
        // 根据选中的部门加载员工，需要合并多个部门的员工
        const allEmployees = []
        for (const departmentId of selectedDepartmentIds) {
          try {
            const deptResult = await api.getEmployeesByDepartment(departmentId)
            if (deptResult.code === 200 && deptResult.data) {
              allEmployees.push(...deptResult.data)
            }
          } catch (error) {
            console.error(`获取部门 ${departmentId} 员工失败:`, error)
          }
        }

        // 去重（根据员工ID）
        const uniqueEmployees = []
        const employeeIds = new Set()
        allEmployees.forEach(employee => {
          if (!employeeIds.has(employee.id)) {
            employeeIds.add(employee.id)
            uniqueEmployees.push(employee)
          }
        })

        result = {
          code: 200,
          data: uniqueEmployees
        }
      }

      if (result.code === 200 && result.data) {
        const employees = result.data

        // 为每个员工构建部门路径
        const employeesWithPath = await this.buildEmployeeDepartmentPaths(employees)

        this.setData({
          employees: employeesWithPath,
          filteredEmployees: employeesWithPath,
          loading: false
        })
      } else {
        throw new Error(result.message || '获取员工列表失败')
      }

    } catch (error) {
      console.error('加载员工列表失败:', error)

      this.setData({
        loading: false,
        employees: [],
        filteredEmployees: []
      })

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
        duration: 3000
      })

    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 构建员工部门路径
   */
  async buildEmployeeDepartmentPaths(employees) {
    const { departments } = this.data

    return employees.map(employee => {
      if (employee.departmentId && departments.length > 0) {
        const department = departments.find(dept => dept.id === employee.departmentId)
        if (department) {
          employee.departmentPath = this.buildDepartmentPath(department, departments)
        }
      }
      return employee
    })
  },

  /**
   * 构建部门路径字符串
   */
  buildDepartmentPath(department, allDepartments) {
    const path = []
    let currentDept = department

    // 向上查找父部门，构建完整路径
    while (currentDept) {
      path.unshift(currentDept.name)
      if (currentDept.parentId) {
        currentDept = allDepartments.find(dept => dept.id === currentDept.parentId)
      } else {
        currentDept = null
      }
    }

    return path.join(' > ')
  },

  /**
   * 一级部门选择变化
   */
  onLevel1DepartmentChange(e) {
    const index = parseInt(e.detail.value)
    const selectedDepartment = index === 0 ? '' : this.data.level1DepartmentOptions[index]

    this.setData({
      selectedLevel1Index: index,
      selectedLevel1Department: selectedDepartment,
      selectedLevel2Department: '',
      selectedLevel3Department: '',
      selectedLevel2Index: 0,
      selectedLevel3Index: 0,
      level2DepartmentOptions: ['全部二级部门'],
      level3DepartmentOptions: ['全部三级部门']
    })

    if (selectedDepartment) {
      this.loadLevel2Departments(selectedDepartment)
    }

    this.updateSelectedDepartmentId()
  },

  /**
   * 二级部门选择变化
   */
  onLevel2DepartmentChange(e) {
    const index = parseInt(e.detail.value)
    const selectedDepartment = index === 0 ? '' : this.data.level2DepartmentOptions[index]

    this.setData({
      selectedLevel2Index: index,
      selectedLevel2Department: selectedDepartment,
      selectedLevel3Department: '',
      selectedLevel3Index: 0,
      level3DepartmentOptions: ['全部三级部门']
    })

    if (selectedDepartment) {
      this.loadLevel3Departments(selectedDepartment)
    }

    this.updateSelectedDepartmentId()
  },

  /**
   * 三级部门选择变化
   */
  onLevel3DepartmentChange(e) {
    const index = parseInt(e.detail.value)
    const selectedDepartment = index === 0 ? '' : this.data.level3DepartmentOptions[index]

    this.setData({
      selectedLevel3Index: index,
      selectedLevel3Department: selectedDepartment
    })

    this.updateSelectedDepartmentId()
  },

  /**
   * 加载二级部门
   */
  loadLevel2Departments(level1DepartmentName) {
    const { departments } = this.data
    const level1Dept = departments.find(dept => dept.name === level1DepartmentName && dept.level === 1)

    if (level1Dept) {
      const level2Departments = departments.filter(dept => dept.parentId === level1Dept.id)
      const level2Options = ['全部二级部门', ...level2Departments.map(dept => dept.name)]

      this.setData({
        level2DepartmentOptions: level2Options
      })
    }
  },

  /**
   * 加载三级部门
   */
  loadLevel3Departments(level2DepartmentName) {
    const { departments } = this.data
    const level2Dept = departments.find(dept => dept.name === level2DepartmentName && dept.level === 2)

    if (level2Dept) {
      const level3Departments = departments.filter(dept => dept.parentId === level2Dept.id)
      const level3Options = ['全部三级部门', ...level3Departments.map(dept => dept.name)]

      this.setData({
        level3DepartmentOptions: level3Options
      })
    }
  },

  /**
   * 更新选中的部门ID并重新加载员工
   */
  updateSelectedDepartmentId() {
    const { departments, selectedLevel1Department, selectedLevel2Department, selectedLevel3Department } = this.data
    let selectedDepartmentIds = []

    // 根据选择的部门级别，获取该部门及其所有子部门的ID
    if (selectedLevel3Department) {
      // 选择了三级部门，只获取该部门的员工
      const dept = departments.find(dept => dept.name === selectedLevel3Department && dept.level === 3)
      if (dept) {
        selectedDepartmentIds = [dept.id]
      }
    } else if (selectedLevel2Department) {
      // 选择了二级部门，获取该部门及其所有三级子部门的员工
      const level2Dept = departments.find(dept => dept.name === selectedLevel2Department && dept.level === 2)
      if (level2Dept) {
        selectedDepartmentIds = [level2Dept.id]
        // 添加所有三级子部门
        const level3Children = departments.filter(dept => dept.parentId === level2Dept.id)
        selectedDepartmentIds.push(...level3Children.map(dept => dept.id))
      }
    } else if (selectedLevel1Department) {
      // 选择了一级部门，获取该部门及其所有子部门的员工
      const level1Dept = departments.find(dept => dept.name === selectedLevel1Department && dept.level === 1)
      if (level1Dept) {
        selectedDepartmentIds = [level1Dept.id]
        // 添加所有二级子部门
        const level2Children = departments.filter(dept => dept.parentId === level1Dept.id)
        selectedDepartmentIds.push(...level2Children.map(dept => dept.id))
        // 添加所有三级子部门
        level2Children.forEach(level2Child => {
          const level3Children = departments.filter(dept => dept.parentId === level2Child.id)
          selectedDepartmentIds.push(...level3Children.map(dept => dept.id))
        })
      }
    }

    this.setData({
      selectedDepartmentIds
    })

    // 重新加载员工列表
    this.loadEmployeeList()
  },

  /**
   * 清除筛选条件
   */
  clearFilters() {
    this.setData({
      selectedLevel1Department: '',
      selectedLevel2Department: '',
      selectedLevel3Department: '',
      selectedLevel1Index: 0,
      selectedLevel2Index: 0,
      selectedLevel3Index: 0,
      selectedDepartmentIds: [],
      level2DepartmentOptions: ['全部二级部门'],
      level3DepartmentOptions: ['全部三级部门'],
      employees: [],
      filteredEmployees: []
    })

    // 清除筛选后不自动加载数据，需要用户重新选择部门
  },

  /**
   * 跳转到员工详情页
   */
  goToDetail(e) {
    const employee = e.currentTarget.dataset.employee

    if (!employee || !employee.id) {
      wx.showToast({
        title: '员工信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/employee-detail/employee-detail?id=${employee.id}`
    })
  },

  /**
   * 跳转到自己的名片页面
   */
  goToMyBusinessCard() {
    const app = getApp()
    const userInfo = app.globalData.employeeInfo

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/employee-detail/employee-detail?id=${userInfo.id}`
    })
  },

  /**
   * 处理登出
   */
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp()
          app.logout()

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })

          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/index/index'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadEmployeeList().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '中国五矿 - 员工名录',
      path: '/pages/index/index'
    }
  }
})
