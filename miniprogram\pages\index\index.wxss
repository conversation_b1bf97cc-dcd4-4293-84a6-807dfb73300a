/* pages/index/index.wxss */
/* Logo样式 - 固定在左上角 */
.logo-container {
  position: fixed;
  top: 150rpx;
  left: 90rpx;
  z-index: 1000;
  width: 300rpx;
  height: 200rpx;
  border-radius: 16rpx;
  padding: 10rpx;
  backdrop-filter: blur(10rpx);
}

.logo {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.index-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(220, 221, 221, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 100%
  );
  z-index: 2;
}

.content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 80rpx 60rpx;
  text-align: center;
}

.title-section {
  margin-bottom: 120rpx;
}

.main-title {
  display: block;
  font-size: 72rpx;
  font-weight: bold;
  color: #221815;
  text-shadow: 2rpx 2rpx 8rpx rgba(255, 255, 255, 0.8);
  margin-bottom: 24rpx;
  letter-spacing: 8rpx;
}

.sub-title {
  display: block;
  font-size: 32rpx;
  color: #727171;
  text-shadow: 1rpx 1rpx 4rpx rgba(255, 255, 255, 0.8);
  letter-spacing: 2rpx;
}

.button-section {
  width: 100%;
  max-width: 400rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.enter-btn, .long-images-btn {
  width: 100%;
  height: 96rpx;
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.enter-btn {
  background: #ffffff;
}

.long-images-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(199, 0, 25, 0.3);
}

.enter-btn:active, .long-images-btn:active {
  transform: translateY(2rpx);
}

.enter-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(199, 0, 25, 0.4);
}

.long-images-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(114, 113, 113, 0.4);
}

.btn-text {
  font-size: 36rpx;
  color: #000000;
  font-weight: 500;
  letter-spacing: 4rpx;
}

.loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(220, 221, 221, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-text {
  font-size: 32rpx;
  color: #727171;
}

/* 响应式设计已通过rpx单位实现，无需媒体查询 */
