/* pages/employee-list/employee-list.wxss */
.employee-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.welcome-section {
  flex: 1;
}

.welcome-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #221815;
  margin-bottom: 8rpx;
}

.department-text {
  display: block;
  font-size: 24rpx;
  color: #727171;
}

.button-section {
  flex-shrink: 0;
  gap: 12rpx;
  align-items: center;
}

.my-card-btn {
  padding: 16rpx 1rpx;
  background-color: #c70019;
  border: 1rpx solid #c70019;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.my-card-btn:active {
  background-color: #a50015;
}

.logout-btn {
  padding: 16rpx 16rpx;
  background-color: #f0f0f0;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #727171;
}

.logout-btn:active {
  background-color: #e0e0e0;
}

.filter-section {
  margin-bottom: 20rpx;
}

.filter-box {
  display: flex;
  flex-wrap: wrap;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  align-items: center;
  gap: 12rpx;
}

.filter-item {
  flex: 1;
  min-width: 200rpx;
}

.filter-picker {
  width: 100%;
  height: 64rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #f8f8f8;
}

.picker-text {
  font-size: 28rpx;
  color: #221815;
  flex: 1;
}

.picker-arrow {
  font-size: 24rpx;
  color: #727171;
  margin-left: 8rpx;
}

.clear-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1rpx solid #c70019;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-top: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.clear-filter-btn:active {
  background-color: #f8f8f8;
}

.clear-text {
  font-size: 28rpx;
  color: #c70019;
  font-weight: 500;
}

.list-section {
  flex: 1;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 16rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #221815;
}

.list-count {
  font-size: 24rpx;
  color: #727171;
}

.loading-container,
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #727171;
}

.employee-scroll {
  height: calc(100vh - 400rpx);
}

.employee-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.employee-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.avatar-section {
  margin-right: 24rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.info-section {
  flex: 1;
}

.name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #221815;
  margin-bottom: 8rpx;
}

.employee-id {
  display: block;
  font-size: 24rpx;
  color: #727171;
}

.arrow-section {
  margin-left: 16rpx;
}

.arrow {
  font-size: 32rpx;
  color: #c0c0c0;
  font-weight: bold;
}

.card-body {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #727171;
  width: 120rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #221815;
  flex: 1;
}

.value.phone {
  color: #c70019;
}

/* 响应式设计已通过rpx单位实现，无需媒体查询 */
