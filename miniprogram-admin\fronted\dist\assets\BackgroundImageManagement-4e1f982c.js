import{_ as T}from"./_plugin-vue_export-helper-063e7853.js";/* empty css                   *//* empty css                     */import{L as q}from"./Layout-97877336.js";import{I as R}from"./ImageUpload-90a0755f.js";import{r as n,b as $,q as j,o as p,s as f,w as a,v as I,d as i,X as z,e,i as m,t as v,H as A,j as h,T as F,a5 as O,R as S,m as H,U as X,l as Z,E as G,I as J,Z as K}from"./index-fbf0bc73.js";/* empty css                    */const P={class:"background-image-management"},Q={class:"content-card"},W={class:"dialog-footer"},Y={__name:"BackgroundImageManagement",setup(ee){const u=n(!1),c=n(!1),b=n([]),d=n(!1),k=n(!1),g=n(),r=$({imageUrl:""}),E={imageUrl:[{required:!0,message:"请上传图片文件",trigger:"change"}]},w=async()=>{u.value=!0;try{const t=await I.get("/background-images");b.value=t.data||[]}catch(t){console.error("加载背景图片列表失败:",t)}finally{u.value=!1}},x=t=>t?new Date(t).toLocaleString("zh-CN"):"",V=t=>{Object.assign(r,{imageUrl:t.imageUrl||""}),r.currentImageId=t.id,k.value=!0,d.value=!0},C=async()=>{if(g.value)try{await g.value.validate(),c.value=!0,r.currentImageId&&(await I.put(`/background-images/${r.currentImageId}`,{imageUrl:r.imageUrl}),h.success("图片更新成功")),d.value=!1,w()}catch(t){console.error("更新背景图片失败:",t),h.error("更新失败")}finally{c.value=!1}};return j(()=>{w()}),(t,o)=>{const s=F,y=O,B=S,_=H,D=X,U=Z,L=G,M=J,N=K;return p(),f(q,null,{default:a(()=>[i("div",P,[i("div",Q,[o[4]||(o[4]=i("div",{class:"table-toolbar"},[i("div",{class:"toolbar-left"},[i("h3",null,"背景图片管理")]),i("div",{class:"toolbar-right"})],-1)),z((p(),f(D,{data:b.value,stripe:"",style:{width:"100%"}},{default:a(()=>[e(s,{prop:"id",label:"ID",width:"80"}),e(s,{prop:"name",label:"图片名称",width:"200"}),e(s,{label:"图片预览",width:"120"},{default:a(({row:l})=>[e(y,{src:l.imageUrl,"preview-src-list":[l.imageUrl],class:"table-image",fit:"cover"},null,8,["src","preview-src-list"])]),_:1}),e(s,{prop:"description",label:"描述"}),e(s,{prop:"sortOrder",label:"排序",width:"80"}),e(s,{label:"状态",width:"80"},{default:a(({row:l})=>[e(B,{type:l.enabled?"success":"danger"},{default:a(()=>[m(v(l.enabled?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(s,{prop:"createdAt",label:"创建时间",width:"180"},{default:a(({row:l})=>[m(v(x(l.createdAt)),1)]),_:1}),e(s,{label:"操作",width:"100",fixed:"right"},{default:a(({row:l})=>[e(_,{type:"primary",size:"small",onClick:ae=>V(l)},{default:a(()=>o[3]||(o[3]=[m(" 修改 ")])),_:2,__:[3]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[N,u.value]])]),e(M,{modelValue:d.value,"onUpdate:modelValue":o[2]||(o[2]=l=>d.value=l),title:"修改背景图片",width:"600px","close-on-click-modal":!1},{footer:a(()=>[i("div",W,[e(_,{onClick:o[1]||(o[1]=l=>d.value=!1)},{default:a(()=>o[5]||(o[5]=[m(" 取消 ")])),_:1,__:[5]}),e(_,{type:"primary",loading:c.value,onClick:C},{default:a(()=>[m(v(c.value?"保存中...":"保存"),1)]),_:1},8,["loading"])])]),default:a(()=>[e(L,{ref_key:"formRef",ref:g,model:r,rules:E,"label-width":"100px"},{default:a(()=>[r.imageUrl?(p(),f(U,{key:0,label:"当前图片"},{default:a(()=>[e(y,{src:r.imageUrl,"preview-src-list":[r.imageUrl],style:{width:"200px",height:"120px"},fit:"cover"},null,8,["src","preview-src-list"])]),_:1})):A("",!0),e(U,{label:"替换图片",prop:"imageUrl"},{default:a(()=>[e(R,{modelValue:r.imageUrl,"onUpdate:modelValue":o[0]||(o[0]=l=>r.imageUrl=l),"upload-url":"/api/file-upload/background-image",limit:1,accept:"image/*"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])]),_:1})}}},de=T(Y,[["__scopeId","data-v-b6143818"]]);export{de as default};
