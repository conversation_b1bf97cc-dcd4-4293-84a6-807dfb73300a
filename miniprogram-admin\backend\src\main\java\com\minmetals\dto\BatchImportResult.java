package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量导入结果DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "批量导入结果")
public class BatchImportResult {

    @Schema(description = "成功导入数量")
    private int successCount;

    @Schema(description = "失败数量")
    private int failureCount;

    @Schema(description = "失败详情列表")
    private List<ImportFailure> failures;

    /**
     * 导入失败详情
     */
    @Data
    @Schema(description = "导入失败详情")
    public static class ImportFailure {
        
        @Schema(description = "行号")
        private int row;
        
        @Schema(description = "员工姓名")
        private String name;
        
        @Schema(description = "用户名")
        private String username;
        
        @Schema(description = "失败原因")
        private String error;

        public ImportFailure(int row, String name, String username, String error) {
            this.row = row;
            this.name = name;
            this.username = username;
            this.error = error;
        }
    }
}
