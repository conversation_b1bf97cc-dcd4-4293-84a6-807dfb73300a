<template>
  <Layout>
    <div class="employee-management">
      <div class="content-card">
        <div class="table-toolbar">
          <div class="toolbar-left">
            <h3>员工管理</h3>
          </div>
          <div class="toolbar-right">
            <el-button type="success" @click="showBatchImportDialog">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新增员工
            </el-button>
          </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-bar">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="filters.keyword"
                placeholder="搜索员工姓名"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="filters.departmentId"
                placeholder="选择部门"
                clearable
                @change="handleDepartmentFilter"
                style="width: 100%"
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <!-- 员工表格 -->
        <el-table
          :data="employeeList"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="employeeId" label="传真" width="120" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="departmentPath" label="部门" width="200">
            <template #default="{ row }">
              {{ row.departmentPath || row.department }}
            </template>
          </el-table-column>
          <el-table-column prop="position" label="职位" width="120" />
          <el-table-column prop="email" label="邮箱" width="180" />
          <el-table-column prop="phone" label="电话" width="130" />
          <el-table-column prop="avatar" label="移动电话" width="130" />
          <el-table-column prop="manager" label="邮政编码" width="100" />
          <el-table-column prop="workLocation" label="工作地址" width="150" />
          <el-table-column prop="address" label="详细地址" width="200" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteEmployee(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 员工表单对话框 -->
      <EmployeeForm
        v-model:visible="dialogVisible"
        :employee="currentEmployee"
        :is-edit="isEdit"
        @success="handleFormSuccess"
      />

      <!-- 批量导入对话框 -->
      <BatchImportDialog
        v-model:visible="batchImportVisible"
        @success="handleBatchImportSuccess"
      />
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Upload } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import EmployeeForm from '@/components/EmployeeForm.vue'
import BatchImportDialog from '@/components/BatchImportDialog.vue'

import request from '@/utils/request'

const loading = ref(false)
const employeeList = ref([])
const dialogVisible = ref(false)
const currentEmployee = ref(null)
const isEdit = ref(false)
const batchImportVisible = ref(false)
const departmentOptions = ref([])

const filters = ref({
  keyword: '',
  departmentId: null
})



// 加载部门列表
const loadDepartments = async () => {
  try {
    const response = await request.get('/departments')
    departmentOptions.value = response.data || []
  } catch (error) {
    console.error('加载部门列表失败:', error)
  }
}

// 加载员工列表
const loadEmployees = async () => {
  loading.value = true
  try {
    let employees = []

    if (filters.value.departmentId) {
      // 按部门筛选
      const response = await request.get(`/employees/department-id/${filters.value.departmentId}`)
      employees = response.data || []
    } else if (filters.value.keyword) {
      // 按关键词搜索
      const response = await request.get('/employees/search', {
        params: { keyword: filters.value.keyword }
      })
      employees = response.data || []
    } else {
      // 获取所有员工
      const response = await request.get('/employees')
      employees = response.data || []
    }

    // 为每个员工构建部门路径
    for (const employee of employees) {
      if (employee.departmentId) {
        try {
          const deptResponse = await request.get(`/departments/${employee.departmentId}`)
          employee.departmentPath = buildDepartmentPath(deptResponse.data)
        } catch (error) {
          console.error('获取部门路径失败:', error)
          employee.departmentPath = employee.department
        }
      }
    }

    employeeList.value = employees
  } catch (error) {
    console.error('加载员工列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 构建部门路径
const buildDepartmentPath = (department) => {
  if (!department) return ''

  // 如果有path字段，解析路径构建完整名称
  if (department.path) {
    // path格式如：/1/6/13，需要根据ID查询对应的部门名称
    // 简化实现，直接返回部门名称，实际项目中可以根据path递归查询
    return department.name
  }

  return department.name
}

// 搜索处理
const handleSearch = () => {
  // 清除部门筛选
  filters.value.departmentId = null
  loadEmployees()
}

// 部门筛选处理
const handleDepartmentFilter = () => {
  // 清除关键词搜索
  filters.value.keyword = ''
  loadEmployees()
}



// 显示创建对话框
const showCreateDialog = () => {
  currentEmployee.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (employee) => {
  currentEmployee.value = { ...employee }
  isEdit.value = true
  dialogVisible.value = true
}

// 表单成功处理
const handleFormSuccess = () => {
  dialogVisible.value = false
  loadEmployees()
}

// 显示批量导入对话框
const showBatchImportDialog = () => {
  batchImportVisible.value = true
}

// 批量导入成功处理
const handleBatchImportSuccess = () => {
  batchImportVisible.value = false
  loadEmployees()
}



// 删除员工
const deleteEmployee = async (employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工 ${employee.name} 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await request.delete(`/employees/${employee.id}`)
    ElMessage.success('删除成功')
    loadEmployees()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除员工失败:', error)
    }
  }
}

onMounted(() => {
  loadDepartments()
  loadEmployees()
})
</script>

<style scoped>
.employee-management {
  max-width: 1400px;
  margin: 0 auto;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

h3 {
  color: var(--text-primary);
  margin: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
}
</style>
